@echo off
echo 🚀 Installing Custom Laravel E-commerce Packages...
echo.

REM Check if we're in a Laravel project
if not exist "artisan" (
    echo ❌ Error: This doesn't appear to be a Laravel project directory.
    echo Please run this script from your Laravel project root.
    pause
    exit /b 1
)

echo ✅ Laravel project detected!
echo.

echo 📦 Installing Core E-commerce Packages...
echo.

REM Core E-commerce packages
echo Installing Image Processing...
call composer require intervention/image:^2.4

echo Installing PDF Generation...
call composer require barryvdh/laravel-dompdf:^2.0.0

echo Installing Excel Import/Export...
call composer require maatwebsite/excel:^3.1.46

echo Installing Authentication...
call composer require laravel/sanctum:^4.0
call composer require laravel/ui:^4.0

echo Installing Nested Categories...
call composer require kalnoy/nestedset:^6.0

echo Installing Repository Pattern...
call composer require prettus/l5-repository:^2.6

echo Installing Breadcrumbs...
call composer require diglactic/laravel-breadcrumbs:^9.0

echo Installing HTTP Client...
call composer require guzzlehttp/guzzle:^7.0.1

echo Installing Caching...
call composer require spatie/laravel-responsecache:^7.4

echo Installing Sitemap...
call composer require spatie/laravel-sitemap:^7.3

echo.
echo 💳 Installing Payment Packages...
echo.

echo Installing PayPal...
call composer require paypal/paypal-checkout-sdk:1.0.1

echo.
echo 🔧 Installing Optional Packages...
echo.

echo Installing Social Login...
call composer require laravel/socialite:^5.0

echo Installing Visitor Tracking...
call composer require shetabit/visitor:^4.1

echo Installing Redis...
call composer require predis/predis:^2.2

echo.
echo 🛠️ Installing Development Packages...
echo.

echo Installing Debug Bar...
call composer require barryvdh/laravel-debugbar:^3.8 --dev

echo Installing Testing Framework...
call composer require pestphp/pest:^3.0 --dev
call composer require pestphp/pest-plugin-laravel:^3.0 --dev

echo Installing Code Quality Tools...
call composer require laravel/pint:^1.19 --dev

echo.
echo ✅ Composer packages installed successfully!
echo.

echo 🎨 Installing Frontend Packages...
echo.

REM Check if package.json exists
if not exist "package.json" (
    echo Creating package.json...
    call npm init -y
)

echo Installing Vue.js 3...
call npm install vue@next @vitejs/plugin-vue

echo Installing UI Components...
call npm install @headlessui/vue @heroicons/vue

echo Installing Form Validation...
call npm install @vuelidate/core @vuelidate/validators

echo Installing HTTP Client...
call npm install axios

echo Installing Utilities...
call npm install lodash moment

echo Installing TailwindCSS...
call npm install --save-dev tailwindcss postcss autoprefixer
call npm install --save-dev @tailwindcss/forms @tailwindcss/typography

echo.
echo 🎨 Initializing TailwindCSS...
call npx tailwindcss init -p

echo.
echo ✅ Frontend packages installed successfully!
echo.

echo 📋 Publishing Configuration Files...
echo.

REM Publish config files
call php artisan vendor:publish --provider="Intervention\Image\ImageServiceProviderLaravelRecent" --quiet
call php artisan vendor:publish --provider="Barryvdh\DomPDF\ServiceProvider" --quiet
call php artisan vendor:publish --provider="Maatwebsite\Excel\ExcelServiceProvider" --quiet
call php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider" --quiet
call php artisan vendor:publish --provider="Spatie\ResponseCache\ResponseCacheServiceProvider" --quiet
call php artisan vendor:publish --provider="Spatie\Sitemap\SitemapServiceProvider" --quiet

echo Creating storage link...
call php artisan storage:link

echo.
echo 🧹 Clearing Caches...
call php artisan config:clear
call php artisan cache:clear
call php artisan view:clear
call php artisan route:clear

echo.
echo 🎉 Installation Complete!
echo.
echo 📝 Next Steps:
echo 1. Update your .env file with database credentials
echo 2. Create your database: custom_ecommerce
echo 3. Run: php artisan migrate
echo 4. Run: npm run dev
echo 5. Start development: php artisan serve
echo.
echo 📚 Documentation:
echo - Package Installation Guide: PACKAGE_INSTALLATION_GUIDE.md
echo - Custom Laravel Structure: CUSTOM_LARAVEL_STRUCTURE.md
echo - Project Documentation: PROJECT_DOCUMENTATION.md
echo.

pause
