{"__meta": {"id": "01K2E8JXR1SYEED5037VMY37NX", "datetime": "2025-08-12 09:58:31", "utime": **********.364169, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/api/categories/tree", "ip": "::1"}, "modules": {"count": 2, "modules": [{"name": "Webkul\\Category", "models": ["Webkul\\Category\\Models\\Category (3)", "Webkul\\Category\\Models\\CategoryTranslation (3)"], "views": [], "queries": [{"sql": "select `_lft`, `_rgt` from `categories` where `id` = 1 limit 1", "duration": 1.12, "duration_str": "1.12s", "connection": "Online_store"}, {"sql": "select * from `categories` where `status` = 1 and (`categories`.`_lft` between 1 and 6) order by `position` asc", "duration": 12.42, "duration_str": "12.42s", "connection": "Online_store"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (1, 2, 3)", "duration": 2.17, "duration_str": "2.17s", "connection": "Online_store"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (2)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "duration": 9.21, "duration_str": "9.21s", "connection": "Online_store"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.14, "duration_str": "1.14s", "connection": "Online_store"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 1.34, "duration_str": "1.34s", "connection": "Online_store"}, {"sql": "select * from `locales` where `locales`.`id` = 1 limit 1", "duration": 9.02, "duration_str": "9.02s", "connection": "Online_store"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754972909.564579, "end": **********.428738, "duration": 1.864159107208252, "duration_str": "1.86s", "measures": [{"label": "Booting", "start": 1754972909.564579, "relative_start": 0, "end": **********.890359, "relative_end": **********.890359, "duration": 1.***************, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.890415, "relative_start": 1.***************, "end": **********.428743, "relative_end": 4.76837158203125e-06, "duration": 0.****************, "duration_str": "538ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.005275, "relative_start": 1.****************, "end": **********.02248, "relative_end": **********.02248, "duration": 0.017204999923706055, "duration_str": "17.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.306354, "relative_start": 1.****************, "end": **********.356642, "relative_end": **********.356642, "duration": 0.*****************, "duration_str": "50.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03642, "accumulated_duration_str": "36.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "type": "query", "params": [], "bindings": ["localhost", "http://localhost", "https://localhost"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.191253, "duration": 0.009210000000000001, "duration_str": "9.21ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 25.288}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.2193158, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 25.288, "width_percent": 3.13}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.2310412, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 28.418, "width_percent": 3.679}, {"sql": "select `_lft`, `_rgt` from `categories` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 235}, {"index": 18, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 227}, {"index": 19, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}], "start": **********.2547219, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "QueryBuilder.php:38", "source": {"index": 14, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fkalnoy%2Fnestedset%2Fsrc%2FQueryBuilder.php&line=38", "ajax": false, "filename": "QueryBuilder.php", "line": "38"}, "connection": "Online_store", "explain": null, "start_percent": 32.098, "width_percent": 3.075}, {"sql": "select * from `categories` where `status` = 1 and (`categories`.`_lft` between 1 and 6) order by `position` asc", "type": "query", "params": [], "bindings": [1, 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, {"index": 16, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 321}, {"index": 17, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 183}, {"index": 18, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CategoryController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CategoryController.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2644641, "duration": 0.01242, "duration_str": "12.42ms", "memory": 0, "memory_str": null, "filename": "QueryBuilder.php:305", "source": {"index": 15, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fkalnoy%2Fnestedset%2Fsrc%2FQueryBuilder.php&line=305", "ajax": false, "filename": "QueryBuilder.php", "line": "305"}, "connection": "Online_store", "explain": null, "start_percent": 35.173, "width_percent": 34.102}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 321}, {"index": 22, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 183}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CategoryController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CategoryController.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.289152, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "QueryBuilder.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fkalnoy%2Fnestedset%2Fsrc%2FQueryBuilder.php&line=305", "ajax": false, "filename": "QueryBuilder.php", "line": "305"}, "connection": "Online_store", "explain": null, "start_percent": 69.275, "width_percent": 5.958}, {"sql": "select * from `locales` where `locales`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 213}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Models/Category.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Category\\src\\Models\\Category.php", "line": 140}, {"index": 24, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 195}, {"index": 25, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}], "start": **********.3235848, "duration": 0.00902, "duration_str": "9.02ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 75.233, "width_percent": 24.767}]}, "models": {"data": {"Webkul\\Category\\Models\\Category": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Webkul\\Category\\Models\\CategoryTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategoryTranslation.php&line=1", "ajax": false, "filename": "CategoryTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/api/categories/tree", "action_name": "shop.api.categories.tree", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\CategoryController@tree", "uri": "GET api/categories/tree", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\CategoryController@tree<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/categories", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/CategoryController.php:51-56</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "1.87s", "peak_memory": "36MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1151462321 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1151462321\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-495922665 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-495922665\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1613517691 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkpJUFRHUW5IZHROWEEraHJxVCt4YWc9PSIsInZhbHVlIjoiOWJMd29Da1M4cGZ3RGxqME5yeDMwV1JadE5iNVhQYjJzNTA2TEVqMU9ETDF6Q0Y3SlJGTVRPYTV0cDFSSzlRemFJUmFvZHRiOEwwYklWUktqUVF0eFpTLzdCdzM3cHJxb085THZyZHZzVkdzVHU5VXVIQkFOcjNBdVA0TnZwcnciLCJtYWMiOiJjZGEyODNmODA1ZDIyYzlhNzU4YzI0N2NmMjIzNzQ1NDUyNGU1NDQzNzZmZWY2YWQxNDZhNDM1YWY5MDA3M2IyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://localhost/onlinestore/bagisto-2.3/public/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"730 characters\">dark_mode=0; XSRF-TOKEN=eyJpdiI6IkpJUFRHUW5IZHROWEEraHJxVCt4YWc9PSIsInZhbHVlIjoiOWJMd29Da1M4cGZ3RGxqME5yeDMwV1JadE5iNVhQYjJzNTA2TEVqMU9ETDF6Q0Y3SlJGTVRPYTV0cDFSSzlRemFJUmFvZHRiOEwwYklWUktqUVF0eFpTLzdCdzM3cHJxb085THZyZHZzVkdzVHU5VXVIQkFOcjNBdVA0TnZwcnciLCJtYWMiOiJjZGEyODNmODA1ZDIyYzlhNzU4YzI0N2NmMjIzNzQ1NDUyNGU1NDQzNzZmZWY2YWQxNDZhNDM1YWY5MDA3M2IyIiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6InNOcldiYlZCbjRsZEJxcE1QekUyWEE9PSIsInZhbHVlIjoib3JBZ0NvNnhXMGZVTmFsT2RWQVVtUk8ySGtNNCs1ZlllS3M2Y2xrcDhuTEM5bUxkcEpkVjczYWMzUzh2UDBLV0RTOUMzbmZscmFwclZicHRqek9sUjI4OHIvR21pdUhSSGFjNHh6WFRTWS9BQkR4Qnd2TDNUK21QOUNJcDI4SUMiLCJtYWMiOiI4ODQxZjc1MDE1NmFjZTU5N2IxN2Q3NmRlMGQ1ZGE2NGI5NDIzOWNlYWY1MGIwNTNmNDkyMmRlMDMxYTNkOWY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613517691\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-376899093 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CMXJMs2xqNIQnFDf8eSo2lGnmAMshguwYrRLXOfi</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">72RLgi663pSk9p1EC9ybWYgWzI5PxdHCf8IG9BgM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376899093\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1542559956 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 04:28:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542559956\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-305060000 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CMXJMs2xqNIQnFDf8eSo2lGnmAMshguwYrRLXOfi</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://localhost/onlinestore/bagisto-2.3/public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jn8zqa9BVfrsHv5ks5rTzDUwZmT8GtC1VV4Zx01t</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305060000\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/api/categories/tree", "action_name": "shop.api.categories.tree", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\CategoryController@tree"}, "badge": null}}