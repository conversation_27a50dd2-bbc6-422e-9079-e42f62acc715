{"__meta": {"id": "01K2CZ2XA1Y25AF0DKGZD6VA58", "datetime": "2025-08-11 21:53:15", "utime": **********.010989, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/admin/dashboard/stats?type=today", "ip": "::1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Customer", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `customers` where `channel_id` in (1) and `created_at` between '2025-08-10 00:00:00' and '2025-08-10 23:59:59'", "duration": 5.28, "duration_str": "5.28s", "connection": "Online_store"}, {"sql": "select count(*) as aggregate from `customers` where `channel_id` in (1) and `created_at` between '2025-08-11 00:00:00' and '2025-08-11 23:59:59'", "duration": 5.28, "duration_str": "5.28s", "connection": "Online_store"}]}, {"name": "Webkul\\Sales", "models": [], "views": [], "queries": [{"sql": "select * from `orders` where `channel_id` in (1) and `orders`.`created_at` between '2025-08-11 00:00:00' and '2025-08-11 23:59:59'", "duration": 9.52, "duration_str": "9.52s", "connection": "Online_store"}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-08-10 00:00:00' and '2025-08-10 23:59:59'", "duration": 1.79, "duration_str": "1.79s", "connection": "Online_store"}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-08-11 00:00:00' and '2025-08-11 23:59:59'", "duration": 2.91, "duration_str": "2.91s", "connection": "Online_store"}, {"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-08-10 00:00:00' and '2025-08-10 23:59:59'", "duration": 1.29, "duration_str": "1.29s", "connection": "Online_store"}, {"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-08-11 00:00:00' and '2025-08-11 23:59:59'", "duration": 1.09, "duration_str": "1.09s", "connection": "Online_store"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 7.59, "duration_str": "7.59s", "connection": "Online_store"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.96, "duration_str": "960ms", "connection": "Online_store"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754929393.487706, "end": **********.062526, "duration": 1.5748200416564941, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1754929393.487706, "relative_start": 0, "end": **********.574205, "relative_end": **********.574205, "duration": 1.****************, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.574227, "relative_start": 1.****************, "end": **********.06253, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "488ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.619287, "relative_start": 1.****************, "end": **********.631213, "relative_end": **********.631213, "duration": 0.011925935745239258, "duration_str": "11.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.004856, "relative_start": 1.****************, "end": **********.005894, "relative_end": **********.005894, "duration": 0.0010378360748291016, "duration_str": "1.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 9, "nb_statements": 9, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03570999999999999, "accumulated_duration_str": "35.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.886448, "duration": 0.0075899999999999995, "duration_str": "7.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 21.255}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 109}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.904837, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "Online_store", "explain": null, "start_percent": 21.255, "width_percent": 2.688}, {"sql": "select * from `orders` where `channel_id` in (1) and `orders`.`created_at` between '2025-08-11 00:00:00' and '2025-08-11 23:59:59'", "type": "query", "params": [], "bindings": [1, "2025-08-11 00:00:00", "2025-08-11 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 121}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 49}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.9120429, "duration": 0.009519999999999999, "duration_str": "9.52ms", "memory": 0, "memory_str": null, "filename": "Sale.php:121", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=121", "ajax": false, "filename": "Sale.php", "line": "121"}, "connection": "Online_store", "explain": null, "start_percent": 23.943, "width_percent": 26.659}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-08-10 00:00:00' and '2025-08-10 23:59:59'", "type": "query", "params": [], "bindings": [1, "2025-08-10 00:00:00", "2025-08-10 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 156}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 70}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.929929, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "Sale.php:175", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=175", "ajax": false, "filename": "Sale.php", "line": "175"}, "connection": "Online_store", "explain": null, "start_percent": 50.602, "width_percent": 5.013}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-08-11 00:00:00' and '2025-08-11 23:59:59'", "type": "query", "params": [], "bindings": [1, "2025-08-11 00:00:00", "2025-08-11 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 157}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 70}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9386072, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "Sale.php:175", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=175", "ajax": false, "filename": "Sale.php", "line": "175"}, "connection": "Online_store", "explain": null, "start_percent": 55.615, "width_percent": 8.149}, {"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-08-10 00:00:00' and '2025-08-10 23:59:59'", "type": "query", "params": [], "bindings": [1, "2025-08-10 00:00:00", "2025-08-10 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 103}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 71}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9519188, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Sale.php:76", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=76", "ajax": false, "filename": "Sale.php", "line": "76"}, "connection": "Online_store", "explain": null, "start_percent": 63.764, "width_percent": 3.612}, {"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-08-11 00:00:00' and '2025-08-11 23:59:59'", "type": "query", "params": [], "bindings": [1, "2025-08-11 00:00:00", "2025-08-11 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 71}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.961129, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Sale.php:76", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=76", "ajax": false, "filename": "Sale.php", "line": "76"}, "connection": "Online_store", "explain": null, "start_percent": 67.376, "width_percent": 3.052}, {"sql": "select count(*) as aggregate from `customers` where `channel_id` in (1) and `created_at` between '2025-08-10 00:00:00' and '2025-08-10 23:59:59'", "type": "query", "params": [], "bindings": [1, "2025-08-10 00:00:00", "2025-08-10 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 84}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 66}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 72}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.969281, "duration": 0.00528, "duration_str": "5.28ms", "memory": 0, "memory_str": null, "filename": "Customer.php:84", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FCustomer.php&line=84", "ajax": false, "filename": "Customer.php", "line": "84"}, "connection": "Online_store", "explain": null, "start_percent": 70.428, "width_percent": 14.786}, {"sql": "select count(*) as aggregate from `customers` where `channel_id` in (1) and `created_at` between '2025-08-11 00:00:00' and '2025-08-11 23:59:59'", "type": "query", "params": [], "bindings": [1, "2025-08-11 00:00:00", "2025-08-11 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 84}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 67}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 72}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.981851, "duration": 0.00528, "duration_str": "5.28ms", "memory": 0, "memory_str": null, "filename": "Customer.php:84", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Customer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Customer.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FCustomer.php&line=84", "ajax": false, "filename": "Customer.php", "line": "84"}, "connection": "Online_store", "explain": null, "start_percent": 85.214, "width_percent": 14.786}]}, "models": {"data": {"Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/dashboard/stats?type=today", "action_name": "admin.dashboard.stats", "controller_action": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats", "uri": "GET admin/dashboard/stats", "controller": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=49\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/dashboard", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=49\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/DashboardController.php:49-57</a>", "middleware": "web, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "1.58s", "peak_memory": "36MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-856910236 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">today</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856910236\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1930727639 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1930727639\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1287788222 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImpFQUJmcnIrcjRVL09tSi8wVUk1aVE9PSIsInZhbHVlIjoiaDBnU3pBMDlNWDJzRjk3cEpMUjF6MWJCT2RIWHltc3Z5dlVIVG1UQlk0OXdNOHo5dmx3RUswZUluRGRoZkk4WFpMRlZkQjJUdkdnOFRMQkhYdDlSWGxnRFZ0ZW51eVdCVUxDRVRwb05VVkRiT1RYSVJrazRVcXA4Y2tRQUZVcUoiLCJtYWMiOiJkNTUyYjMwOTM2OGJjODRjNWM5NDJjMWYwNzU3NTZlNWUwOWNiMjI5M2E1NzgzMGY0ZDNmMzc1YTgxY2Y5OWYwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1090 characters\">bagisto_session=eyJpdiI6ImpDWlNrdnV1KzVRcHJsU1NOQ1RaRHc9PSIsInZhbHVlIjoiNit6V3FHZEdhMzRvcGk4WnFBZUlOK09WTVZxNm5NbElac2JoaE5hV1krZGFOeExFMVpLZVcyMVdJOUFkdk0rRVZZczB3bkF0R0hxRzVjdnMrUm1KU3pkNmlHeGJYT2JuNXVyblByaUg1Qlozc3BmaEFzMkZEZmx4VzBTT3lydnEiLCJtYWMiOiIyYzI4MGJhZTYyZDJkN2I1MDdjMGE4YjIxM2M1ZGJiOTFiYWY3OGYxNGU5ZGIyZTg4ODlkN2U5NTY4ZGIwYTg4IiwidGFnIjoiIn0%3D; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImpFQUJmcnIrcjRVL09tSi8wVUk1aVE9PSIsInZhbHVlIjoiaDBnU3pBMDlNWDJzRjk3cEpMUjF6MWJCT2RIWHltc3Z5dlVIVG1UQlk0OXdNOHo5dmx3RUswZUluRGRoZkk4WFpMRlZkQjJUdkdnOFRMQkhYdDlSWGxnRFZ0ZW51eVdCVUxDRVRwb05VVkRiT1RYSVJrazRVcXA4Y2tRQUZVcUoiLCJtYWMiOiJkNTUyYjMwOTM2OGJjODRjNWM5NDJjMWYwNzU3NTZlNWUwOWNiMjI5M2E1NzgzMGY0ZDNmMzc1YTgxY2Y5OWYwIiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6InZGU2RHMzhwNEF4TWs4TzU0WUsvM1E9PSIsInZhbHVlIjoia2NLc0FnZkRGa1RlaHJSdDVQMUgwRmZiMGQ0SEtSSHBkRTNkeDJMUmNZSGQ3bU9RTU92c0FXRUV0UmlOV0hvRnB5Z1VzdU1GUFN2b1V4QnNzZVhQaC9LMTZQaHdrb3YyVFBnSnlRM1JLMGk1aVE4UHRmYmlib1Q4WHFIR0xkZDQiLCJtYWMiOiJlNDFjZmNjNzgzYWQxN2E1ZTUyMWIxYjQyM2JlMDY4NGMxYjYwNGM0MTIxZjBlNjRmOTZjOWI5YmNlZjIyZWQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287788222\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-285578534 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bagisto_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TMRz3GXEM8sAcFV1fikDiVtK46lGff3YWRNL67O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285578534\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1093639935 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 16:23:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093639935\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-454259975 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-454259975\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/dashboard/stats?type=today", "action_name": "admin.dashboard.stats", "controller_action": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats"}, "badge": null}}