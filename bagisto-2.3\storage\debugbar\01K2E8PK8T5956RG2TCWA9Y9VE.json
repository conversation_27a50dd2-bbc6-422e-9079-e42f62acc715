{"__meta": {"id": "01K2E8PK8T5956RG2TCWA9Y9VE", "datetime": "2025-08-12 10:00:31", "utime": **********.708897, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/api/products?sort=name-desc&limit=12", "ip": "::1"}, "modules": {"count": 4, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (32)", "Webkul\\Attribute\\Models\\AttributeFamily (7)"], "views": [], "queries": [{"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('sort', 'limit', 'channel_id', 'status', 'visible_individually', 'url_key')", "duration": 9.03, "duration_str": "9.03s", "connection": "Online_store"}, {"sql": "select * from `attributes` where `code` = 'name'", "duration": 8.55, "duration_str": "8.55s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 5.94, "duration_str": "5.94s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 3.92, "duration_str": "3.92s", "connection": "Online_store"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 8.72, "duration_str": "8.72s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 1.09, "duration_str": "1.09s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 1.05, "duration_str": "1.05s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 1.37, "duration_str": "1.37s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 2.06, "duration_str": "2.06s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 2.82, "duration_str": "2.82s", "connection": "Online_store"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (1)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "duration": 10.3, "duration_str": "10.3s", "connection": "Online_store"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.56, "duration_str": "1.56s", "connection": "Online_store"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 3.77, "duration_str": "3.77s", "connection": "Online_store"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "duration": 2.9, "duration_str": "2.9s", "connection": "Online_store"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\Customer (1)", "Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customers` where `id` = 1 limit 1", "duration": 1.68, "duration_str": "1.68s", "connection": "Online_store"}, {"sql": "select * from `customer_groups` where `customer_groups`.`id` = 1 limit 1", "duration": 0.83, "duration_str": "830ms", "connection": "Online_store"}, {"sql": "select * from `wishlist_items` where `wishlist_items`.`customer_id` = 1 and `wishlist_items`.`customer_id` is not null", "duration": 1.05, "duration_str": "1.05s", "connection": "Online_store"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (20)", "Webkul\\Product\\Models\\ProductImage (17)", "Webkul\\Product\\Models\\ProductAttributeValue (376)", "Webkul\\Product\\Models\\ProductPriceIndex (45)", "Webkul\\Product\\Models\\ProductInventoryIndex (18)", "Webkul\\Product\\Models\\ProductBundleOption (4)", "Webkul\\Product\\Models\\ProductBundleOptionProduct (4)", "Webkul\\Product\\Models\\ProductGroupedProduct (3)"], "views": [], "queries": [{"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id` order by `sort_product_attribute_values`.`text_value` desc limit 12 offset 0", "duration": 30.2, "duration_str": "30.2s", "connection": "Online_store"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `position` asc", "duration": 1.18, "duration_str": "1.18s", "connection": "Online_store"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `position` asc", "duration": 1.16, "duration_str": "1.16s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "duration": 6.54, "duration_str": "6.54s", "connection": "Online_store"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "duration": 6.87, "duration_str": "6.87s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "duration": 1.03, "duration_str": "1.03s", "connection": "Online_store"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "duration": 0.93, "duration_str": "930ms", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`parent_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "duration": 3.44, "duration_str": "3.44s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (8, 9, 10, 11)", "duration": 10.12, "duration_str": "10.12s", "connection": "Online_store"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (8, 9, 10, 11)", "duration": 1.19, "duration_str": "1.19s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (8, 9, 10, 11)", "duration": 1.24, "duration_str": "1.24s", "connection": "Online_store"}, {"sql": "select * from `product_bundle_options` where `product_bundle_options`.`product_id` = 6 and `product_bundle_options`.`product_id` is not null", "duration": 10.52, "duration_str": "10.52s", "connection": "Online_store"}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 1 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "duration": 3.63, "duration_str": "3.63s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 1 limit 1", "duration": 1.43, "duration_str": "1.43s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 1 and `product_attribute_values`.`product_id` is not null", "duration": 1.36, "duration_str": "1.36s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 1 and `product_inventory_indices`.`product_id` is not null", "duration": 1.55, "duration_str": "1.55s", "connection": "Online_store"}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 2 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "duration": 1.21, "duration_str": "1.21s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 2 limit 1", "duration": 1.02, "duration_str": "1.02s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 2 and `product_attribute_values`.`product_id` is not null", "duration": 1.43, "duration_str": "1.43s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 2 and `product_inventory_indices`.`product_id` is not null", "duration": 1.07, "duration_str": "1.07s", "connection": "Online_store"}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 3 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "duration": 1.17, "duration_str": "1.17s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 3 limit 1", "duration": 0.94, "duration_str": "940ms", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 3 and `product_attribute_values`.`product_id` is not null", "duration": 1.44, "duration_str": "1.44s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 3 and `product_inventory_indices`.`product_id` is not null", "duration": 1.45, "duration_str": "1.45s", "connection": "Online_store"}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 4 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "duration": 1.2, "duration_str": "1.2s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 4 limit 1", "duration": 0.99, "duration_str": "990ms", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 4 and `product_attribute_values`.`product_id` is not null", "duration": 1.88, "duration_str": "1.88s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 4 and `product_inventory_indices`.`product_id` is not null", "duration": 1.14, "duration_str": "1.14s", "connection": "Online_store"}, {"sql": "select * from `product_grouped_products` where `product_grouped_products`.`product_id` = 5 and `product_grouped_products`.`product_id` is not null", "duration": 8.2, "duration_str": "8.2s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 1 limit 1", "duration": 2.6, "duration_str": "2.6s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 1 and `product_attribute_values`.`product_id` is not null", "duration": 6.69, "duration_str": "6.69s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 1 and `product_inventory_indices`.`product_id` is not null", "duration": 2.59, "duration_str": "2.59s", "connection": "Online_store"}]}]}, "messages": {"count": 22, "messages": [{"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.860873, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.861279, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.861649, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.861994, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.862355, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.862718, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.863035, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.863376, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.863713, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.864047, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.864423, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:30] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.974507, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:31] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.0297, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:31] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.065818, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:31] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.107839, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:31] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.14675, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:31] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.186233, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:31] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.223707, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:31] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.461209, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:31] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.564816, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:31] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.626339, "xdebug_link": null, "collector": "log"}, {"message": "[10:00:31] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.691298, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754973029.015877, "end": **********.758076, "duration": 2.742198944091797, "duration_str": "2.74s", "measures": [{"label": "Booting", "start": 1754973029.015877, "relative_start": 0, "end": **********.308488, "relative_end": **********.308488, "duration": 1.****************, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.308511, "relative_start": 1.****************, "end": **********.75808, "relative_end": 4.0531158447265625e-06, "duration": 1.****************, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.355056, "relative_start": 1.****************, "end": **********.370374, "relative_end": **********.370374, "duration": 0.015317916870117188, "duration_str": "15.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.864929, "relative_start": 1.****************, "end": **********.700379, "relative_end": **********.700379, "duration": 0.****************, "duration_str": "835ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::products.prices.index", "start": **********.973594, "relative_start": 1.***************, "end": **********.973594, "relative_end": **********.973594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.028976, "relative_start": 2.***************, "end": **********.028976, "relative_end": **********.028976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.065092, "relative_start": 2.049215078353882, "end": **********.065092, "relative_end": **********.065092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.107004, "relative_start": 2.0911269187927246, "end": **********.107004, "relative_end": **********.107004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.configurable", "start": **********.142573, "relative_start": 2.1266961097717285, "end": **********.142573, "relative_end": **********.142573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.185569, "relative_start": 2.169692039489746, "end": **********.185569, "relative_end": **********.185569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.222995, "relative_start": 2.207118034362793, "end": **********.222995, "relative_end": **********.222995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.bundle", "start": **********.459657, "relative_start": 2.443779945373535, "end": **********.459657, "relative_end": **********.459657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.grouped", "start": **********.563772, "relative_start": 2.5478949546813965, "end": **********.563772, "relative_end": **********.563772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.62324, "relative_start": 2.607362985610962, "end": **********.62324, "relative_end": **********.62324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.69062, "relative_start": 2.6747429370880127, "end": **********.69062, "relative_end": **********.69062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 43273656, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 11, "nb_templates": 11, "templates": [{"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.973456, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.028813, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.064944, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.106848, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.configurable", "param_count": null, "params": [], "start": **********.142428, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/configurable.blade.phpshop::products.prices.configurable", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.185442, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.222821, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.bundle", "param_count": null, "params": [], "start": **********.459409, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/bundle.blade.phpshop::products.prices.bundle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fbundle.blade.php&line=1", "ajax": false, "filename": "bundle.blade.php", "line": "?"}}, {"name": "shop::products.prices.grouped", "param_count": null, "params": [], "start": **********.563632, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/grouped.blade.phpshop::products.prices.grouped", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fgrouped.blade.php&line=1", "ajax": false, "filename": "grouped.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.623086, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.690484, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 50, "nb_statements": 50, "nb_visible_statements": 50, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.2681399999999998, "accumulated_duration_str": "268ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "type": "query", "params": [], "bindings": ["localhost", "http://localhost", "https://localhost"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.4474812, "duration": 0.0103, "duration_str": "10.3ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 3.841}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.475061, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 3.841, "width_percent": 0.582}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.4843018, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 4.423, "width_percent": 1.406}, {"sql": "select * from `customers` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 39}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 250}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 249}], "start": **********.553745, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "Online_store", "explain": null, "start_percent": 5.829, "width_percent": 0.627}, {"sql": "select * from `customer_groups` where `customer_groups`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 250}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 249}, {"index": 29, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}, {"index": 30, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 485}], "start": **********.564992, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "CustomerRepository.php:41", "source": {"index": 22, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FRepositories%2FCustomerRepository.php&line=41", "ajax": false, "filename": "CustomerRepository.php", "line": "41"}, "connection": "Online_store", "explain": null, "start_percent": 6.456, "width_percent": 0.31}, {"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('sort', 'limit', 'channel_id', 'status', 'visible_individually', 'url_key')", "type": "query", "params": [], "bindings": ["sort", "limit", "channel_id", "status", "visible_individually", "url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 198}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 293}, {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 485}], "start": **********.570257, "duration": 0.00903, "duration_str": "9.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "Online_store", "explain": null, "start_percent": 6.765, "width_percent": 3.368}, {"sql": "select * from `attributes` where `code` = 'name'", "type": "query", "params": [], "bindings": ["name"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 397}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}], "start": **********.5881262, "duration": 0.00855, "duration_str": "8.55ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "Online_store", "explain": null, "start_percent": 10.133, "width_percent": 3.189}, {"sql": "select count(*) as aggregate from (select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": [1, 2, "en", "1", 3, 7, 1, 8, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.603865, "duration": 0.08409, "duration_str": "84.09ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 13.321, "width_percent": 31.36}, {"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id` order by `sort_product_attribute_values`.`text_value` desc limit 12 offset 0", "type": "query", "params": [], "bindings": [1, 2, "en", "1", 3, 7, 1, 8, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.6935909, "duration": 0.030199999999999998, "duration_str": "30.2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 44.682, "width_percent": 11.263}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.730171, "duration": 0.00594, "duration_str": "5.94ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 55.945, "width_percent": 2.215}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.7447011, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 58.16, "width_percent": 0.44}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.7527862, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 58.6, "width_percent": 0.433}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.760591, "duration": 0.00654, "duration_str": "6.54ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 59.033, "width_percent": 2.439}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.779984, "duration": 0.00687, "duration_str": "6.87ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 61.472, "width_percent": 2.562}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.793678, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 64.034, "width_percent": 0.384}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.800299, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 64.418, "width_percent": 0.347}, {"sql": "select * from `products` where `products`.`parent_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.806669, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 64.765, "width_percent": 1.283}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.814632, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 66.048, "width_percent": 1.462}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.8227232, "duration": 0.010119999999999999, "duration_str": "10.12ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 67.51, "width_percent": 3.774}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.840159, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 71.284, "width_percent": 0.444}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.846086, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 71.727, "width_percent": 0.462}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 213}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}], "start": **********.884385, "duration": 0.00872, "duration_str": "8.72ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "Online_store", "explain": null, "start_percent": 72.19, "width_percent": 3.252}, {"sql": "select * from `wishlist_items` where `wishlist_items`.`customer_id` = 1 and `wishlist_items`.`customer_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 45}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 134}], "start": **********.95045, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ProductResource.php:45", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=45", "ajax": false, "filename": "ProductResource.php", "line": "45"}, "connection": "Online_store", "explain": null, "start_percent": 75.442, "width_percent": 0.392}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 455}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 477}], "start": **********.9591012, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 75.834, "width_percent": 1.082}, {"sql": "select * from `product_bundle_options` where `product_bundle_options`.`product_id` = 6 and `product_bundle_options`.`product_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 528}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.261202, "duration": 0.01052, "duration_str": "10.52ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 76.915, "width_percent": 3.923}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 1 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 530}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.278907, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 80.838, "width_percent": 1.354}, {"sql": "select * from `products` where `products`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.288588, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Bundle.php:532", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FBundle.php&line=532", "ajax": false, "filename": "Bundle.php", "line": "532"}, "connection": "Online_store", "explain": null, "start_percent": 82.192, "width_percent": 0.533}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.29867, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 82.725, "width_percent": 0.407}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 1 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.306899, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 83.132, "width_percent": 0.507}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 1 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.315383, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 83.639, "width_percent": 0.578}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 2 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 530}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.32352, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 84.217, "width_percent": 0.451}, {"sql": "select * from `products` where `products`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.3307161, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Bundle.php:532", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FBundle.php&line=532", "ajax": false, "filename": "Bundle.php", "line": "532"}, "connection": "Online_store", "explain": null, "start_percent": 84.668, "width_percent": 0.38}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.340088, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 85.049, "width_percent": 0.392}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 2 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.349991, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 85.44, "width_percent": 0.533}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 2 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.358164, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 85.974, "width_percent": 0.399}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 3 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 530}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.365214, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 86.373, "width_percent": 0.436}, {"sql": "select * from `products` where `products`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.3721118, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Bundle.php:532", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FBundle.php&line=532", "ajax": false, "filename": "Bundle.php", "line": "532"}, "connection": "Online_store", "explain": null, "start_percent": 86.809, "width_percent": 0.351}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.379421, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 87.16, "width_percent": 0.511}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 3 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.388471, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 87.671, "width_percent": 0.537}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 3 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.39616, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 88.208, "width_percent": 0.541}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 4 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 530}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.403457, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 88.748, "width_percent": 0.448}, {"sql": "select * from `products` where `products`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.409192, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Bundle.php:532", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FBundle.php&line=532", "ajax": false, "filename": "Bundle.php", "line": "532"}, "connection": "Online_store", "explain": null, "start_percent": 89.196, "width_percent": 0.369}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.4174352, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 89.565, "width_percent": 0.768}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 4 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.426839, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 90.333, "width_percent": 0.701}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 4 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.4372628, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 91.035, "width_percent": 0.425}, {"sql": "select * from `product_grouped_products` where `product_grouped_products`.`product_id` = 5 and `product_grouped_products`.`product_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Grouped.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Grouped.php", "line": 151}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.4911501, "duration": 0.008199999999999999, "duration_str": "8.2ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 91.46, "width_percent": 3.058}, {"sql": "select * from `products` where `products`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Grouped.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Grouped.php", "line": 152}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.504507, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "Grouped.php:152", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Grouped.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Grouped.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FGrouped.php&line=152", "ajax": false, "filename": "Grouped.php", "line": "152"}, "connection": "Online_store", "explain": null, "start_percent": 94.518, "width_percent": 0.97}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.513946, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 95.487, "width_percent": 1.052}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 1 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.523181, "duration": 0.006690000000000001, "duration_str": "6.69ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 96.539, "width_percent": 2.495}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 1 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 144}], "start": **********.538716, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 99.034, "width_percent": 0.966}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 376, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 32, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductBundleOption": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductBundleOption.php&line=1", "ajax": false, "filename": "ProductBundleOption.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductBundleOptionProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductBundleOptionProduct.php&line=1", "ajax": false, "filename": "ProductBundleOptionProduct.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductGroupedProduct": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductGroupedProduct.php&line=1", "ajax": false, "filename": "ProductGroupedProduct.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Customer\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}}, "count": 531, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/api/products?limit=12&sort=name-desc", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index", "uri": "GET api/products", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php:26-56</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "2.78s", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1218798281 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"9 characters\">name-desc</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218798281\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1091633773 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1091633773\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1772586807 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjE5SzlVVzZLSnVVRCtoTE5DZTY2WHc9PSIsInZhbHVlIjoiV1dXTXpscFM3YVROQVZtVy9pTDByMVdjWjh0ZFlGaUZZNkJHVHNEYnozTk5KVktXbTRzckh1YkhseU11L1dSaUtPZk41Z29WdDhpNWxrME5EK0t5WmtyYkdZUlh4TXg2MGRvUEFPck5ZZUpXQ0xJMFRLa1FWclNYenYvSXZQTE8iLCJtYWMiOiJjOWQzMDBjMDFkNzM4MWNmODk3MmRhODJlZWY2NzkxZWU5OTY1NWRmY2EyYjhlYTA2ODU0NmRiODk4ZTdkNWViIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://localhost/onlinestore/bagisto-2.3/public/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"730 characters\">dark_mode=0; XSRF-TOKEN=eyJpdiI6IjE5SzlVVzZLSnVVRCtoTE5DZTY2WHc9PSIsInZhbHVlIjoiV1dXTXpscFM3YVROQVZtVy9pTDByMVdjWjh0ZFlGaUZZNkJHVHNEYnozTk5KVktXbTRzckh1YkhseU11L1dSaUtPZk41Z29WdDhpNWxrME5EK0t5WmtyYkdZUlh4TXg2MGRvUEFPck5ZZUpXQ0xJMFRLa1FWclNYenYvSXZQTE8iLCJtYWMiOiJjOWQzMDBjMDFkNzM4MWNmODk3MmRhODJlZWY2NzkxZWU5OTY1NWRmY2EyYjhlYTA2ODU0NmRiODk4ZTdkNWViIiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6ImhKa1UzTFdlcDlSZTNwNXZZcTNvUXc9PSIsInZhbHVlIjoiZWRQczhOZGQ5bjZNR1lsNHFXNnJxMHlWSDNJbVNsamRuQktmTXRZOExJYmw2Uis3ZGZPMkVqZ2IzMWd3M1FBc1ZIbFByMUxlREtOaDZZMEdUQkdBelpXT1poTmhVdlJUWm8zVnB4c3VkRVJXVWtDWU1rWHZnR1h1WVNiN0trdDYiLCJtYWMiOiIyMTg3MjU0ZjEwOTdhMzBmM2Y0N2E1YTNjNTQyNjM4NmVkMzc0Y2FjOThiNWY0NGQ1Y2VmYjNmZWZjZThhOGQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772586807\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-897195710 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CMXJMs2xqNIQnFDf8eSo2lGnmAMshguwYrRLXOfi</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mC2K4FgBCXewVV70GHvCq5ifLs4pArYA6Ff0JPb1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-897195710\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1676992839 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 04:30:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676992839\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-928579552 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CMXJMs2xqNIQnFDf8eSo2lGnmAMshguwYrRLXOfi</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost/onlinestore/bagisto-2.3/public/customer/account/downloadable-products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jn8zqa9BVfrsHv5ks5rTzDUwZmT8GtC1VV4Zx01t</span>\"\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928579552\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/api/products?limit=12&sort=name-desc", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index"}, "badge": null}}