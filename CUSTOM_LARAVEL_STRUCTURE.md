# Custom Laravel E-commerce Structure

## 🏗️ Replacing Bagisto with Custom Laravel Implementation

### Directory Structure Comparison

#### Bagisto Structure (Current)
```
packages/Webkul/
├── Admin/src/Http/Controllers/
├── Customer/src/Repositories/
├── Product/src/Models/
├── Sales/src/Models/
└── Checkout/src/Facades/
```

#### Custom Laravel Structure (Proposed)
```
app/
├── Http/Controllers/
│   ├── Admin/
│   │   ├── DashboardController.php
│   │   ├── ProductController.php
│   │   ├── OrderController.php
│   │   ├── CustomerController.php
│   │   └── Customers/
│   │       └── CartController.php
│   ├── Shop/
│   │   ├── ProductController.php
│   │   ├── CartController.php
│   │   ├── CheckoutController.php
│   │   └── CustomerController.php
│   └── Api/
│       ├── ProductController.php
│       └── OrderController.php
├── Models/
│   ├── Product.php
│   ├── Category.php
│   ├── Customer.php
│   ├── Order.php
│   ├── OrderItem.php
│   ├── Cart.php
│   ├── CartItem.php
│   ├── Payment.php
│   └── Shipping.php
├── Services/
│   ├── CartService.php
│   ├── OrderService.php
│   ├── PaymentService.php
│   ├── ShippingService.php
│   └── ProductService.php
├── Repositories/
│   ├── ProductRepository.php
│   ├── CustomerRepository.php
│   ├── OrderRepository.php
│   └── CartRepository.php
├── Resources/
│   ├── ProductResource.php
│   ├── OrderResource.php
│   └── CartResource.php
└── Traits/
    ├── HasInventory.php
    ├── HasPricing.php
    └── HasAttributes.php
```

## 🔄 Migration Examples

### 1. Cart Controller Migration

#### Original Bagisto Controller
```php
<?php

namespace Webkul\Admin\Http\Controllers\Customers\Customer;

use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Checkout\Facades\Cart;
use Webkul\Customer\Repositories\CustomerRepository;

class CartController extends Controller
{
    public function __construct(
        protected CustomerRepository $customerRepository,
        protected CartItemRepository $cartItemRepository
    ) {}
}
```

#### Custom Laravel Controller
```php
<?php

namespace App\Http\Controllers\Admin\Customers;

use App\Http\Controllers\Controller;
use App\Services\CartService;
use App\Repositories\CustomerRepository;
use App\Models\Customer;
use Illuminate\Http\Request;

class CartController extends Controller
{
    public function __construct(
        protected CustomerRepository $customerRepository,
        protected CartService $cartService
    ) {}

    public function store(Request $request, Customer $customer)
    {
        try {
            $cart = $this->cartService->createCart([
                'customer_id' => $customer->id,
                'channel_id' => config('app.default_channel_id', 1),
                'currency_code' => config('app.currency', 'USD'),
            ]);

            return response()->json([
                'success' => true,
                'cart' => $cart,
                'message' => 'Cart created successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
```

### 2. Models Migration

#### Product Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Traits\HasInventory;
use App\Traits\HasPricing;

class Product extends Model
{
    use HasInventory, HasPricing;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'sku',
        'type',
        'status',
        'price',
        'special_price',
        'special_price_from',
        'special_price_to',
        'weight',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'status' => 'boolean',
        'price' => 'decimal:2',
        'special_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'special_price_from' => 'date',
        'special_price_to' => 'date',
    ];

    // Relationships
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'product_categories');
    }

    public function images(): HasMany
    {
        return $this->hasMany(ProductImage::class);
    }

    public function inventories(): HasMany
    {
        return $this->hasMany(ProductInventory::class);
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    public function scopeInStock($query)
    {
        return $query->whereHas('inventories', function ($q) {
            $q->where('quantity', '>', 0);
        });
    }
}
```

#### Cart Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cart extends Model
{
    protected $fillable = [
        'customer_id',
        'customer_email',
        'customer_first_name',
        'customer_last_name',
        'shipping_method',
        'coupon_code',
        'is_guest',
        'items_count',
        'items_qty',
        'exchange_rate',
        'global_currency_code',
        'base_currency_code',
        'channel_currency_code',
        'cart_currency_code',
        'grand_total',
        'base_grand_total',
        'sub_total',
        'base_sub_total',
        'tax_total',
        'base_tax_total',
        'discount_amount',
        'base_discount_amount',
        'checkout_method',
        'is_guest',
        'is_active',
    ];

    protected $casts = [
        'is_guest' => 'boolean',
        'is_active' => 'boolean',
        'items_count' => 'integer',
        'items_qty' => 'integer',
        'exchange_rate' => 'decimal:4',
        'grand_total' => 'decimal:2',
        'base_grand_total' => 'decimal:2',
        'sub_total' => 'decimal:2',
        'base_sub_total' => 'decimal:2',
        'tax_total' => 'decimal:2',
        'base_tax_total' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'base_discount_amount' => 'decimal:2',
    ];

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    // Methods
    public function addItem(Product $product, int $quantity = 1, array $options = []): CartItem
    {
        $cartItem = $this->items()->where('product_id', $product->id)->first();

        if ($cartItem) {
            $cartItem->update(['quantity' => $cartItem->quantity + $quantity]);
        } else {
            $cartItem = $this->items()->create([
                'product_id' => $product->id,
                'quantity' => $quantity,
                'price' => $product->price,
                'total' => $product->price * $quantity,
                'product_options' => $options,
            ]);
        }

        $this->updateTotals();
        return $cartItem;
    }

    public function updateTotals(): void
    {
        $this->items_count = $this->items()->count();
        $this->items_qty = $this->items()->sum('quantity');
        $this->sub_total = $this->items()->sum('total');
        $this->grand_total = $this->sub_total + $this->tax_total - $this->discount_amount;
        $this->save();
    }
}
```

### 3. Services Migration

#### Cart Service
```php
<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\Product;
use App\Models\Customer;
use App\Repositories\CartRepository;
use Illuminate\Support\Facades\Session;

class CartService
{
    public function __construct(
        protected CartRepository $cartRepository
    ) {}

    public function createCart(array $data): Cart
    {
        return $this->cartRepository->create(array_merge($data, [
            'is_active' => true,
            'created_at' => now(),
        ]));
    }

    public function getActiveCart(?Customer $customer = null): ?Cart
    {
        if ($customer) {
            return $this->cartRepository->findActiveByCustomer($customer->id);
        }

        $cartId = Session::get('cart_id');
        return $cartId ? $this->cartRepository->find($cartId) : null;
    }

    public function addToCart(Product $product, int $quantity = 1, array $options = []): Cart
    {
        $cart = $this->getActiveCart() ?? $this->createGuestCart();
        
        $cart->addItem($product, $quantity, $options);
        
        Session::put('cart_id', $cart->id);
        
        return $cart;
    }

    public function removeFromCart(int $cartItemId): bool
    {
        $cart = $this->getActiveCart();
        
        if (!$cart) {
            return false;
        }

        $item = $cart->items()->find($cartItemId);
        
        if ($item) {
            $item->delete();
            $cart->updateTotals();
            return true;
        }

        return false;
    }

    public function updateCartItem(int $cartItemId, int $quantity): bool
    {
        $cart = $this->getActiveCart();
        
        if (!$cart) {
            return false;
        }

        $item = $cart->items()->find($cartItemId);
        
        if ($item) {
            $item->update([
                'quantity' => $quantity,
                'total' => $item->price * $quantity,
            ]);
            $cart->updateTotals();
            return true;
        }

        return false;
    }

    public function clearCart(): bool
    {
        $cart = $this->getActiveCart();
        
        if ($cart) {
            $cart->items()->delete();
            $cart->updateTotals();
            Session::forget('cart_id');
            return true;
        }

        return false;
    }

    protected function createGuestCart(): Cart
    {
        return $this->createCart([
            'customer_id' => null,
            'is_guest' => true,
            'channel_id' => config('app.default_channel_id', 1),
            'currency_code' => config('app.currency', 'USD'),
        ]);
    }
}
```

### 4. Repository Pattern
```php
<?php

namespace App\Repositories;

use App\Models\Cart;
use Illuminate\Database\Eloquent\Collection;

class CartRepository
{
    public function create(array $data): Cart
    {
        return Cart::create($data);
    }

    public function find(int $id): ?Cart
    {
        return Cart::find($id);
    }

    public function findActiveByCustomer(int $customerId): ?Cart
    {
        return Cart::where('customer_id', $customerId)
                   ->where('is_active', true)
                   ->first();
    }

    public function update(Cart $cart, array $data): bool
    {
        return $cart->update($data);
    }

    public function delete(Cart $cart): bool
    {
        return $cart->delete();
    }

    public function getActiveCartsCount(): int
    {
        return Cart::where('is_active', true)->count();
    }
}
```

## 🚀 Migration Steps

### Step 1: Create New Laravel Project
```bash
composer create-project laravel/laravel custom-ecommerce
cd custom-ecommerce
```

### Step 2: Install Required Packages
```bash
composer require intervention/image
composer require barryvdh/laravel-dompdf
composer require maatwebsite/excel
npm install vue@next @vitejs/plugin-vue
```

### Step 3: Create Database Structure
```bash
php artisan make:migration create_products_table
php artisan make:migration create_categories_table
php artisan make:migration create_carts_table
php artisan make:migration create_cart_items_table
php artisan make:migration create_orders_table
php artisan make:migration create_customers_table
```

### Step 4: Generate Models and Controllers
```bash
php artisan make:model Product -mcr
php artisan make:model Cart -mcr
php artisan make:model Order -mcr
php artisan make:controller Admin/Customers/CartController
```

### Step 5: Create Services and Repositories
```bash
php artisan make:class Services/CartService
php artisan make:class Repositories/CartRepository
```

## ✅ Benefits of Custom Laravel Implementation

1. **Full Control:** Complete control over codebase
2. **Standard Laravel:** Uses Laravel conventions
3. **Lighter Weight:** No unnecessary packages
4. **Easier Maintenance:** Standard Laravel structure
5. **Better Performance:** Optimized for your needs
6. **Custom Features:** Build exactly what you need
7. **Team Familiarity:** Standard Laravel patterns

## 🔄 Migration Timeline

- **Phase 1:** Core models and database (1-2 weeks)
- **Phase 2:** Basic CRUD operations (1 week)
- **Phase 3:** Cart and checkout functionality (2 weeks)
- **Phase 4:** Admin panel (2-3 weeks)
- **Phase 5:** Frontend shop (2-3 weeks)
- **Phase 6:** Payment integration (1 week)
- **Phase 7:** Testing and optimization (1-2 weeks)

**Total Estimated Time:** 10-14 weeks for full migration

Would you like me to help you start implementing any specific part of this custom Laravel e-commerce solution?
