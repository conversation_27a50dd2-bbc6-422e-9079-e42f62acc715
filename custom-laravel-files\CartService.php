<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Product;
use App\Models\Customer;
use App\Repositories\CartRepository;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;

class CartService
{
    public function __construct(
        protected CartRepository $cartRepository
    ) {}

    /**
     * Get or create active cart for current user/session
     */
    public function getActiveCart(): Cart
    {
        $cart = $this->findActiveCart();
        
        if (!$cart) {
            $cart = $this->createCart();
        }

        return $cart;
    }

    /**
     * Find active cart
     */
    protected function findActiveCart(): ?Cart
    {
        if (Auth::guard('customer')->check()) {
            return $this->cartRepository->findActiveByCustomer(Auth::guard('customer')->id());
        }

        $cartId = Session::get('cart_id');
        return $cartId ? $this->cartRepository->findActive($cartId) : null;
    }

    /**
     * Create new cart
     */
    public function createCart(array $data = []): Cart
    {
        $customer = Auth::guard('customer')->user();
        
        $cartData = array_merge([
            'customer_id' => $customer?->id,
            'customer_email' => $customer?->email,
            'customer_first_name' => $customer?->first_name,
            'customer_last_name' => $customer?->last_name,
            'is_guest' => !$customer,
            'is_active' => true,
            'currency_code' => config('app.currency', 'USD'),
            'exchange_rate' => 1.0,
            'items_count' => 0,
            'items_qty' => 0,
            'grand_total' => 0,
            'sub_total' => 0,
            'tax_total' => 0,
            'discount_amount' => 0,
        ], $data);

        $cart = $this->cartRepository->create($cartData);
        
        if (!$customer) {
            Session::put('cart_id', $cart->id);
        }

        return $cart;
    }

    /**
     * Add product to cart
     */
    public function addToCart(Product $product, int $quantity = 1, array $options = []): CartItem
    {
        if (!$product->canAddToCart($quantity)) {
            throw new \Exception('Insufficient stock for product: ' . $product->name);
        }

        $cart = $this->getActiveCart();
        
        // Check if product already exists in cart
        $existingItem = $cart->items()
            ->where('product_id', $product->id)
            ->where('product_options', json_encode($options))
            ->first();

        if ($existingItem) {
            return $this->updateCartItem($existingItem->id, $existingItem->quantity + $quantity);
        }

        // Create new cart item
        $cartItem = $cart->items()->create([
            'product_id' => $product->id,
            'product_sku' => $product->sku,
            'product_name' => $product->name,
            'product_type' => $product->type,
            'quantity' => $quantity,
            'price' => $product->final_price,
            'base_price' => $product->final_price,
            'total' => $product->final_price * $quantity,
            'base_total' => $product->final_price * $quantity,
            'weight' => $product->weight * $quantity,
            'product_options' => $options,
        ]);

        $this->updateCartTotals($cart);

        return $cartItem;
    }

    /**
     * Update cart item quantity
     */
    public function updateCartItem(int $cartItemId, int $quantity): CartItem
    {
        $cart = $this->getActiveCart();
        $cartItem = $cart->items()->findOrFail($cartItemId);

        if ($quantity <= 0) {
            return $this->removeFromCart($cartItemId);
        }

        if (!$cartItem->product->canAddToCart($quantity)) {
            throw new \Exception('Insufficient stock for product: ' . $cartItem->product_name);
        }

        $cartItem->update([
            'quantity' => $quantity,
            'total' => $cartItem->price * $quantity,
            'base_total' => $cartItem->base_price * $quantity,
            'weight' => ($cartItem->product->weight ?? 0) * $quantity,
        ]);

        $this->updateCartTotals($cart);

        return $cartItem;
    }

    /**
     * Remove item from cart
     */
    public function removeFromCart(int $cartItemId): bool
    {
        $cart = $this->getActiveCart();
        $cartItem = $cart->items()->find($cartItemId);

        if (!$cartItem) {
            return false;
        }

        $cartItem->delete();
        $this->updateCartTotals($cart);

        return true;
    }

    /**
     * Clear entire cart
     */
    public function clearCart(): bool
    {
        $cart = $this->findActiveCart();
        
        if (!$cart) {
            return false;
        }

        $cart->items()->delete();
        $this->updateCartTotals($cart);

        return true;
    }

    /**
     * Apply coupon to cart
     */
    public function applyCoupon(string $couponCode): bool
    {
        $cart = $this->getActiveCart();
        
        // Here you would implement coupon validation logic
        // For now, just update the cart with the coupon code
        
        $cart->update([
            'coupon_code' => $couponCode,
            // You would calculate discount based on coupon rules
            'discount_amount' => 0, // Calculate actual discount
        ]);

        $this->updateCartTotals($cart);

        return true;
    }

    /**
     * Remove coupon from cart
     */
    public function removeCoupon(): bool
    {
        $cart = $this->getActiveCart();
        
        $cart->update([
            'coupon_code' => null,
            'discount_amount' => 0,
        ]);

        $this->updateCartTotals($cart);

        return true;
    }

    /**
     * Update cart totals
     */
    protected function updateCartTotals(Cart $cart): void
    {
        $items = $cart->items;
        
        $itemsCount = $items->count();
        $itemsQty = $items->sum('quantity');
        $subTotal = $items->sum('total');
        
        // Calculate tax (you would implement tax calculation logic here)
        $taxTotal = $this->calculateTax($cart, $subTotal);
        
        // Calculate final total
        $grandTotal = $subTotal + $taxTotal - $cart->discount_amount;

        $cart->update([
            'items_count' => $itemsCount,
            'items_qty' => $itemsQty,
            'sub_total' => $subTotal,
            'base_sub_total' => $subTotal,
            'tax_total' => $taxTotal,
            'base_tax_total' => $taxTotal,
            'grand_total' => $grandTotal,
            'base_grand_total' => $grandTotal,
        ]);
    }

    /**
     * Calculate tax for cart
     */
    protected function calculateTax(Cart $cart, float $subTotal): float
    {
        // Implement your tax calculation logic here
        // This is a simple example - you'd want more sophisticated tax rules
        
        $taxRate = config('shop.default_tax_rate', 0.0);
        return $subTotal * ($taxRate / 100);
    }

    /**
     * Get cart summary
     */
    public function getCartSummary(): array
    {
        $cart = $this->findActiveCart();
        
        if (!$cart) {
            return [
                'items_count' => 0,
                'items_qty' => 0,
                'sub_total' => 0,
                'tax_total' => 0,
                'discount_amount' => 0,
                'grand_total' => 0,
                'formatted_grand_total' => '$0.00',
            ];
        }

        return [
            'items_count' => $cart->items_count,
            'items_qty' => $cart->items_qty,
            'sub_total' => $cart->sub_total,
            'tax_total' => $cart->tax_total,
            'discount_amount' => $cart->discount_amount,
            'grand_total' => $cart->grand_total,
            'formatted_grand_total' => '$' . number_format($cart->grand_total, 2),
            'coupon_code' => $cart->coupon_code,
        ];
    }

    /**
     * Merge guest cart with customer cart after login
     */
    public function mergeGuestCart(Customer $customer): void
    {
        $guestCartId = Session::get('cart_id');
        
        if (!$guestCartId) {
            return;
        }

        $guestCart = $this->cartRepository->findActive($guestCartId);
        
        if (!$guestCart || $guestCart->items->isEmpty()) {
            return;
        }

        $customerCart = $this->cartRepository->findActiveByCustomer($customer->id);
        
        if (!$customerCart) {
            // Convert guest cart to customer cart
            $guestCart->update([
                'customer_id' => $customer->id,
                'customer_email' => $customer->email,
                'customer_first_name' => $customer->first_name,
                'customer_last_name' => $customer->last_name,
                'is_guest' => false,
            ]);
        } else {
            // Merge guest cart items into customer cart
            foreach ($guestCart->items as $guestItem) {
                $existingItem = $customerCart->items()
                    ->where('product_id', $guestItem->product_id)
                    ->where('product_options', $guestItem->product_options)
                    ->first();

                if ($existingItem) {
                    $existingItem->update([
                        'quantity' => $existingItem->quantity + $guestItem->quantity,
                        'total' => $existingItem->price * ($existingItem->quantity + $guestItem->quantity),
                    ]);
                } else {
                    $guestItem->update(['cart_id' => $customerCart->id]);
                }
            }

            $this->updateCartTotals($customerCart);
            $guestCart->delete();
        }

        Session::forget('cart_id');
    }

    /**
     * Convert cart to order data
     */
    public function prepareOrderData(Cart $cart, array $shippingAddress, array $billingAddress, string $paymentMethod, string $shippingMethod): array
    {
        return [
            'customer_id' => $cart->customer_id,
            'customer_email' => $cart->customer_email,
            'customer_first_name' => $cart->customer_first_name,
            'customer_last_name' => $cart->customer_last_name,
            'is_guest' => $cart->is_guest,
            'status' => 'pending',
            'currency_code' => $cart->currency_code,
            'exchange_rate' => $cart->exchange_rate,
            'items_count' => $cart->items_count,
            'items_qty' => $cart->items_qty,
            'sub_total' => $cart->sub_total,
            'base_sub_total' => $cart->base_sub_total,
            'tax_total' => $cart->tax_total,
            'base_tax_total' => $cart->base_tax_total,
            'discount_amount' => $cart->discount_amount,
            'base_discount_amount' => $cart->base_discount_amount,
            'shipping_amount' => 0, // Calculate shipping
            'base_shipping_amount' => 0,
            'grand_total' => $cart->grand_total,
            'base_grand_total' => $cart->base_grand_total,
            'coupon_code' => $cart->coupon_code,
            'payment_method' => $paymentMethod,
            'shipping_method' => $shippingMethod,
            'shipping_address' => $shippingAddress,
            'billing_address' => $billingAddress,
            'items' => $cart->items->map(function ($item) {
                return [
                    'product_id' => $item->product_id,
                    'product_sku' => $item->product_sku,
                    'product_name' => $item->product_name,
                    'product_type' => $item->product_type,
                    'quantity' => $item->quantity,
                    'price' => $item->price,
                    'base_price' => $item->base_price,
                    'total' => $item->total,
                    'base_total' => $item->base_total,
                    'weight' => $item->weight,
                    'product_options' => $item->product_options,
                ];
            })->toArray(),
        ];
    }
}
