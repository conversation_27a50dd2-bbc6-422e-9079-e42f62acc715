#!/bin/bash

echo "🚀 Installing Custom Laravel E-commerce Packages..."
echo

# Check if we're in a Laravel project
if [ ! -f "artisan" ]; then
    echo "❌ Error: This doesn't appear to be a Laravel project directory."
    echo "Please run this script from your Laravel project root."
    exit 1
fi

echo "✅ Laravel project detected!"
echo

echo "📦 Installing Core E-commerce Packages..."
echo

# Core E-commerce packages
echo "Installing Image Processing..."
composer require intervention/image:^2.4

echo "Installing PDF Generation..."
composer require barryvdh/laravel-dompdf:^2.0.0

echo "Installing Excel Import/Export..."
composer require maatwebsite/excel:^3.1.46

echo "Installing Authentication..."
composer require laravel/sanctum:^4.0
composer require laravel/ui:^4.0

echo "Installing Nested Categories..."
composer require kalnoy/nestedset:^6.0

echo "Installing Repository Pattern..."
composer require prettus/l5-repository:^2.6

echo "Installing Breadcrumbs..."
composer require diglactic/laravel-breadcrumbs:^9.0

echo "Installing HTTP Client..."
composer require guzzlehttp/guzzle:^7.0.1

echo "Installing Caching..."
composer require spatie/laravel-responsecache:^7.4

echo "Installing Sitemap..."
composer require spatie/laravel-sitemap:^7.3

echo
echo "💳 Installing Payment Packages..."
echo

echo "Installing PayPal..."
composer require paypal/paypal-checkout-sdk:1.0.1

echo
echo "🔧 Installing Optional Packages..."
echo

echo "Installing Social Login..."
composer require laravel/socialite:^5.0

echo "Installing Visitor Tracking..."
composer require shetabit/visitor:^4.1

echo "Installing Redis..."
composer require predis/predis:^2.2

echo
echo "🛠️ Installing Development Packages..."
echo

echo "Installing Debug Bar..."
composer require barryvdh/laravel-debugbar:^3.8 --dev

echo "Installing Testing Framework..."
composer require pestphp/pest:^3.0 --dev
composer require pestphp/pest-plugin-laravel:^3.0 --dev

echo "Installing Code Quality Tools..."
composer require laravel/pint:^1.19 --dev

echo
echo "✅ Composer packages installed successfully!"
echo

echo "🎨 Installing Frontend Packages..."
echo

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "Creating package.json..."
    npm init -y
fi

echo "Installing Vue.js 3..."
npm install vue@next @vitejs/plugin-vue

echo "Installing UI Components..."
npm install @headlessui/vue @heroicons/vue

echo "Installing Form Validation..."
npm install @vuelidate/core @vuelidate/validators

echo "Installing HTTP Client..."
npm install axios

echo "Installing Utilities..."
npm install lodash moment

echo "Installing TailwindCSS..."
npm install --save-dev tailwindcss postcss autoprefixer
npm install --save-dev @tailwindcss/forms @tailwindcss/typography

echo
echo "🎨 Initializing TailwindCSS..."
npx tailwindcss init -p

echo
echo "✅ Frontend packages installed successfully!"
echo

echo "📋 Publishing Configuration Files..."
echo

# Publish config files
php artisan vendor:publish --provider="Intervention\Image\ImageServiceProviderLaravelRecent" --quiet
php artisan vendor:publish --provider="Barryvdh\DomPDF\ServiceProvider" --quiet
php artisan vendor:publish --provider="Maatwebsite\Excel\ExcelServiceProvider" --quiet
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider" --quiet
php artisan vendor:publish --provider="Spatie\ResponseCache\ResponseCacheServiceProvider" --quiet
php artisan vendor:publish --provider="Spatie\Sitemap\SitemapServiceProvider" --quiet

echo "Creating storage link..."
php artisan storage:link

echo
echo "🧹 Clearing Caches..."
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

echo
echo "🎉 Installation Complete!"
echo
echo "📝 Next Steps:"
echo "1. Update your .env file with database credentials"
echo "2. Create your database: custom_ecommerce"
echo "3. Run: php artisan migrate"
echo "4. Run: npm run dev"
echo "5. Start development: php artisan serve"
echo
echo "📚 Documentation:"
echo "- Package Installation Guide: PACKAGE_INSTALLATION_GUIDE.md"
echo "- Custom Laravel Structure: CUSTOM_LARAVEL_STRUCTURE.md"
echo "- Project Documentation: PROJECT_DOCUMENTATION.md"
echo
