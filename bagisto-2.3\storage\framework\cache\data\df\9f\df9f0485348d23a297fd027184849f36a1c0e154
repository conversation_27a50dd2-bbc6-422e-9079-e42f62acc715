1755577835s:308854:"a:4:{s:10:"statusCode";i:200;s:7:"headers";O:50:"Symfony\Component\HttpFoundation\ResponseHeaderBag":5:{s:10:" * headers";a:4:{s:12:"content-type";a:1:{i:0;s:24:"text/html; charset=UTF-8";}s:13:"cache-control";a:1:{i:0;s:17:"no-cache, private";}s:4:"date";a:1:{i:0;s:29:"Tu<PERSON>, 12 Aug 2025 04:30:33 GMT";}s:21:"laravel-responsecache";a:1:{i:0;s:31:"<PERSON><PERSON>, 12 Aug 2025 10:00:35 +0530";}}s:15:" * cacheControl";a:0:{}s:23:" * computedCacheControl";a:2:{s:8:"no-cache";b:1;s:7:"private";b:1;}s:10:" * cookies";a:0:{}s:14:" * headerNames";a:4:{s:12:"content-type";s:12:"Content-Type";s:13:"cache-control";s:13:"Cache-Control";s:4:"date";s:4:"Date";s:21:"laravel-responsecache";s:21:"laravel-responsecache";}}s:7:"content";s:308103:"<!-- SEO Meta Content -->

<!-- Page Layout -->
<!DOCTYPE html>

<html
    lang="en"
    dir="ltr"
>
    <head>

        

        <title>Meta Title</title>

        <meta charset="UTF-8">

        <meta
            http-equiv="X-UA-Compatible"
            content="IE=edge"
        >
        <meta
            http-equiv="content-language"
            content="en"
        >

        <meta
            name="viewport"
            content="width=device-width, initial-scale=1"
        >
        <meta
            name="base-url"
            content="http://localhost/onlinestore/bagisto-2.3/public"
        >
        <meta
            name="currency"
            content="{&quot;id&quot;:1,&quot;code&quot;:&quot;INR&quot;,&quot;name&quot;:&quot;Indian Rupee&quot;,&quot;symbol&quot;:&quot;\u20b9&quot;,&quot;decimal&quot;:2,&quot;group_separator&quot;:&quot;,&quot;,&quot;decimal_separator&quot;:&quot;.&quot;,&quot;currency_position&quot;:null,&quot;created_at&quot;:null,&quot;updated_at&quot;:null}"
        >

            <meta name="description" content="meta description"/>

    <meta name="keywords" content="meta1, meta2, meta3"/>

    
    
    <meta name="twitter:card" content="summary_large_image" />

    <meta name="twitter:title" content="Arctic Bliss Stylish Winter Scarf" />

    <meta name="twitter:description" content="The Arctic Bliss Winter Scarf is more than just a cold-weather accessory; it&#039;s a statement of warmth, comfort, and style for the winter season. Crafted with care from a luxurious blend of acrylic and wool, this scarf is designed to keep you cozy and snug even in the chilliest temperatures. The soft and plush texture not only provides insulation against the cold but also adds a touch of luxury to your winter wardrobe. The design of the Arctic Bliss Winter Scarf is both stylish and versatile, making it a perfect addition to a variety of winter outfits. Whether you&#039;re dressing up for a special occasion or adding a chic layer to your everyday look, this scarf complements your style effortlessly. The extra-long length of the scarf offers customizable styling options. Wrap it around for added warmth, drape it loosely for a casual look, or experiment with different knots to express your unique style. This versatility makes it a must-have accessory for the winter season. Looking for the perfect gift? The Arctic Bliss Winter Scarf is an ideal choice. Whether you&#039;re surprising a loved one or treating yourself, this scarf is a timeless and practical gift that will be cherished throughout the winter months. Embrace the winter with the Arctic Bliss Winter Scarf, where warmth meets style in perfect harmony. Elevate your winter wardrobe with this essential accessory that not only keeps you warm but also adds a touch of sophistication to your cold-weather ensemble." />

    <meta name="twitter:image:alt" content="" />

    <meta name="twitter:image" content="http://localhost/onlinestore/bagisto-2.3/public/cache/medium/product/2/BURS21o4NmmkrUhP2sebQrdigeA8sVS5xNapyIVS.webp" />

    <meta property="og:type" content="og:product" />

    <meta property="og:title" content="Arctic Bliss Stylish Winter Scarf" />

    <meta property="og:image" content="http://localhost/onlinestore/bagisto-2.3/public/cache/medium/product/2/BURS21o4NmmkrUhP2sebQrdigeA8sVS5xNapyIVS.webp" />

    <meta property="og:description" content="The Arctic Bliss Winter Scarf is more than just a cold-weather accessory; it&#039;s a statement of warmth, comfort, and style for the winter season. Crafted with care from a luxurious blend of acrylic and wool, this scarf is designed to keep you cozy and snug even in the chilliest temperatures. The soft and plush texture not only provides insulation against the cold but also adds a touch of luxury to your winter wardrobe. The design of the Arctic Bliss Winter Scarf is both stylish and versatile, making it a perfect addition to a variety of winter outfits. Whether you&#039;re dressing up for a special occasion or adding a chic layer to your everyday look, this scarf complements your style effortlessly. The extra-long length of the scarf offers customizable styling options. Wrap it around for added warmth, drape it loosely for a casual look, or experiment with different knots to express your unique style. This versatility makes it a must-have accessory for the winter season. Looking for the perfect gift? The Arctic Bliss Winter Scarf is an ideal choice. Whether you&#039;re surprising a loved one or treating yourself, this scarf is a timeless and practical gift that will be cherished throughout the winter months. Embrace the winter with the Arctic Bliss Winter Scarf, where warmth meets style in perfect harmony. Elevate your winter wardrobe with this essential accessory that not only keeps you warm but also adds a touch of sophistication to your cold-weather ensemble." />

    <meta property="og:url" content="http://localhost/onlinestore/bagisto-2.3/public/arctic-bliss-stylish-winter-scarf" />

        <link
            rel="icon"
            sizes="16x16"
            href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/favicon-Df9chQdB.ico"
        />

        <link rel="preload" as="style" href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-F65DyJd8.css" /><link rel="preload" as="style" href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-DsP8OK1c.css" /><link rel="modulepreload" href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-zmUk0CX0.js" /><link rel="stylesheet" href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-F65DyJd8.css" /><link rel="stylesheet" href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-DsP8OK1c.css" /><script type="module" src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-zmUk0CX0.js"></script>
        <link
            rel="preload"
            href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap"
            as="style"
        >
        <link
            rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap"
        >

        <link
            rel="preload"
            href="https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap"
            as="style"
        >
        <link
            rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap"
        >

        
        <style>
            
        </style>

                    <script type="speculationrules">
                {"prerender":[{"source":"document","where":{"and":[{"href_matches":"/*"},{"not":{"href_matches":"account"}},{"not":{"href_matches":"checkout"}},{"not":{"href_matches":"onepage"}},{"not":{"href_matches":"cart"}}]},"eagerness":"moderate"}]}            </script>
        
        

    </head>

    <body>
        

        <a
            href="#main"
            class="skip-to-main-content-link"
        >
            Skip to main content
        </a>

        <div id="app">
            <!-- Flash Message Blade Component -->
            <v-flash-group ref='flashes'></v-flash-group>


            <!-- Confirm Modal Blade Component -->
            <v-modal-confirm ref="confirmModal"></v-modal-confirm>


            <!-- Page Header Blade Component -->
                            <header class="shadow-gray sticky top-0 z-10 bg-white shadow-sm max-lg:shadow-none">
    <div class="flex flex-wrap max-lg:hidden">
    <div class="flex min-h-[78px] w-full justify-between border border-b border-l-0 border-r-0 border-t-0 px-[60px] max-1180:px-8">
    <!--
        This section will provide categories for the first, second, and third levels. If
        additional levels are required, users can customize them according to their needs.
    -->
    <!-- Left Nagivation Section -->
    <div class="flex items-center gap-x-10 max-[1180px]:gap-x-5">
        

        <a
            href="http://localhost/onlinestore/bagisto-2.3/public"
            aria-label="Bagisto"
        >
            <img
                src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/logo-CZWQQgOF.svg"
                width="131"
                height="29"
                alt="OnlineStore"
            >
        </a>

        

        

        <v-desktop-category>
            <div class="flex items-center gap-5">
                <span
                    class="shimmer h-6 w-20 rounded"
                    role="presentation"
                ></span>

                <span
                    class="shimmer h-6 w-20 rounded"
                    role="presentation"
                ></span>

                <span
                    class="shimmer h-6 w-20 rounded"
                    role="presentation"
                ></span>
            </div>
        </v-desktop-category>

        
    </div>

    <!-- Right Nagivation Section -->
    <div class="flex items-center gap-x-9 max-[1100px]:gap-x-6 max-lg:gap-x-8">

        

        <!-- Search Bar Container -->
        <div class="relative w-full">
            <form
                action="http://localhost/onlinestore/bagisto-2.3/public/search"
                class="flex max-w-[445px] items-center"
                role="search"
            >
                <label
                    for="organic-search"
                    class="sr-only"
                >
                    Search                </label>

                <div class="icon-search pointer-events-none absolute top-2.5 flex items-center text-xl ltr:left-3 rtl:right-3"></div>

                <input
                    type="text"
                    name="query"
                    value=""
                    class="block w-full rounded-lg border border-transparent bg-zinc-100 px-11 py-3 text-xs font-medium text-gray-900 transition-all hover:border-gray-400 focus:border-gray-400"
                    minlength="0"
                    maxlength="1000"
                    placeholder="Search products here"
                    aria-label="Search products here"
                    aria-required="true"
                    pattern="[^\\]+"
                    required
                >

                <button
                    type="submit"
                    class="hidden"
                    aria-label="Submit"
                >
                </button>

                                    <v-image-search>
    <button type="button"
        class="icon-camera absolute top-3 flex items-center text-xl max-sm:top-2.5 ltr:right-3 ltr:pr-3 max-md:ltr:right-1.5 rtl:left-3 rtl:pl-3 max-md:rtl:left-1.5"
        aria-label="Search">
    </button>
</v-image-search>

                            </form>
        </div>

        

        <!-- Right Navigation Links -->
        <div class="mt-1.5 flex gap-x-8 max-[1100px]:gap-x-6 max-lg:gap-x-8">

            

            <!-- Compare -->
                            <a
                    href="http://localhost/onlinestore/bagisto-2.3/public/compare"
                    aria-label="Compare"
                >
                    <span
                        class="icon-compare inline-block cursor-pointer text-2xl"
                        role="presentation"
                    ></span>
                </a>
            
            

            

            <!-- Mini cart -->
                            <!-- Mini Cart Vue Component -->
<v-mini-cart>
    <span
        class="icon-cart cursor-pointer text-2xl"
        role="button"
        aria-label="Shopping Cart"
    ></span>
</v-mini-cart>

            
            

            

            <!-- user profile -->
            <v-dropdown position="bottom-right" class="relative">
            <span
                        class="icon-users inline-block cursor-pointer text-2xl"
                        role="button"
                        aria-label="Profile"
                        tabindex="0"
                    ></span>

        <template v-slot:toggle>
            <span
                        class="icon-users inline-block cursor-pointer text-2xl"
                        role="button"
                        aria-label="Profile"
                        tabindex="0"
                    ></span>
        </template>
    
            <template v-slot:content>
            <div class="p-5 !p-0">
                <div class="grid gap-2.5 p-5 pb-0">
                            <p class="font-dmserif text-xl">
                                Welcome’
                                Vignesh
                            </p>

                            <p class="text-sm">
                                Manage Cart, Orders & Wishlist                            </p>
                        </div>

                        <p class="mt-3 w-full border border-zinc-200"></p>

                        <div class="mt-2.5 grid gap-1 pb-2.5">
                            

                            <a
                                class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/profile"
                            >
                                Profile                            </a>

                            <a
                                class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/orders"
                            >
                                Orders                            </a>

                                                            <a
                                    class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                    href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/wishlist"
                                >
                                    Wishlist                                </a>
                            
                            <!--Customers logout-->
                                                            <!--
    If a component has the `as` attribute, it indicates that it uses
    the ajaxified form or some customized slot form.
-->
    
    
    <v-form
        method="POST"
        :initial-errors="[]"
        v-slot="{ meta, errors }"
        action="http://localhost/onlinestore/bagisto-2.3/public/customer/logout" id="customerLogout"
    >
                    <input type="hidden" name="_token" value="<laravel-responsecache-csrf-token-here>" autocomplete="off">        
                    <input type="hidden" name="_method" value="DELETE">        
        
    </v-form>

                                <a
                                    class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                    href="http://localhost/onlinestore/bagisto-2.3/public/customer/logout"
                                    onclick="event.preventDefault(); document.getElementById('customerLogout').submit();"
                                >
                                    Logout                                </a>
                            
                            
                        </div>
            </div>
        </template>
    
    </v-dropdown>


            
        </div>
    </div>
</div>


</div>

    <!--
    This code needs to be refactored to reduce the amount of PHP in the Blade
    template as much as possible.
-->

<div class="flex flex-wrap gap-4 px-4 pb-4 pt-6 shadow-sm lg:hidden">
    <div class="flex w-full items-center justify-between">
        <!-- Left Navigation -->
        <div class="flex items-center gap-x-1.5">
            

            <!-- Drawer -->
            <v-mobile-drawer></v-mobile-drawer>

            

            

            <a
                href="http://localhost/onlinestore/bagisto-2.3/public"
                class="max-h-[30px]"
                aria-label="Bagisto"
            >
                <img
                    src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/logo-CZWQQgOF.svg"
                    alt="OnlineStore"
                    width="131"
                    height="29"
                >
            </a>

            
        </div>

        <!-- Right Navigation -->
        <div>
            <div class="flex items-center gap-x-5 max-md:gap-x-4">
                

                                    <a
                        href="http://localhost/onlinestore/bagisto-2.3/public/compare"
                        aria-label="Compare"
                    >
                        <span class="icon-compare cursor-pointer text-2xl"></span>
                    </a>
                
                

                

                                    <!-- Mini Cart Vue Component -->
<v-mini-cart>
    <span
        class="icon-cart cursor-pointer text-2xl"
        role="button"
        aria-label="Shopping Cart"
    ></span>
</v-mini-cart>

                
                

                <!-- For Large screens -->
                <div class="max-md:hidden">
                    <v-dropdown position="bottom-right" class="relative">
            <span class="icon-users cursor-pointer text-2xl"></span>

        <template v-slot:toggle>
            <span class="icon-users cursor-pointer text-2xl"></span>
        </template>
    
            <template v-slot:content>
            <div class="p-5 !p-0">
                <div class="grid gap-2.5 p-5 pb-0">
                                    <p class="font-dmserif text-xl">
                                        Welcome’
                                        Vignesh
                                    </p>

                                    <p class="text-sm">
                                        Manage Cart, Orders & Wishlist                                    </p>
                                </div>

                                <p class="mt-3 w-full border border-zinc-200"></p>

                                <div class="mt-2.5 grid gap-1 pb-2.5">
                                    

                                    <a
                                        class="cursor-pointer px-5 py-2 text-base"
                                        href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/profile"
                                    >
                                        Profile                                    </a>

                                    <a
                                        class="cursor-pointer px-5 py-2 text-base"
                                        href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/orders"
                                    >
                                        Orders                                    </a>

                                                                            <a
                                            class="cursor-pointer px-5 py-2 text-base"
                                            href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/wishlist"
                                        >
                                            Wishlist                                        </a>
                                    
                                    <!--Customers logout-->
                                                                            <!--
    If a component has the `as` attribute, it indicates that it uses
    the ajaxified form or some customized slot form.
-->
    
    
    <v-form
        method="POST"
        :initial-errors="[]"
        v-slot="{ meta, errors }"
        action="http://localhost/onlinestore/bagisto-2.3/public/customer/logout" id="customerLogout"
    >
                    <input type="hidden" name="_token" value="<laravel-responsecache-csrf-token-here>" autocomplete="off">        
                    <input type="hidden" name="_method" value="DELETE">        
        
    </v-form>

                                        <a
                                            class="cursor-pointer px-5 py-2 text-base"
                                            href="http://localhost/onlinestore/bagisto-2.3/public/customer/logout"
                                            onclick="event.preventDefault(); document.getElementById('customerLogout').submit();"
                                        >
                                            Logout                                        </a>
                                    
                                    
                                </div>
            </div>
        </template>
    
    </v-dropdown>

                </div>

                <!-- For Medium and small screen -->
                <div class="md:hidden">
                    
                    <!-- Customers Dropdown -->
                                            <a
                            href="http://localhost/onlinestore/bagisto-2.3/public/customer/account"
                            aria-label="Account"
                        >
                            <span class="icon-users cursor-pointer text-2xl"></span>
                        </a>
                                    </div>
            </div>
        </div>
    </div>

    

    <!-- Serach Catalog Form -->
    <form action="http://localhost/onlinestore/bagisto-2.3/public/search" class="flex w-full items-center">
        <label
            for="organic-search"
            class="sr-only"
        >
            Search        </label>

        <div class="relative w-full">
            <div class="icon-search pointer-events-none absolute top-3 flex items-center text-2xl max-md:text-xl max-sm:top-2.5 ltr:left-3 rtl:right-3"></div>

            <input
                type="text"
                class="block w-full rounded-xl border border-['#E3E3E3'] px-11 py-3.5 text-sm font-medium text-gray-900 max-md:rounded-lg max-md:px-10 max-md:py-3 max-md:font-normal max-sm:text-xs"
                name="query"
                value=""
                placeholder="Search products here"
                required
            >

                            <v-image-search>
    <button type="button"
        class="icon-camera absolute top-3 flex items-center text-xl max-sm:top-2.5 ltr:right-3 ltr:pr-3 max-md:ltr:right-1.5 rtl:left-3 rtl:pl-3 max-md:rtl:left-1.5"
        aria-label="Search">
    </button>
</v-image-search>

                    </div>
    </form>

    
</div>

</header>


            
            
            

            <!-- Page Content Blade Component -->
            <main id="main" class="bg-white">
                <!-- Page Title -->
     
    

    <!-- Breadcrumbs -->
            <div class="flex justify-center px-7 max-lg:hidden">
            <div class="mt-[34px] flex justify-start max-lg:hidden">
    <div class="flex items-center gap-x-3.5">        
        <nav aria-label="">
        <ol class="flex">
                                                <li class="flex items-center gap-x-2.5 text-base font-medium">
                        <a href="http://localhost/onlinestore/bagisto-2.3/public">
                            Home
                        </a>

                        <span class="icon-arrow-right rtl:icon-arrow-left text-2xl"></span>
                    </li>
                                                                <li 
                        class="flex items-center gap-x-2.5 break-all text-base text-zinc-500 after:content-['/'] after:last:hidden ltr:ml-2.5 rtl:mr-0" 
                        aria-current="page"
                    >
                        Arctic Bliss Stylish Winter Scarf
                    </li>
                                    </ol>
    </nav>

    </div>
</div>
        </div>
    
    <!-- Product Information Vue Component -->
    <v-product>
        <div class="container px-[60px] max-1180:px-0">
    <div class="mt-12 flex gap-10 max-1180:flex-wrap max-lg:mt-0 max-sm:gap-y-6">

        <div class="sticky top-8 flex h-max gap-8 max-1180:hidden">
    <div class="flex max-h-[100px] max-w-[100px] flex-wrap gap-2.5">
        <div class="flex-24 h-509 flex max-w-[100px] flex-wrap place-content-start justify-center gap-2.5">
            <span class="shimmer h-6 w-6 text-2xl"></span>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <span class="shimmer h-6 w-6 text-2xl"></span>
        </div>
    </div>
    
    <div class="max-h-[610px] max-w-[560px]">
        <div class="shimmer min-h-[607px] min-w-[560px] rounded-xl"></div>
    </div>
</div>

<div class="overflow-hidden 1180:hidden">
    <div class="shimmer aspect-square max-h-screen w-screen"></div>
</div>
        <div class="relative max-w-[590px] max-1180:w-full max-1180:max-w-full max-1180:px-5">
            <div class="flex justify-between gap-4">
                <h1 class="shimmer h-[46px] w-2/4"></h1>

                <div class="shimmer h-[46px] w-[46px] rounded-full"></div>
            </div>

            <div class="shimmer mt-4 h-[38px] w-[150px] rounded-lg"></div>

            <p class="shimmer mt-5 h-9 w-[35%]"></p>

            <div class="mt-6 grid gap-2.5 max-sm:my-[20px] max-sm:flex max-sm:justify-between">
                <p class="shimmer h-[27px] w-full max-sm:h-[21px] max-sm:w-[100px]"></p>
                <p class="shimmer h-[27px] w-[90%] max-sm:hidden"></p>
                <p class="shimmer h-[27px] w-4/5 max-sm:h-[21px] max-sm:w-[60px]"></p>
            </div>

            <!-- Colors -->
            <div class="mt-5">
                <h3 class="shimmer mb-4 h-9 w-1/5"></h3>

                <div class="flex items-center space-x-3">
                    <span class="shimmer h-8 w-8 rounded-full"></span>
                    <span class="shimmer h-8 w-8 rounded-full"></span>
                    <span class="shimmer h-8 w-8 rounded-full"></span>
                    <span class="shimmer h-8 w-8 rounded-full"></span>
                    <span class="shimmer h-8 w-8 rounded-full"></span>
                </div>
            </div>

            <!-- Size -->
            <div class="mt-5">
                <h3 class="shimmer mb-4 h-9 w-1/5"></h3>

                <div class="flex flex-wrap gap-3">
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                </div>
            </div>

            <div class="mt-8 flex max-w-[470px] flex-wrap gap-4">
                <!-- Quantity changer button -->
                <div class="shimmer h-14 w-[161px] rounded-xl max-sm:w-[124px]"></div>

                <!-- Add to cart Button -->
                <button class="shimmer h-14 w-[279px] rounded-xl"></button>
            </div>

            <!-- Buy Now Button -->
            <button class="shimmer mt-5 h-14 w-full rounded-xl"></button>

            <!-- Share Buttons -->
            <div class="mt-10 flex items-center gap-9 max-sm:flex-wrap">
                <div class="shimmer h-6 w-20"></div>

                <div class="flex items-center gap-6 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-20"></div>
                    
                    <div class="flex gap-3">
                        <span class="shimmer h-10 w-10 rounded-full"></span>
                        <span class="shimmer h-10 w-10 rounded-full"></span>
                        <span class="shimmer h-10 w-10 rounded-full"></span>
                        <span class="shimmer h-10 w-10 rounded-full"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>    </v-product>

    <!-- Information Section -->
    <div class="1180:mt-20">
        <div class="max-1180:hidden">
            <v-tabs
    position="center"
    ref="productTabs"
>
    <div class="flex justify-center gap-8 bg-zinc-100 pt-5 max-1180:hidden">
    <div class="px-8 pb-5">
        <span class="shimmer block h-[30px] w-[200px] bg-zinc-300"></span>
    </div>

    <div class="px-8 pb-5">
        <span class="shimmer block h-[30px] w-[200px] bg-zinc-300"></span>
    </div>

    <div class="px-8 pb-5">
        <span class="shimmer block h-[30px] w-[150px] bg-zinc-300"></span>
    </div>
</div>

<div class="container mt-16 max-1180:px-5">
    <div class="">
        <div class="mt-6 grid gap-2.5">
            <p class="shimmer h-[27px] w-full"></p>
            <p class="shimmer h-[27px] w-full"></p>
            <p class="shimmer h-[27px] w-full"></p>
            <p class="shimmer h-[27px] w-full"></p>
            <p class="shimmer h-[27px] w-full"></p>
        </div>
    </div>
</div></v-tabs>

        </div>
    </div>

    <!-- Information Section -->
    <div class="container mt-6 grid gap-3 !p-0 max-1180:px-5 1180:hidden">
        <!-- Description Accordion -->
        <div class="border-b border-zinc-200 max-md:border-none">
    <v-accordion
        
        is-active="1"
    >
                    <template v-slot:header="{ toggle, isOpen }">
                <div
                    class="flex cursor-pointer select-none items-center justify-between p-4 bg-gray-100 max-md:!py-3 max-sm:!py-2"
                    role="button"
                    tabindex="0"
                    @click="toggle"
                >
                    <p class="text-base font-medium 1180:hidden">
                    Description                </p>

                    <span
                        v-bind:class="isOpen ? 'icon-arrow-up text-2xl' : 'icon-arrow-down text-2xl'"
                        role="button"
                        aria-label="Toggle accordion"
                        tabindex="0"
                    ></span>
                </div>
            </template>
        
                    <template v-slot:content="{ isOpen }">
                <div
                    class="z-10 rounded-lg bg-white p-1.5 max-sm:px-0"
                    v-show="isOpen"
                >
                    <div class="mb-5 text-lg text-zinc-500 max-1180:text-sm max-md:mb-1 max-md:px-4">
                    The Arctic Bliss Winter Scarf is more than just a cold-weather accessory; it's a statement of warmth, comfort, and style for the winter season. Crafted with care from a luxurious blend of acrylic and wool, this scarf is designed to keep you cozy and snug even in the chilliest temperatures. The soft and plush texture not only provides insulation against the cold but also adds a touch of luxury to your winter wardrobe. The design of the Arctic Bliss Winter Scarf is both stylish and versatile, making it a perfect addition to a variety of winter outfits. Whether you're dressing up for a special occasion or adding a chic layer to your everyday look, this scarf complements your style effortlessly. The extra-long length of the scarf offers customizable styling options. Wrap it around for added warmth, drape it loosely for a casual look, or experiment with different knots to express your unique style. This versatility makes it a must-have accessory for the winter season. Looking for the perfect gift? The Arctic Bliss Winter Scarf is an ideal choice. Whether you're surprising a loved one or treating yourself, this scarf is a timeless and practical gift that will be cherished throughout the winter months. Embrace the winter with the Arctic Bliss Winter Scarf, where warmth meets style in perfect harmony. Elevate your winter wardrobe with this essential accessory that not only keeps you warm but also adds a touch of sophistication to your cold-weather ensemble.
                </div>
                </div>
            </template>
            </v-accordion>
</div>


        <!-- Additional Information Accordion -->
        
        <!-- Reviews Accordion -->
        <div class="border-b border-zinc-200 max-md:border-none">
    <v-accordion
        
        is-active=""
    >
                    <template v-slot:header="{ toggle, isOpen }">
                <div
                    class="flex cursor-pointer select-none items-center justify-between p-4 bg-gray-100 max-md:!py-3 max-sm:!py-2" id="review-accordian-button"
                    role="button"
                    tabindex="0"
                    @click="toggle"
                >
                    <p class="text-base font-medium">
                    Reviews                </p>

                    <span
                        v-bind:class="isOpen ? 'icon-arrow-up text-2xl' : 'icon-arrow-down text-2xl'"
                        role="button"
                        aria-label="Toggle accordion"
                        tabindex="0"
                    ></span>
                </div>
            </template>
        
                    <template v-slot:content="{ isOpen }">
                <div
                    class="z-10 rounded-lg bg-white p-1.5"
                    v-show="isOpen"
                >
                    <v-product-reviews>
    <div class="container max-1180:px-5">
        <!-- Review Section Header -->
<div class="mb-8 flex items-center justify-between gap-4 max-sm:flex-wrap">
    <div class="shimmer h-9 w-[245px]"></div>
</div>

<div class="flex gap-16 max-lg:flex-wrap">
    <!-- Left Section -->
    <div class="flex flex-col gap-6">
        <div class="flex flex-col items-center gap-2">
            <div class="shimmer h-12 w-16"></div>
            
            <div class="flex items-center gap-0.5">
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
            </div>

            <div class="shimmer h-6 w-20"></div>
        </div>

        <!-- Ratings By Individual Stars -->
        <div class="grid max-w-[365px] flex-wrap gap-y-3">
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                    </div>
    </div>

    <!-- Right Section -->
    <div class="flex w-full flex-col gap-5">
        <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    </div>
</div>    </div>
</v-product-reviews>
                </div>
            </template>
            </v-accordion>
</div>

    </div>

    <!-- Featured Products -->
    <v-products-carousel
    src="http://localhost/onlinestore/bagisto-2.3/public/api/products/2/related"
    title="Related Products"
    navigation-link=""
>
    <div class="container mt-20 max-lg:px-8 max-md:mt-8 max-sm:mt-7 max-sm:!px-4">
    <div class="flex items-center justify-between">
        <h3 class="shimmer h-8 w-[200px] max-sm:h-7"></h3>

        <div class="flex items-center justify-between gap-8 max-lg:hidden">
            <span
                class="shimmer inline-block h-6 w-6"
                role="presentation"
            ></span>

            <span
                class="shimmer inline-block h-6 w-6 max-sm:hidden"
                role="presentation"
            ></span>
        </div>

        <div class="shimmer h-7 w-24 max-sm:h-5 max-sm:w-[68px] lg:hidden"></div>
    </div>

    <div class="scrollbar-hide mt-10 flex gap-8 overflow-auto pb-2.5 max-md:mt-5 max-sm:gap-4">
        <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    </div>

    </div>
</v-products-carousel>


    <!-- Up-sell Products -->
    <v-products-carousel
    src="http://localhost/onlinestore/bagisto-2.3/public/api/products/2/up-sell"
    title="We found other products you might like!"
    navigation-link=""
>
    <div class="container mt-20 max-lg:px-8 max-md:mt-8 max-sm:mt-7 max-sm:!px-4">
    <div class="flex items-center justify-between">
        <h3 class="shimmer h-8 w-[200px] max-sm:h-7"></h3>

        <div class="flex items-center justify-between gap-8 max-lg:hidden">
            <span
                class="shimmer inline-block h-6 w-6"
                role="presentation"
            ></span>

            <span
                class="shimmer inline-block h-6 w-6 max-sm:hidden"
                role="presentation"
            ></span>
        </div>

        <div class="shimmer h-7 w-24 max-sm:h-5 max-sm:w-[68px] lg:hidden"></div>
    </div>

    <div class="scrollbar-hide mt-10 flex gap-8 overflow-auto pb-2.5 max-md:mt-5 max-sm:gap-4">
        <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    </div>

    </div>
</v-products-carousel>
            </main>

            


            <!-- Page Services Blade Component -->
                            <!--
    The ThemeCustomizationRepository repository is injected directly here because there is no way
    to retrieve it from the view composer, as this is an anonymous component.
-->


<!-- Features -->
    <div class="container mt-20 max-lg:px-8 max-md:mt-10 max-md:px-4">
        <div class="max-md:max-y-6 flex justify-center gap-6 max-lg:flex-wrap max-md:grid max-md:grid-cols-2 max-md:gap-x-2.5 max-md:text-center">
                            <div class="flex items-center gap-5 bg-white max-md:grid max-md:gap-2.5 max-sm:gap-1 max-sm:px-2">
                    <span
                        class="icon-truck flex items-center justify-center w-[60px] h-[60px] bg-white border border-black rounded-full text-4xl text-navyBlue p-2.5 max-md:m-auto max-md:w-16 max-md:h-16 max-sm:w-10 max-sm:h-10 max-sm:text-2xl"
                        role="presentation"
                    ></span>

                    <div class="max-lg:grid max-lg:justify-center">
                        <!-- Service Title -->
                        <p class="font-dmserif text-base font-medium max-md:text-xl max-sm:text-sm">Free Shipping</p>

                        <!-- Service Description -->
                        <p class="mt-2.5 max-w-[217px] text-sm font-medium text-zinc-500 max-md:mt-0 max-md:text-base max-sm:text-xs">
                            Enjoy free shipping on all orders
                        </p>
                    </div>
                </div>
                            <div class="flex items-center gap-5 bg-white max-md:grid max-md:gap-2.5 max-sm:gap-1 max-sm:px-2">
                    <span
                        class="icon-product flex items-center justify-center w-[60px] h-[60px] bg-white border border-black rounded-full text-4xl text-navyBlue p-2.5 max-md:m-auto max-md:w-16 max-md:h-16 max-sm:w-10 max-sm:h-10 max-sm:text-2xl"
                        role="presentation"
                    ></span>

                    <div class="max-lg:grid max-lg:justify-center">
                        <!-- Service Title -->
                        <p class="font-dmserif text-base font-medium max-md:text-xl max-sm:text-sm">Product Replace</p>

                        <!-- Service Description -->
                        <p class="mt-2.5 max-w-[217px] text-sm font-medium text-zinc-500 max-md:mt-0 max-md:text-base max-sm:text-xs">
                            Easy Product Replacement Available!
                        </p>
                    </div>
                </div>
                            <div class="flex items-center gap-5 bg-white max-md:grid max-md:gap-2.5 max-sm:gap-1 max-sm:px-2">
                    <span
                        class="icon-dollar-sign flex items-center justify-center w-[60px] h-[60px] bg-white border border-black rounded-full text-4xl text-navyBlue p-2.5 max-md:m-auto max-md:w-16 max-md:h-16 max-sm:w-10 max-sm:h-10 max-sm:text-2xl"
                        role="presentation"
                    ></span>

                    <div class="max-lg:grid max-lg:justify-center">
                        <!-- Service Title -->
                        <p class="font-dmserif text-base font-medium max-md:text-xl max-sm:text-sm">Emi Available</p>

                        <!-- Service Description -->
                        <p class="mt-2.5 max-w-[217px] text-sm font-medium text-zinc-500 max-md:mt-0 max-md:text-base max-sm:text-xs">
                            No cost EMI available on all major credit cards
                        </p>
                    </div>
                </div>
                            <div class="flex items-center gap-5 bg-white max-md:grid max-md:gap-2.5 max-sm:gap-1 max-sm:px-2">
                    <span
                        class="icon-support flex items-center justify-center w-[60px] h-[60px] bg-white border border-black rounded-full text-4xl text-navyBlue p-2.5 max-md:m-auto max-md:w-16 max-md:h-16 max-sm:w-10 max-sm:h-10 max-sm:text-2xl"
                        role="presentation"
                    ></span>

                    <div class="max-lg:grid max-lg:justify-center">
                        <!-- Service Title -->
                        <p class="font-dmserif text-base font-medium max-md:text-xl max-sm:text-sm">24/7 Support</p>

                        <!-- Service Description -->
                        <p class="mt-2.5 max-w-[217px] text-sm font-medium text-zinc-500 max-md:mt-0 max-md:text-base max-sm:text-xs">
                            Dedicated 24/7 support via chat and email
                        </p>
                    </div>
                </div>
                    </div>
    </div>

            
            <!-- Page Footer Blade Component -->
                            <!--
    The category repository is injected directly here because there is no way
    to retrieve it from the view composer, as this is an anonymous component.
-->

<!--
    This code needs to be refactored to reduce the amount of PHP in the Blade
    template as much as possible.
-->

<footer class="mt-9 bg-lightOrange max-sm:mt-10">
    <div class="flex justify-between gap-x-6 gap-y-8 p-[60px] max-1060:flex-col-reverse max-md:gap-5 max-md:p-8 max-sm:px-4 max-sm:py-5">
        <!-- For Desktop View -->
        <div class="flex flex-wrap items-start gap-24 max-1180:gap-6 max-1060:hidden">
                                                <ul class="grid gap-5 text-sm">
                        
                                                    <li>
                                <a href="http://localhost/page/about-us">
                                    About Us
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/contact-us">
                                    Contact Us
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/customer-service">
                                    Customer Service
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/whats-new">
                                    What&#039;s New
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/terms-of-use">
                                    Terms of Use
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/terms-conditions">
                                    Terms &amp; Conditions
                                </a>
                            </li>
                                            </ul>
                                    <ul class="grid gap-5 text-sm">
                        
                                                    <li>
                                <a href="http://localhost/page/privacy-policy">
                                    Privacy Policy
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/payment-policy">
                                    Payment Policy
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/shipping-policy">
                                    Shipping Policy
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/refund-policy">
                                    Refund Policy
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/return-policy">
                                    Return Policy
                                </a>
                            </li>
                                            </ul>
                                    </div>

        <!-- For Mobile view -->
        <div class="border-b border-zinc-200 hidden !w-full rounded-xl !border-2 !border-[#e9decc] max-1060:block max-sm:rounded-lg">
    <v-accordion
        
        is-active=""
    >
                    <template v-slot:header="{ toggle, isOpen }">
                <div
                    class="flex cursor-pointer select-none items-center justify-between p-4 rounded-t-lg bg-[#F1EADF] font-medium max-md:p-2.5 max-sm:px-3 max-sm:py-2 max-sm:text-sm"
                    role="button"
                    tabindex="0"
                    @click="toggle"
                >
                    Footer Content

                    <span
                        v-bind:class="isOpen ? 'icon-arrow-up text-2xl' : 'icon-arrow-down text-2xl'"
                        role="button"
                        aria-label="Toggle accordion"
                        tabindex="0"
                    ></span>
                </div>
            </template>
        
                    <template v-slot:content="{ isOpen }">
                <div
                    class="z-10 rounded-lg bg-white p-1.5 flex justify-between !bg-transparent !p-4"
                    v-show="isOpen"
                >
                    <ul class="grid gap-5 text-sm">
                            
                                                            <li>
                                    <a
                                        href="http://localhost/page/about-us"
                                        class="text-sm font-medium max-sm:text-xs">
                                        About Us
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/contact-us"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Contact Us
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/customer-service"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Customer Service
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/whats-new"
                                        class="text-sm font-medium max-sm:text-xs">
                                        What&#039;s New
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/terms-of-use"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Terms of Use
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/terms-conditions"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Terms &amp; Conditions
                                    </a>
                                </li>
                                                    </ul>
                                            <ul class="grid gap-5 text-sm">
                            
                                                            <li>
                                    <a
                                        href="http://localhost/page/privacy-policy"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Privacy Policy
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/payment-policy"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Payment Policy
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/shipping-policy"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Shipping Policy
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/refund-policy"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Refund Policy
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/return-policy"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Return Policy
                                    </a>
                                </li>
                                                    </ul>
                </div>
            </template>
            </v-accordion>
</div>


        

        <!-- News Letter subscription -->
                    <div class="grid gap-2.5">
                <p
                    class="max-w-[288px] text-3xl italic leading-[45px] text-navyBlue max-md:text-2xl max-sm:text-lg"
                    role="heading"
                    aria-level="2"
                >
                    Get Ready for our Fun Newsletter!                </p>

                <p class="text-xs">
                    Subscribe to stay in touch.                </p>

                <div>
                    <!--
    If a component has the `as` attribute, it indicates that it uses
    the ajaxified form or some customized slot form.
-->
    
    
    <v-form
        method="POST"
        :initial-errors="[]"
        v-slot="{ meta, errors }"
        action="http://localhost/onlinestore/bagisto-2.3/public/subscription" class="mt-2.5 rounded max-sm:mt-0"
    >
                    <input type="hidden" name="_token" value="<laravel-responsecache-csrf-token-here>" autocomplete="off">        
        
        <div class="relative w-full">
                            <v-field
            v-slot="{ field, errors }"
            rules="required|email" label="Email"
            name="email"
        >
            <input
                type="email"
                name="email"
                v-bind="field"
                :class="[errors.length ? 'border !border-red-500 hover:border-red-500' : '']"
                class="mb-1.5 w-full rounded-lg border px-5 py-3 text-base font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm block w-[420px] max-w-full rounded-xl border-2 border-[#e9decc] bg-[#F1EADF] px-5 py-4 text-base max-1060:w-full max-md:p-3.5 max-sm:mb-0 max-sm:rounded-lg max-sm:border-2 max-sm:p-2 max-sm:text-sm" aria-label="Email" placeholder="<EMAIL>"
            >
        </v-field>
            
                            <v-error-message
    
    name="email"
    v-slot="{ message }"
>
    <p
        class="text-red-500 text-xs italic"
        v-text="message"
    >
    </p>
</v-error-message>
    
                            <button
                                type="submit"
                                class="absolute top-1.5 flex w-max items-center rounded-xl bg-white px-7 py-2.5 font-medium hover:bg-zinc-100 max-md:top-1 max-md:px-5 max-md:text-xs max-sm:mt-0 max-sm:rounded-lg max-sm:px-4 max-sm:py-2 ltr:right-2 rtl:left-2"
                            >
                                Subscribe                            </button>
                        </div>
    </v-form>
                </div>
            </div>
        
        
    </div>

    <div class="flex justify-between bg-[#F1EADF] px-[60px] py-3.5 max-md:justify-center max-sm:px-5">
        

        <p class="text-sm text-zinc-600 max-md:text-center">
            © Copyright 2010 - 2025, Webkul Software (Registered in India). All rights reserved.        </p>

        
    </div>
</footer>


                    </div>

        <style>
    .path-hint {
        border: solid 1px transparent;
        padding: 1px;
    }

    .path-hint:hover {
        border: 1px solid red;
    }

    .path-hint-tooltip {
        padding: 0px 10px;
        position: absolute;
        background: #000000;
        z-index: 10000;
        color: #fff;
        font-size: 10px;
    }

    .path-hint-tooltip h4 {
        margin-top: 5px;
        margin-bottom: 3px;
        color: #fff;
        font-size: 12px;
    }

    .path-hint-tooltip ul li {
        margin-bottom: 3px;
    }

    .main-container-wrapper .product-card .product-image img {
        max-width: 100%;
        height: 260px;
        object-fit: cover;
    }
</style>


            <script
        type="text/x-template"
        id="v-tab-item-template"
    >
        <div
            v-if="isActive"
            class="animate-[on-fade_0.5s_ease-in-out]"
        >
            <slot></slot>
        </div>
    </script>

    <script type="module">
        app.component('v-tab-item', {
            template: '#v-tab-item-template',

            props: ['title', 'isSelected'],

            data() {
                return {
                    isActive: false
                }
            },

            mounted() {
                this.isActive = this.isSelected;

                /**
                 * On mounted, pushing element to its parents component.
                 */
                this.$parent.$data.tabs.push(this);
            }
        });
    </script>
    <!-- Product Review Template -->
    <script
        type="text/x-template"
        id="v-product-reviews-template"
    >
        <div class="container max-1180:mt-3.5 max-1180:px-5 max-md:px-4 max-sm:px-3.5">
            <!-- Create Review Form Container -->
            <div 
                class="w-full" 
                v-if="canReview"
            >
                <!--
    If a component has the `as` attribute, it indicates that it uses
    the ajaxified form or some customized slot form.
-->
    <v-form v-slot="{ meta, errors, handleSubmit }" as="div">
        <!-- Review Form -->
                    <form
                        class="grid grid-cols-[auto_1fr] justify-center gap-10 max-md:grid-cols-[1fr] max-md:gap-0"
                        @submit="handleSubmit($event, store)"
                        enctype="multipart/form-data"
                    >
                        <div class="max-w-[286px]">
                            <div class="mb-4">
    <v-media
    :class="[errors && errors['attachments'] ? 'border !border-red-500' : '']" name="attachments" class="!mb-0 !p-0 max-md:gap-1.5" label="Attachments" is-multiple="is-multiple" ref="reviewImages"
    width="200px"
    height="200px"
>
    <v-shimmer-image class="mb-4 h-[200px] w-[200px] rounded-xl max-sm:h-[100px] max-sm:w-[100px]">
    <div class="shimmer mb-4 h-[200px] w-[200px] rounded-xl max-sm:h-[100px] max-sm:w-[100px]"></div>
</v-shimmer-image>

</v-media>

        
                                <v-error-message
    class="mt-4"
    name="attachments"
    v-slot="{ message }"
>
    <p
        class="text-red-500 text-xs italic mt-4"
        v-text="message"
    >
    </p>
</v-error-message>
</div>
                        </div>
                        
                        <div>
                            <div class="mb-4">
    <label class="mb-2 block text-base max-sm:text-sm max-sm:mb-1 required mt-0">
    Rating
</label>

                                <span
                                    class="icon-star-fill cursor-pointer text-2xl"
                                    role="presentation"
                                    v-for="rating in [1,2,3,4,5]"
                                    :class="appliedRatings >= rating ? 'text-amber-500' : 'text-zinc-500'"
                                    @click="appliedRatings = rating"
                                >
                                </span>

                                <v-field
                                    type="hidden"
                                    name="rating"
                                    v-model="appliedRatings"
                                ></v-field>

                                <v-error-message
    
    name="rating"
    v-slot="{ message }"
>
    <p
        class="text-red-500 text-xs italic"
        v-text="message"
    >
    </p>
</v-error-message>
</div>

                            
                            <div class="mb-4">
    <label class="mb-2 block text-base max-sm:text-sm max-sm:mb-1 required">
    Title
</label>

                                <v-field
            v-slot="{ field, errors }"
            rules="required" label="Title"
            name="title"
        >
            <input
                type="text"
                name="title"
                v-bind="field"
                :class="[errors.length ? 'border !border-red-500 hover:border-red-500' : '']"
                class="mb-1.5 w-full rounded-lg border px-5 py-3 text-base font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm" placeholder="Title"
            >
        </v-field>
        
                                <v-error-message
    
    name="title"
    v-slot="{ message }"
>
    <p
        class="text-red-500 text-xs italic"
        v-text="message"
    >
    </p>
</v-error-message>
</div>

                            <div class="mb-4">
    <label class="mb-2 block text-base max-sm:text-sm max-sm:mb-1 required">
    Comment
</label>

                                <v-field
            v-slot="{ field, errors }"
            rules="required" label="Comment"
            name="comment"
        >
            <textarea
                type="textarea"
                name="comment"
                v-bind="field"
                :class="[errors.length ? 'border !border-red-500 hover:border-red-500' : '']"
                class="mb-1.5 w-full rounded-lg border px-5 py-3 text-base font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400" placeholder="Comment" rows="12"
            >
            </textarea>

                    </v-field>
        
                                <v-error-message
    
    name="comment"
    v-slot="{ message }"
>
    <p
        class="text-red-500 text-xs italic"
        v-text="message"
    >
    </p>
</v-error-message>
</div>


                            <div class="mt-4 flex justify-start gap-4 max-xl:mb-5 max-sm:mb-5 max-sm:flex-wrap max-sm:justify-normal max-sm:gap-x-0">
                                <button
                                    class="primary-button w-full max-w-[374px] rounded-2xl px-11 py-4 text-center max-md:max-w-full max-md:rounded-lg max-md:py-3 max-sm:py-1.5"
                                    type='submit'
                                >
                                    Submit Review                                </button>
                                
                                <button
                                    type="button"
                                    class="secondary-button items-center rounded-2xl px-8 py-2.5 max-md:w-full max-md:max-w-full max-md:rounded-lg max-md:py-1.5"
                                    @click="canReview = false"
                                >
                                    Cancel                                </button>
                            </div>
                        </div>
                    </form>
    </v-form>

<!--
    Otherwise, a traditional form will be provided with a minimal
    set of configurations.
-->
            </div>

            <!-- Product Reviews Container -->
            <div v-else>
                <!-- Review Container Shimmer Effect -->
                <template v-if="isLoading">
                    <!-- Review Section Header -->
<div class="mb-8 flex items-center justify-between gap-4 max-sm:flex-wrap">
    <div class="shimmer h-9 w-[245px]"></div>
</div>

<div class="flex gap-16 max-lg:flex-wrap">
    <!-- Left Section -->
    <div class="flex flex-col gap-6">
        <div class="flex flex-col items-center gap-2">
            <div class="shimmer h-12 w-16"></div>
            
            <div class="flex items-center gap-0.5">
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
            </div>

            <div class="shimmer h-6 w-20"></div>
        </div>

        <!-- Ratings By Individual Stars -->
        <div class="grid max-w-[365px] flex-wrap gap-y-3">
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                    </div>
    </div>

    <!-- Right Section -->
    <div class="flex w-full flex-col gap-5">
        <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    </div>
</div>                </template>

                <!-- Reviews Cards Container -->
                <template v-else>
                    <template v-if="reviews.length">
                        <h3 class="mb-8 font-dmserif text-3xl max-md:mb-2.5 max-md:text-2xl max-sm:text-xl">
                            Customer Reviews
                            (0)
                        </h3>
                        
                        <div class="flex gap-16 max-lg:flex-wrap max-sm:gap-5 max-sm:gap-x-0">
                            <!-- Left Section -->
                            <div class="sticky top-24 flex h-max flex-col gap-6 max-lg:relative max-lg:top-auto max-md:w-full">
                                
                                <div class="flex flex-col items-center gap-2 max-md:mt-3 max-md:gap-0 max-md:border-b max-md:border-zinc-200 max-md:pb-3">
                                    <p class="text-5xl max-md:text-3xl">
                                        0.0
                                    </p>
                                    
                                    <div class="flex items-center gap-0.5">
                                                                                    <span class="icon-star-fill text-3xl text-zinc-500"></span>
                                                                                    <span class="icon-star-fill text-3xl text-zinc-500"></span>
                                                                                    <span class="icon-star-fill text-3xl text-zinc-500"></span>
                                                                                    <span class="icon-star-fill text-3xl text-zinc-500"></span>
                                                                                    <span class="icon-star-fill text-3xl text-zinc-500"></span>
                                                                            </div>

                                    <p class="text-base text-zinc-500 max-sm:text-sm">
                                        0

                                        Ratings                                    </p>
                                </div>

                                <!-- Ratings By Individual Stars -->
                                <div class="grid max-w-[365px] flex-wrap gap-y-3 max-md:max-w-full">
                                                                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-md:grid-cols-[0.5fr_2fr] max-sm:flex-wrap max-sm:gap-0">
                                            <div class="whitespace-nowrap text-base font-medium max-sm:text-sm">5 Stars</div>

                                            <div class="h-4 w-[275px] max-w-full rounded-sm bg-neutral-200 max-sm:h-3.5 max-sm:w-full">
                                                <div
                                                    class="h-4 rounded-sm bg-amber-500 max-sm:h-3.5"
                                                    style="width: 0%"
                                                ></div>
                                            </div>
                                        </div>
                                                                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-md:grid-cols-[0.5fr_2fr] max-sm:flex-wrap max-sm:gap-0">
                                            <div class="whitespace-nowrap text-base font-medium max-sm:text-sm">4 Stars</div>

                                            <div class="h-4 w-[275px] max-w-full rounded-sm bg-neutral-200 max-sm:h-3.5 max-sm:w-full">
                                                <div
                                                    class="h-4 rounded-sm bg-amber-500 max-sm:h-3.5"
                                                    style="width: 0%"
                                                ></div>
                                            </div>
                                        </div>
                                                                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-md:grid-cols-[0.5fr_2fr] max-sm:flex-wrap max-sm:gap-0">
                                            <div class="whitespace-nowrap text-base font-medium max-sm:text-sm">3 Stars</div>

                                            <div class="h-4 w-[275px] max-w-full rounded-sm bg-neutral-200 max-sm:h-3.5 max-sm:w-full">
                                                <div
                                                    class="h-4 rounded-sm bg-amber-500 max-sm:h-3.5"
                                                    style="width: 0%"
                                                ></div>
                                            </div>
                                        </div>
                                                                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-md:grid-cols-[0.5fr_2fr] max-sm:flex-wrap max-sm:gap-0">
                                            <div class="whitespace-nowrap text-base font-medium max-sm:text-sm">2 Stars</div>

                                            <div class="h-4 w-[275px] max-w-full rounded-sm bg-neutral-200 max-sm:h-3.5 max-sm:w-full">
                                                <div
                                                    class="h-4 rounded-sm bg-amber-500 max-sm:h-3.5"
                                                    style="width: 0%"
                                                ></div>
                                            </div>
                                        </div>
                                                                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-md:grid-cols-[0.5fr_2fr] max-sm:flex-wrap max-sm:gap-0">
                                            <div class="whitespace-nowrap text-base font-medium max-sm:text-sm">1 Stars</div>

                                            <div class="h-4 w-[275px] max-w-full rounded-sm bg-neutral-200 max-sm:h-3.5 max-sm:w-full">
                                                <div
                                                    class="h-4 rounded-sm bg-amber-500 max-sm:h-3.5"
                                                    style="width: 0%"
                                                ></div>
                                            </div>
                                        </div>
                                                                    </div>

                                <!-- Create Button -->
                                                                                                            <div
                                            class="flex cursor-pointer items-center justify-center gap-x-4 rounded-xl border border-navyBlue px-4 py-3 max-sm:rounded-lg max-sm:py-1.5"
                                            @click="canReview = true"
                                        >
                                            <span class="icon-pen text-2xl"></span>

                                            Write a Review                                        </div>
                                                                                                </div>

                            <!-- Right Section -->
                            <div class="flex w-full flex-col gap-5">
                                <!-- Product Review Item Vue Component -->
                                <v-product-review-item
                                    v-for='review in reviews'
                                    :review="review"
                                ></v-product-review-item>

                                <button
                                    class="mx-auto block w-max rounded-2xl border border-navyBlue bg-white px-11 py-3 text-center text-base font-medium text-navyBlue"
                                    v-if="links?.next"
                                    @click="get()"
                                >
                                    Load More                                </button>
                            </div>
                        </div>
                    </template>

                    <!-- Empty Review Section -->
                    <template v-else>
                        <div class="m-auto grid h-[476px] w-full place-content-center items-center justify-items-center text-center max-md:h-60">
                            <img
                                class="max-md:h-32 max-md:w-32 max-sm:h-[100px] max-sm:w-[100px]"
                                src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/review-ZWqERyaT.png"
                                alt=""
                                title=""
                            >

                            <p class="text-xl max-md:text-sm max-sm:text-xs">
                                No Review found, Be the first to review this product                            </p>
                        
                                                                                                <div
                                        class="mt-8 flex cursor-pointer items-center gap-x-4 rounded-xl border border-navyBlue px-4 py-2.5 max-sm:mt-5 max-sm:gap-x-1.5 max-sm:rounded-lg max-sm:py-1.5 max-sm:text-sm"
                                        @click="canReview = true"
                                    >
                                        <span class="icon-pen text-2xl max-sm:text-lg"></span>

                                        Write a Review                                    </div>
                                                                                    </div>
                    </template>
                </template>
            </div>
        </div>
    </script>

    <!-- Product Review Item Template -->
    <script
        type="text/x-template"
        id="v-product-review-item-template"
    >
        <div class="rounded-xl border border-zinc-200 p-6 max-md:hidden">
            <div class="flex gap-5">
                <template v-if="review.profile">
                    <img
                        class="flex max-h-[100px] min-h-[100px] min-w-[100px] max-w-[100px] items-center justify-center rounded-xl"
                        :src="review.profile"
                        :alt="review.name"
                        :title="review.name"
                    >
                </template>

                <template v-else>
                    <div
                        class="flex max-h-[100px] min-h-[100px] min-w-[100px] max-w-[100px] items-center justify-center rounded-xl bg-zinc-100"
                        :title="review.name"
                    >
                        <span class="text-2xl font-semibold text-zinc-500">
                            {{ review.name.split(' ').map(name => name.charAt(0).toUpperCase()).join('') }}
                        </span>
                    </div>
                </template>
            
                <div class="flex flex-col">
                    <p class="font x-md:text-lg text-xl">
                        {{ review.name }}
                    </p>
                    
                    <p class="mb-2 text-sm font-medium text-neutral-500">
                        {{ review.created_at }}
                    </p>

                    <div class="flex items-center gap-0.5">
                        <span
                            class="icon-star-fill text-3xl"
                            v-for="rating in [1,2,3,4,5]"
                            :class="review.rating >= rating ? 'text-amber-500' : 'text-zinc-500'"
                        ></span>
                    </div>
                </div>
            </div>

            <div class="mt-3 flex flex-col gap-4">
                <p class="text-base max-sm:text-xs">
                    {{ review.title }}
                </p>

                <p class="text-base leading-relaxed text-neutral-500 max-sm:text-xs">
                    {{ review.comment }}
                </p>

                                
                <!-- Review Attachments -->
                <div
                    class="mt-3 flex flex-wrap gap-2"
                    v-if="review.images.length"
                >
                    <template v-for="(file, index) in review.images">
                        <div
                            :href="file.url"
                            class="flex h-12 w-12"
                            target="_blank"
                            v-if="file.type == 'image'"
                        >
                            <img
                                class="max-h-[50px] min-w-[50px] cursor-pointer rounded-xl"
                                :src="file.url"
                                :alt="review.name"
                                :title="review.name"
                                @click="isImageZooming = !isImageZooming; activeIndex = index"
                            >
                        </div>
                        
                        <div
                            :href="file.url"
                            class="flex h-12 w-12"
                            target="_blank"
                            v-else
                        >
                            <video
                                class="max-h-[50px] min-w-[50px] cursor-pointer rounded-xl"
                                :src="file.url"
                                :alt="review.name"
                                :title="review.name"
                                @click="isImageZooming = !isImageZooming; activeIndex = index"
                            >
                            </video>
                        </div>
                    </template>
                </div>

                <!-- Review Images zoomer -->
                <v-gallery-zoomer :attachments="attachments" :is-image-zooming="isImageZooming" :initial-index="'file_'+activeIndex"></v-gallery-zoomer>

            </div>
        </div>

        <!-- For Mobile View -->
        <div class="md:hidden">
            <div class="grid gap-1.5 rounded-xl border border-zinc-200 p-4 max-md:mb-0">
                <div class="flex items-center gap-2.5">
                    <img
                        v-if="review.profile"
                        class="flex max-h-10 min-h-10 min-w-10 max-w-10 items-center justify-center rounded-full"
                        :src="review.profile"
                        :alt="review.name"
                        :title="review.name"
                    >
    
                    <div
                        v-else
                        class="flex max-h-10 min-h-10 min-w-10 max-w-10 items-center justify-center rounded-full bg-zinc-100"
                        :title="review.name"
                    >
                        <span class="text-xs font-semibold text-zinc-500">
                            {{ review.name.split(' ').map(name => name.charAt(0).toUpperCase()).join('') }}
                        </span>
                    </div>
    
                    <div class="grid grid-cols-1">
                        <p class="text-base font-medium">
                            {{ review.name }}
                        </p>
                        
                        <p class="text-xs text-zinc-500">
                            {{ review.created_at }}
                        </p>
                    </div>
                </div>

                <div class="flex items-center">
                                            <span class="icon-star-fill text-xl text-zinc-500"></span>
                                            <span class="icon-star-fill text-xl text-zinc-500"></span>
                                            <span class="icon-star-fill text-xl text-zinc-500"></span>
                                            <span class="icon-star-fill text-xl text-zinc-500"></span>
                                            <span class="icon-star-fill text-xl text-zinc-500"></span>
                                    </div>
    
                <div class="w-full">
                    <p class="text-sm font-semibold">
                        {{ review.title }}
                    </p>
    
                    <p class="mt-1.5 text-sm text-zinc-500">
                        {{ review.comment }}
                    </p>

                                    </div>
    
                <!-- Review Attachments -->
                <div
                    class="journal-scroll scrollbar-width-hidden mt-3 flex gap-2 overflow-auto"
                    v-if="review.images.length"
                >
                    <template v-for="file in review.images">
                        <a
                            :href="file.url"
                            class="flex h-20 w-20"
                            target="_blank"
                            v-if="file.type == 'image'"
                        >
                            <img
                                class="max-h-20 min-w-20 cursor-pointer rounded-xl"
                                :src="file.url"
                                :alt="review.name"
                                :title="review.name"
                            >
                        </a>
    
                        <a
                            :href="file.url"
                            class="flex h-20 w-20"
                            target="_blank"
                            v-else
                        >
                            <video
                                class="max-h-20 min-w-20 cursor-pointer rounded-xl"
                                :src="file.url"
                                :alt="review.name"
                                :title="review.name"
                            >
                            </video>
                        </a>
                    </template>
                </div>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-product-reviews', {
            template: '#v-product-reviews-template',

            data() {
                return {
                    isLoading: true,
                    
                    appliedRatings: 5,

                    canReview: false,

                    reviews: [],

                    links: {
                        next: 'http://localhost/onlinestore/bagisto-2.3/public/api/product/2/reviews',
                    },

                    meta: {},
                }
            },

            mounted() {
                this.get();
            },

            methods: {
                get() {
                    if (! this.links?.next) {
                        return;
                    }
                    
                    this.$axios.get(this.links.next)
                        .then(response => {
                            this.isLoading = false;

                            this.reviews = [...this.reviews, ...response.data.data];

                            this.links = response.data.links;

                            this.meta = response.data.meta;
                        })
                        .catch(error => {});
                },

                store(params, { resetForm, setErrors }) {
                    let selectedFiles = this.$refs.reviewImages.uploadedFiles
                        .filter(obj => obj.file instanceof File)
                        .map(obj => obj.file);

                    params.attachments = selectedFiles;

                    this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/api/product/2/review', params, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            }
                        })
                        .then(response => {
                            this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });

                            resetForm();

                            this.canReview = false;
                        })
                        .catch(error => {
                            setErrors({'attachments': ["The image failed to upload"]});

                            this.$refs.reviewImages.uploadedFiles.forEach(element => {
                                setTimeout(() => {
                                    this.$refs.reviewImages.removeFile();
                                }, 0);
                            });
                        });
                },
            },
        });
        
        app.component('v-product-review-item', {
            template: '#v-product-review-item-template',

            props: ['review'],

            data() {
                return {
                    isLoading: false,

                    isImageZooming: false,

                    activeIndex: 0,
                }
            },

            computed: {
                attachments() {
                    let data = [...this.review.images].map((file) => {
                        return {
                            url: file.url,
                            type: file.type,
                        }
                    });

                    return data;
                },
            },

            methods: {
                translate() {
                    this.isLoading = true;

                    this.$axios.get("http://localhost/onlinestore/bagisto-2.3/public/api/product/2/reviews/:reviewId/translate".replace(':reviewId', this.review.id))
                        .then(response => {
                            this.isLoading = false;

                            this.review.comment = response.data.content;
                        })
                        .catch(error => {
                            this.isLoading = false;

                            this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });
                        });
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-tabs-template"
    >
        <div>
            <div
                class="flex flex-row justify-center gap-8 bg-zinc-100 max-sm:gap-1.5"
                :style="positionStyles"
            >
                <div
                    role="button"
                    tabindex="0"
                    v-for="tab in tabs"
                    class="cursor-pointer px-8 py-5 text-xl font-medium text-zinc-600 max-md:px-4 max-md:py-3 max-md:text-sm max-sm:px-2.5 max-sm:py-2.5"
                    :class="{'border-b-2 border-navyBlue !text-black transition': tab.isActive }"
                    :id="tab.$attrs.id + '-button'"
                    @click="change(tab)"
                >
                    {{ tab.title }}
                </div>
            </div>

            <div>
                <!-- Description Tab -->
                

                <v-tab-item
    title="Description"
    is-selected="1"
    class="p-5 max-1180:px-5 container mt-[60px] !p-0" id="descritpion-tab"
>
    <template v-slot>
        <div class="container mt-[60px] max-1180:px-5">
                        <p class="text-lg text-zinc-500 max-1180:text-sm">
                            The Arctic Bliss Winter Scarf is more than just a cold-weather accessory; it's a statement of warmth, comfort, and style for the winter season. Crafted with care from a luxurious blend of acrylic and wool, this scarf is designed to keep you cozy and snug even in the chilliest temperatures. The soft and plush texture not only provides insulation against the cold but also adds a touch of luxury to your winter wardrobe. The design of the Arctic Bliss Winter Scarf is both stylish and versatile, making it a perfect addition to a variety of winter outfits. Whether you're dressing up for a special occasion or adding a chic layer to your everyday look, this scarf complements your style effortlessly. The extra-long length of the scarf offers customizable styling options. Wrap it around for added warmth, drape it loosely for a casual look, or experiment with different knots to express your unique style. This versatility makes it a must-have accessory for the winter season. Looking for the perfect gift? The Arctic Bliss Winter Scarf is an ideal choice. Whether you're surprising a loved one or treating yourself, this scarf is a timeless and practical gift that will be cherished throughout the winter months. Embrace the winter with the Arctic Bliss Winter Scarf, where warmth meets style in perfect harmony. Elevate your winter wardrobe with this essential accessory that not only keeps you warm but also adds a touch of sophistication to your cold-weather ensemble.
                        </p>
                    </div>
    </template>
</v-tab-item>


                

                <!-- Additional Information Tab -->
                
                <!-- Reviews Tab -->
                <v-tab-item
    title="Reviews"
    is-selected=""
    class="p-5 max-1180:px-5 container mt-[60px] !p-0" id="review-tab"
>
    <template v-slot>
        <v-product-reviews>
    <div class="container max-1180:px-5">
        <!-- Review Section Header -->
<div class="mb-8 flex items-center justify-between gap-4 max-sm:flex-wrap">
    <div class="shimmer h-9 w-[245px]"></div>
</div>

<div class="flex gap-16 max-lg:flex-wrap">
    <!-- Left Section -->
    <div class="flex flex-col gap-6">
        <div class="flex flex-col items-center gap-2">
            <div class="shimmer h-12 w-16"></div>
            
            <div class="flex items-center gap-0.5">
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
                <span class="shimmer h-[30px] w-[30px]"></span>
            </div>

            <div class="shimmer h-6 w-20"></div>
        </div>

        <!-- Ratings By Individual Stars -->
        <div class="grid max-w-[365px] flex-wrap gap-y-3">
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                            <div class="row grid grid-cols-[1fr_2fr] items-center gap-4 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-[56px]"></div>

                    <div class="shimmer h-4 w-[275px] rounded-sm"></div>
                </div>
                    </div>
    </div>

    <!-- Right Section -->
    <div class="flex w-full flex-col gap-5">
        <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    <div class="rounded-xl border border-zinc-200 p-6">
        <div class="flex gap-5">
            <div class="shimmer h-[100px] w-[100px] rounded-xl"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-7 w-40"></p
>
                <p class="shimmer mb-2 h-4 w-40"></p>

                <div class="flex items-center gap-0.5">
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                    <span class="shimmer h-[30px] w-[30px]"></span>
                </div>
            </div>
        </div>

        <div class="mt-3 flex flex-col gap-4">
            <div class="shimmer h-6 w-[250px]"></div>

            <div class="flex flex-col gap-0.5">
                <p class="shimmer h-6 w-[500px]"></p>
                <p class="shimmer h-6 w-[300px]"></p>
            </div>
        </div>
    </div>
    </div>
</div>    </div>
</v-product-reviews>
    </template>
</v-tab-item>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-tabs', {
            template: '#v-tabs-template',

            props: ['position'],

            data() {
                return {
                    tabs: []
                }
            },

            computed: {
                positionStyles() {
                    return [
                        `justify-content: ${this.position}`
                    ];
                },
            },

            methods: {
                change(selectedTab) {
                    this.tabs.forEach(tab => {
                        tab.isActive = (tab.title == selectedTab.title);
                    });
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-accordion-template"
    >
        <div>
            <slot
                name="header"
                :toggle="toggle"
                :isOpen="isOpen"
            >
                admin::app.components.accordion.default-content            </slot>

            <slot
                name="content"
                :isOpen="isOpen"
            >
                admin::app.components.accordion.default-content            </slot>
        </div>
    </script>

    <script type="module">
        app.component('v-accordion', {
            template: '#v-accordion-template',

            props: [
                'isActive',
            ],

            data() {
                return {
                    isOpen: this.isActive,
                };
            },

            methods: {
                toggle() {
                    this.isOpen = ! this.isOpen;

                    this.$emit('toggle', { isActive: this.isOpen });
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-products-carousel-template"
    >
        <div
            class="container mt-20 max-lg:px-8 max-md:mt-8 max-sm:mt-7 max-sm:!px-4"
            v-if="! isLoading && products.length"
        >
            <div class="flex justify-between">
                <h2 class="font-dmserif text-3xl max-md:text-2xl max-sm:text-xl">
                    {{ title }}
                </h2>

                <div class="flex items-center justify-between gap-8">
                    <a
                        :href="navigationLink"
                        class="hidden max-lg:flex"
                        v-if="navigationLink"
                    >
                        <p class="items-center text-xl max-md:text-base max-sm:text-sm">
                            View All
                            <span class="icon-arrow-right text-2xl max-md:text-lg max-sm:text-sm"></span>
                        </p>
                    </a>

                    <template v-if="products.length > 3">
                        <span
                            v-if="products.length > 4 || (products.length > 3 && isScreenMax2xl)"
                            class="icon-arrow-left-stylish rtl:icon-arrow-right-stylish inline-block cursor-pointer text-2xl max-lg:hidden"
                            role="button"
                            aria-label="Previous"
                            tabindex="0"
                            @click="swipeLeft"
                        >
                        </span>

                        <span
                            v-if="products.length > 4 || (products.length > 3 && isScreenMax2xl)"
                            class="icon-arrow-right-stylish rtl:icon-arrow-left-stylish inline-block cursor-pointer text-2xl max-lg:hidden"
                            role="button"
                            aria-label="Next"
                            tabindex="0"
                            @click="swipeRight"
                        >
                        </span>
                    </template>
                </div>
            </div>

            <div
                ref="swiperContainer"
                class="flex gap-8 pb-2.5 [&>*]:flex-[0] mt-10 overflow-auto scroll-smooth scrollbar-hide max-md:gap-7 max-md:mt-5 max-sm:gap-4 max-md:pb-0 max-md:whitespace-nowrap"
            >
                <v-product-card
    class="min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]" v-for="product in products"
    :product="product"
>
</v-product-card>

            </div>

            <a
                :href="navigationLink"
                class="secondary-button mx-auto mt-5 block w-max rounded-2xl px-11 py-3 text-center text-base max-lg:mt-0 max-lg:hidden max-lg:py-3.5 max-md:rounded-lg"
                :aria-label="title"
                v-if="navigationLink"
            >
                View All            </a>
        </div>

        <!-- Product Card Listing -->
        <template v-if="isLoading">
            <div class="container mt-20 max-lg:px-8 max-md:mt-8 max-sm:mt-7 max-sm:!px-4">
    <div class="flex items-center justify-between">
        <h3 class="shimmer h-8 w-[200px] max-sm:h-7"></h3>

        <div class="flex items-center justify-between gap-8 max-lg:hidden">
            <span
                class="shimmer inline-block h-6 w-6"
                role="presentation"
            ></span>

            <span
                class="shimmer inline-block h-6 w-6 max-sm:hidden"
                role="presentation"
            ></span>
        </div>

        <div class="shimmer h-7 w-24 max-sm:h-5 max-sm:w-[68px] lg:hidden"></div>
    </div>

    <div class="scrollbar-hide mt-10 flex gap-8 overflow-auto pb-2.5 max-md:mt-5 max-sm:gap-4">
        <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px]">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    </div>

    </div>
        </template>
    </script>

    <script type="module">
        app.component('v-products-carousel', {
            template: '#v-products-carousel-template',

            props: [
                'src',
                'title',
                'navigationLink',
            ],

            data() {
                return {
                    isLoading: true,

                    products: [],

                    offset: 323,

                    isScreenMax2xl: window.innerWidth <= 1440,
                };
            },

            mounted() {
                this.getProducts();
            },

            created() {
                window.addEventListener('resize', this.updateScreenSize);
            },

            beforeDestroy() {
                window.removeEventListener('resize', this.updateScreenSize);
            },

            methods: {
                getProducts() {
                    this.$axios.get(this.src)
                        .then(response => {
                            this.isLoading = false;

                            this.products = response.data.data;
                        }).catch(error => {
                            console.log(error);
                        });
                },

                updateScreenSize() {
                    this.isScreenMax2xl = window.innerWidth <= 1440;
                },

                swipeLeft() {
                    const container = this.$refs.swiperContainer;

                    container.scrollLeft -= this.offset;
                },

                swipeRight() {
                    const container = this.$refs.swiperContainer;

                    // Check if scroll reaches the end
                    if (container.scrollLeft + container.clientWidth >= container.scrollWidth) {
                        // Reset scroll to the beginning
                        container.scrollLeft = 0;
                    } else {
                        // Scroll to the right
                        container.scrollLeft += this.offset;
                    }
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-product-gallery-template"
    >
        <div>
            <!-- Desktop Gallery -->
            <!-- For large screens greater than 1180px. -->
<div class="sticky top-20 flex h-max gap-8 max-1180:hidden">
    <!-- Product Image and Videos Slider -->
    <div class="flex-24 h-509 flex min-w-[100px] max-w-[100px] flex-wrap place-content-start justify-center gap-2.5 overflow-y-auto overflow-x-hidden">
        <!-- Arrow Up -->
        <span
            class="icon-arrow-up cursor-pointer text-2xl"
            role="button"
            aria-label="Previous"
            tabindex="0"
            @click="swipeDown"
            v-if="lengthOfMedia"
        >
        </span>

        <!-- Swiper Container -->
        <div
            ref="swiperContainer"
            class="flex flex-col max-h-[540px] gap-2.5 [&>*]:flex-[0] overflow-auto scroll-smooth scrollbar-hide"
        >
            <template v-for="(media, index) in [...media.images, ...media.videos]">
                <video
                    v-if="media.type == 'videos'"
                    :class="`transparent max-h-[100px] min-w-[100px] cursor-pointer rounded-xl border ${isActiveMedia(index) ? 'pointer-events-none border-navyBlue' : 'border-white'}`"
                    @click="change(media, index)"
                    alt="Arctic Bliss Stylish Winter Scarf"
                    tabindex="0"
                >
                    <source
                        :src="media.video_url"
                        type="video/mp4"
                    />
                </video>

                <img
                    v-else
                    :class="`transparent max-h-[100px] min-w-[100px] cursor-pointer rounded-xl border ${isActiveMedia(index) ? 'pointer-events-none border border-navyBlue' : 'border-white'}`"
                    :src="media.small_image_url"
                    alt="Arctic Bliss Stylish Winter Scarf"
                    width="100"
                    height="100"
                    tabindex="0"
                    @click="change(media, index)"
                />
            </template>
        </div>

        <!-- Arrow Down -->
        <span
            class="icon-arrow-down cursor-pointer text-2xl"
            v-if= "lengthOfMedia"
            role="button"
            aria-label="Previous"
            tabindex="0"
            @click="swipeTop"
        >
        </span>
    </div>

    <!-- Product Base Image and Video with Shimmer-->
    <div
        class="max-h-[610px] max-w-[560px]"
        v-show="isMediaLoading"
    >
        <div class="shimmer min-h-[607px] min-w-[560px] rounded-xl bg-zinc-200"></div>
    </div>

    <div
        class="max-h-[610px] max-w-[560px]"
        v-show="! isMediaLoading"
    >
        <img
            class="min-w-[450px] cursor-pointer rounded-xl"
            :src="baseFile.path"
            v-if="baseFile.type == 'image'"
            alt="Arctic Bliss Stylish Winter Scarf"
            width="560"
            height="610"
            tabindex="0"
            @click="isImageZooming = !isImageZooming"
            @load="onMediaLoad()"
            fetchpriority="high"
        />

        <div
            class="min-w-[450px] cursor-pointer rounded-xl"
            tabindex="0"
            v-if="baseFile.type == 'video'"
        >
            <video
                controls
                width="475"
                alt="Arctic Bliss Stylish Winter Scarf"
                @click="isImageZooming = !isImageZooming"
                @loadeddata="onMediaLoad()"
                :key="baseFile.path"
            >
                <source
                    :src="baseFile.path"
                    type="video/mp4"
                />
            </video>
        </div>
    </div>
</div>

            <!-- Mobile Gallery -->
            <div
    class="overflow-hidden 1180:hidden"
    v-if="isMediaLoading"
>
    <div class="shimmer aspect-square max-h-screen w-screen bg-zinc-200"></div>
</div>

<div
    class="scrollbar-hide flex w-screen gap-8 overflow-auto max-sm:gap-5 1180:hidden"
    v-else
>
    <v-product-carousel
        :options="[
            ...media.images,
            ...media.videos
        ]"
        @click="isImageZooming = ! isImageZooming"
    >
        <div class="sticky top-8 flex h-max gap-8 max-1180:hidden">
    <div class="flex max-h-[100px] max-w-[100px] flex-wrap gap-2.5">
        <div class="flex-24 h-509 flex max-w-[100px] flex-wrap place-content-start justify-center gap-2.5">
            <span class="shimmer h-6 w-6 text-2xl"></span>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <span class="shimmer h-6 w-6 text-2xl"></span>
        </div>
    </div>
    
    <div class="max-h-[610px] max-w-[560px]">
        <div class="shimmer min-h-[607px] min-w-[560px] rounded-xl"></div>
    </div>
</div>

<div class="overflow-hidden 1180:hidden">
    <div class="shimmer aspect-square max-h-screen w-screen"></div>
</div>    </v-product-carousel>
</div>

            
            <!-- Gallery Images Zoomer -->
            <v-gallery-zoomer :attachments="attachments" :is-image-zooming="isImageZooming" :initial-index="`media_${activeIndex}`"></v-gallery-zoomer>

        </div>
    </script>

    <script type="module">
        app.component('v-product-gallery', {
            template: '#v-product-gallery-template',

            data() {
                return {
                    isImageZooming: false,

                    isMediaLoading: true,

                    media: {
                        images: [{"small_image_url":"http:\/\/localhost\/onlinestore\/bagisto-2.3\/public\/cache\/small\/product\/2\/BURS21o4NmmkrUhP2sebQrdigeA8sVS5xNapyIVS.webp","medium_image_url":"http:\/\/localhost\/onlinestore\/bagisto-2.3\/public\/cache\/medium\/product\/2\/BURS21o4NmmkrUhP2sebQrdigeA8sVS5xNapyIVS.webp","large_image_url":"http:\/\/localhost\/onlinestore\/bagisto-2.3\/public\/cache\/large\/product\/2\/BURS21o4NmmkrUhP2sebQrdigeA8sVS5xNapyIVS.webp","original_image_url":"http:\/\/localhost\/onlinestore\/bagisto-2.3\/public\/cache\/original\/product\/2\/BURS21o4NmmkrUhP2sebQrdigeA8sVS5xNapyIVS.webp"}],

                        videos: [],
                    },

                    baseFile: {
                        type: '',

                        path: ''
                    },

                    activeIndex: 0,

                    containerOffset: 110,
                };
            },

            watch: {
                'media.images': {
                    deep: true,

                    handler(newImages, oldImages) {
                        let selectedImage = newImages?.[this.activeIndex];

                        if (JSON.stringify(newImages) !== JSON.stringify(oldImages) && selectedImage?.large_image_url) {
                            this.baseFile.path = selectedImage.large_image_url;
                        }
                    },
                },
            },
        
            mounted() {
                if (this.media.images.length) {

                    this.baseFile.type = 'image';

                    this.baseFile.path = this.media.images[0].large_image_url;
                } else if (this.media.videos.length) {

                    this.baseFile.type = 'video';

                    this.baseFile.path = this.media.videos[0].video_url;
                }
            },

            computed: {
                lengthOfMedia() {
                    if (this.media.images.length) {
                        return [...this.media.images, ...this.media.videos].length > 5;
                    }
                },

                attachments() {
                    return [...this.media.images, ...this.media.videos].map(media => ({
                        url: media.type === 'videos' ? media.video_url : media.original_image_url,
                        
                        type: media.type === 'videos' ? 'video' : 'image',
                    }));
                },
            },

            methods: {
                isActiveMedia(index) {
                    return index === this.activeIndex;
                },
                
                onMediaLoad() {
                    this.isMediaLoading = false;
                },

                change(media, index) {
                    this.isMediaLoading = true;

                    if (media.type == 'videos') {
                        this.baseFile.type = 'video';

                        this.baseFile.path = media.video_url;

                        this.onMediaLoad();
                    } else {
                        this.baseFile.type = 'image';

                        this.baseFile.path = media.large_image_url;
                    }

                    if (index > this.activeIndex) {
                        this.swipeDown();
                    } else if (index < this.activeIndex) {
                        this.swipeTop();
                    }

                    this.activeIndex = index;
                },

                swipeTop() {
                    const container = this.$refs.swiperContainer;

                    container.scrollTop -= this.containerOffset;
                },

                swipeDown() {
                    const container = this.$refs.swiperContainer;

                    container.scrollTop += this.containerOffset;
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-quantity-changer-template"
    >
        <div>
            <span 
                class="icon-minus cursor-pointer text-2xl"
                role="button"
                tabindex="0"
                aria-label="Decrease Quantity"
                @click="decrease"
            >
            </span>

            <p class="w-2.5 select-none text-center max-sm:text-sm">
                {{ quantity }}
            </p>
            
            <span 
                class="icon-plus cursor-pointer text-2xl"
                role="button"
                tabindex="0"
                aria-label="Increase Quantity"
                @click="increase"
            >
            </span>

            <v-field
                type="hidden"
                :name="name"
                v-model="quantity"
            ></v-field>
        </div>
    </script>

    <script type="module">
        app.component("v-quantity-changer", {
            template: '#v-quantity-changer-template',

            props:['name', 'value', 'minValue'],

            data() {
                return  {
                    quantity: this.value,
                }
            },

            watch: {
                value() {
                    this.quantity = this.value;
                },
            },

            methods: {
                increase() {
                    this.$emit('change', ++this.quantity);
                },

                decrease() {
                    if (this.quantity > this.minValue) {
                        this.quantity -= 1;

                        this.$emit('change', this.quantity);
                    }
                },
            }
        });
    </script>
    <script
        type="text/x-template"
        id="v-shimmer-image-template"
    >
        <div
            :id="'image-shimmer-' + $.uid"
            class="shimmer"
            v-bind="$attrs"
            v-show="isLoading"
        >
        </div>
        
        <img
            v-bind="$attrs"
            :data-src="src"
            :id="'image-' + $.uid"
            @load="onLoad"
            v-show="! isLoading"
            v-if="lazy"
        >

        <img
            v-bind="$attrs"
            :data-src="src"
            :id="'image-' + $.uid"
            @load="onLoad"
            v-else
            v-show="! isLoading"
        >
    </script>

    <script type="module">
        app.component('v-shimmer-image', {
            template: '#v-shimmer-image-template',

            props: {
                lazy: {
                    type: Boolean, 
                    default: true,
                },

                src: {
                    type: String, 
                    default: '',
                },
            },

            data() {
                return {
                    isLoading: true,
                };
            },

            mounted() {
                let self = this;

                if (! this.lazy) {
                    return;
                }
                
                let lazyImageObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            let lazyImage = document.getElementById('image-' + self.$.uid);

                            lazyImage.src = lazyImage.dataset.src;

                            lazyImageObserver.unobserve(lazyImage);
                        }
                    });
                });

                lazyImageObserver.observe(document.getElementById('image-shimmer-' + this.$.uid));
            },
            
            methods: {
                onLoad() {
                    this.isLoading = false;
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-desktop-category-template"
    >
        <!-- Loading State -->
        <div
            class="flex items-center gap-5"
            v-if="isLoading"
        >
            <span
                class="shimmer h-6 w-20 rounded"
                role="presentation"
            ></span>

            <span
                class="shimmer h-6 w-20 rounded"
                role="presentation"
            ></span>

            <span
                class="shimmer h-6 w-20 rounded"
                role="presentation"
            ></span>
        </div>

        <!-- Default category layout -->
        <div
            class="flex items-center"
            v-else-if="'default' !== 'sidebar'"
        >
            <div
                class="group relative flex h-[77px] items-center border-b-4 border-transparent hover:border-b-4 hover:border-navyBlue"
                v-for="category in categories"
            >
                <span>
                    <a
                        :href="category.url"
                        class="inline-block px-5 uppercase"
                    >
                        {{ category.name }}
                    </a>
                </span>

                <div
                    class="pointer-events-none absolute top-[78px] z-[1] max-h-[580px] w-max max-w-[1260px] translate-y-1 overflow-auto overflow-x-auto border border-b-0 border-l-0 border-r-0 border-t border-[#F3F3F3] bg-white p-9 opacity-0 shadow-[0_6px_6px_1px_rgba(0,0,0,.3)] transition duration-300 ease-out group-hover:pointer-events-auto group-hover:translate-y-0 group-hover:opacity-100 group-hover:duration-200 group-hover:ease-in ltr:-left-9 rtl:-right-9"
                    v-if="category.children && category.children.length"
                >
                    <div class="flex justify-between gap-x-[70px]">
                        <div
                            class="grid w-full min-w-max max-w-[150px] flex-auto grid-cols-[1fr] content-start gap-5"
                            v-for="pairCategoryChildren in pairCategoryChildren(category)"
                        >
                            <template v-for="secondLevelCategory in pairCategoryChildren">
                                <p class="font-medium text-navyBlue">
                                    <a :href="secondLevelCategory.url">
                                        {{ secondLevelCategory.name }}
                                    </a>
                                </p>

                                <ul
                                    class="grid grid-cols-[1fr] gap-3"
                                    v-if="secondLevelCategory.children && secondLevelCategory.children.length"
                                >
                                    <li
                                        class="text-sm font-medium text-zinc-500"
                                        v-for="thirdLevelCategory in secondLevelCategory.children"
                                    >
                                        <a :href="thirdLevelCategory.url">
                                            {{ thirdLevelCategory.name }}
                                        </a>
                                    </li>
                                </ul>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar category layout -->
        <div v-else>
            <!-- Categories Navigation -->
            <div class="flex items-center">
                <!-- "All" button for opening the category drawer -->
                <div
                    class="flex h-[77px] cursor-pointer items-center border-b-4 border-transparent hover:border-b-4 hover:border-navyBlue"
                    @click="toggleCategoryDrawer"
                >
                    <span class="flex items-center gap-1 px-5 uppercase">
                        <span class="icon-hamburger text-xl"></span>

                        All                    </span>
                </div>

                <!-- Show only first 4 categories in main navigation -->
                <div
                    class="group relative flex h-[77px] items-center border-b-4 border-transparent hover:border-b-4 hover:border-navyBlue"
                    v-for="category in categories.slice(0, 4)"
                >
                    <span>
                        <a
                            :href="category.url"
                            class="inline-block px-5 uppercase"
                        >
                            {{ category.name }}
                        </a>
                    </span>

                    <!-- Dropdown for each category -->
                    <div
                        class="pointer-events-none absolute top-[78px] z-[1] max-h-[580px] w-max max-w-[1260px] translate-y-1 overflow-auto overflow-x-auto border border-b-0 border-l-0 border-r-0 border-t border-[#F3F3F3] bg-white p-9 opacity-0 shadow-[0_6px_6px_1px_rgba(0,0,0,.3)] transition duration-300 ease-out group-hover:pointer-events-auto group-hover:translate-y-0 group-hover:opacity-100 group-hover:duration-200 group-hover:ease-in ltr:-left-9 rtl:-right-9"
                        v-if="category.children && category.children.length"
                    >
                        <div class="flex justify-between gap-x-[70px]">
                            <div
                                class="grid w-full min-w-max max-w-[150px] flex-auto grid-cols-[1fr] content-start gap-5"
                                v-for="pairCategoryChildren in pairCategoryChildren(category)"
                            >
                                <template v-for="secondLevelCategory in pairCategoryChildren">
                                    <p class="font-medium text-navyBlue">
                                        <a :href="secondLevelCategory.url">
                                            {{ secondLevelCategory.name }}
                                        </a>
                                    </p>

                                    <ul
                                        class="grid grid-cols-[1fr] gap-3"
                                        v-if="secondLevelCategory.children && secondLevelCategory.children.length"
                                    >
                                        <li
                                            class="text-sm font-medium text-zinc-500"
                                            v-for="thirdLevelCategory in secondLevelCategory.children"
                                        >
                                            <a :href="thirdLevelCategory.url">
                                                {{ thirdLevelCategory.name }}
                                            </a>
                                        </li>
                                    </ul>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bagisto Drawer Integration -->
            <v-drawer
    :is-active="isDrawerActive" @toggle="onDrawerToggle" @close="onDrawerClose"
    is-active=""
    position="left"
    width="400px"
>
            <template v-slot:toggle>
            
        </template>
    
            <template v-slot:header="{ close }">
            <div class="grid gap-y-2.5 p-6 pb-5 max-md:gap-y-1.5 max-md:border-b max-md:border-zinc-200 max-md:p-4 max-md:gap-y-1 max-md:font-semibold border-b border-gray-200">
                <div class="flex w-full items-center justify-between">
                        <p class="text-xl font-medium">
                            Categories                        </p>
                    </div>

                <div class="absolute top-5 max-sm:top-4 ltr:right-5 rtl:left-5">
                    <span
                        class="icon-cancel cursor-pointer text-3xl max-md:text-2xl"
                        @click="close"
                    >
                    </span>
                </div>
            </div>
        </template>
    
            <template v-slot:content>
            <div class="flex-1 overflow-auto px-6 max-md:px-4 !px-0">
                <!-- Wrapper with transition effects -->
                    <div class="relative h-full overflow-hidden">
                        <!-- Sliding container -->
                        <div
                            class="flex h-full transition-transform duration-300"
                            :class="{
                                'ltr:translate-x-0 rtl:translate-x-0': currentViewLevel !== 'third',
                                'ltr:-translate-x-full rtl:translate-x-full': currentViewLevel === 'third'
                            }"
                        >
                            <!-- First level view -->
                            <div class="h-[calc(100vh-74px)] w-full flex-shrink-0 overflow-auto">
                                <div class="py-4">
                                    <div
                                        v-for="category in categories"
                                        :key="category.id"
                                        :class="{'mb-2': category.children && category.children.length}"
                                    >
                                        <div class="flex cursor-pointer items-center justify-between px-6 py-2 transition-colors duration-200 hover:bg-gray-100">
                                            <a
                                                :href="category.url"
                                                class="text-base font-medium text-black"
                                            >
                                                {{ category.name }}
                                            </a>
                                        </div>

                                        <!-- Second Level Categories -->
                                        <div v-if="category.children && category.children.length" >
                                            <div
                                                v-for="secondLevelCategory in category.children"
                                                :key="secondLevelCategory.id"
                                            >
                                                <div
                                                    class="flex cursor-pointer items-center justify-between px-6 py-2 transition-colors duration-200 hover:bg-gray-100"
                                                    @click="showThirdLevel(secondLevelCategory, category, $event)"
                                                >
                                                    <a
                                                        :href="secondLevelCategory.url"
                                                        class="text-sm font-normal"
                                                    >
                                                        {{ secondLevelCategory.name }}
                                                    </a>

                                                    <span
                                                        v-if="secondLevelCategory.children && secondLevelCategory.children.length"
                                                        class="icon-arrow-right rtl:icon-arrow-left"
                                                    ></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Third level view -->
                            <div
                                class="h-full w-full flex-shrink-0"
                                v-if="currentViewLevel === 'third'"
                            >
                                <div class="border-b border-gray-200 px-6 py-4">
                                    <button
                                        @click="goBackToMainView"
                                        class="flex items-center justify-center gap-2 focus:outline-none"
                                        aria-label="Go back"
                                    >
                                        <span class="icon-arrow-left rtl:icon-arrow-right text-lg"></span>

                                        <p class="text-base font-medium text-black">
                                            Back to Main Menu                                        </p>
                                    </button>
                                </div>

                                <!-- Third Level Content -->
                                <div class="py-4">
                                    <div
                                        v-for="thirdLevelCategory in currentSecondLevelCategory?.children"
                                        :key="thirdLevelCategory.id"
                                        class="mb-2"
                                    >
                                        <a
                                            :href="thirdLevelCategory.url"
                                            class="block px-6 py-2 text-sm transition-colors duration-200 hover:bg-gray-100"
                                        >
                                            {{ thirdLevelCategory.name }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </template>
    
    </v-drawer>

        </div>
    </script>

    <script type="module">
        app.component('v-desktop-category', {
            template: '#v-desktop-category-template',

            data() {
                return {
                    isLoading: true,
                    categories: [],
                    isDrawerActive: false,
                    currentViewLevel: 'main',
                    currentSecondLevelCategory: null,
                    currentParentCategory: null
                }
            },

            mounted() {
                this.getCategories();
            },

            methods: {
                getCategories() {
                    this.$axios.get("http://localhost/onlinestore/bagisto-2.3/public/api/categories/tree")
                        .then(response => {
                            this.isLoading = false;
                            this.categories = response.data.data;
                        })
                        .catch(error => {
                            console.log(error);
                        });
                },

                pairCategoryChildren(category) {
                    if (! category.children) return [];

                    return category.children.reduce((result, value, index, array) => {
                        if (index % 2 === 0) {
                            result.push(array.slice(index, index + 2));
                        }
                        return result;
                    }, []);
                },

                toggleCategoryDrawer() {
                    this.isDrawerActive = !this.isDrawerActive;
                    if (this.isDrawerActive) {
                        this.currentViewLevel = 'main';
                    }
                },

                onDrawerToggle(event) {
                    this.isDrawerActive = event.isActive;
                },

                onDrawerClose(event) {
                    this.isDrawerActive = false;
                },

                showThirdLevel(secondLevelCategory, parentCategory, event) {
                    if (secondLevelCategory.children && secondLevelCategory.children.length) {
                        this.currentSecondLevelCategory = secondLevelCategory;
                        this.currentParentCategory = parentCategory;
                        this.currentViewLevel = 'third';

                        if (event) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                    }
                },

                goBackToMainView() {
                    this.currentViewLevel = 'main';
                }
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-media-template"
    >
        <div class="mb-4 flex cursor-pointer flex-col rounded-lg">
            <div :class="{'border border-dashed border-gray-300 rounded-2xl': isDragOver }">
                <div
                    class="flex h-[200px] w-[200px] cursor-pointer flex-col items-center justify-center rounded-xl bg-zinc-100 hover:bg-gray-100 max-md:h-36 max-md:w-36 max-sm:h-[100px] max-sm:w-[100px]"
                    v-if="uploadedFiles.isPicked"
                >
                    <div 
                        class="group relative flex h-[200px] w-[200px] max-md:h-36 max-md:w-36 max-sm:h-[100px] max-sm:w-[100px]"
                        @mouseenter="uploadedFiles.showDeleteButton = true"
                        @mouseleave="uploadedFiles.showDeleteButton = false"
                    >
                        <img
                            class="rounded-xl object-cover max-md:rounded-full"
                            :src="uploadedFiles.url"
                            :class="{ 'opacity-25' : uploadedFiles.showDeleteButton }"
                            alt="Uploaded Image"
                        >

                        <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform opacity-0 transition-opacity group-hover:opacity-100">
                            <span 
                                class="icon-bin cursor-pointer text-2xl text-black"
                                @click="remove"
                            >
                            </span>
                        </div>
                    </div>
                </div>

                <label 
                    :for="`${$.uid}_fileInput`"
                    class="flex h-[200px] w-[200px] cursor-pointer flex-col items-center justify-center gap-2 rounded-xl bg-zinc-100 hover:bg-gray-100 max-md:h-36 max-md:w-36 max-sm:h-[100px] max-sm:w-[100px] max-sm:gap-1"
                    :style="{'max-width': this.width, 'max-height': this.height}"
                    v-show="! uploadedFiles.isPicked"
                    @dragover="onDragOver"
                    @dragleave="onDragLeave"
                    @drop="onDrop"
                >
                    <label 
                        :for="`${$.uid}_fileInput`"
                        class="icon-camera text-3xl max-sm:text-lg"
                    >
                    </label>

                    <p class="font-medium max-md:hidden max-sm:text-xs">
                        Add Image/Video                    </p>

                    <input
                        type="hidden"
                        :name="name"
                        v-if="! uploadedFiles.isPicked"
                    />

                    <v-field
                        type="file"
                        class="hidden"
                        :id="`${$.uid}_fileInput`"
                        :name="name"
                        :accept="acceptedTypes"
                        :rules="appliedRules"
                        :multiple="isMultiple"
                        @change="onFileChange"
                    >
                    </v-field>
                </label>
            </div>

            <div 
                class="flex items-center"
                v-if="isMultiple"
            >
                <ul class="justify-left mt-2 flex flex-wrap gap-2.5">
                    <li
                        v-for="(file, index) in uploadedFiles"
                        :key="index"
                    >
                        <template v-if="isImage(file)">
                            <div
                                class="group relative flex h-12 w-12 justify-center max-sm:h-[60px] max-sm:w-[60px]"
                                @mouseenter="file.showDeleteButton = true"
                                @mouseleave="file.showDeleteButton = false"
                            >
                                <img
                                    :src="file.url"
                                    :alt="file.name"
                                    class="max-h-12 min-w-12 rounded-xl max-sm:max-h-[60px] max-sm:min-w-[60px]"
                                    :class="{ 'opacity-25' : file.showDeleteButton }"
                                >
                                <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform opacity-0 transition-opacity group-hover:opacity-100">
                                    <span
                                        class="icon-bin cursor-pointer text-2xl text-black"
                                        @click="remove(index)"
                                    >
                                    </span>
                                </div>
                            </div>
                        </template>

                        <template v-else>
                            <div
                                class="group relative flex h-12 w-12 justify-center max-sm:h-[60px] max-sm:w-[60px]"
                                @mouseenter="file.showDeleteButton = true"
                                @mouseleave="file.showDeleteButton = false"
                            >
                                <video
                                    :src="file.url"
                                    :alt="file.name"
                                    class="max-h-12 min-w-12 rounded-xl max-sm:max-h-[60px] max-sm:min-w-[60px]"
                                    :class="{'opacity-25' : file.showDeleteButton}"
                                >
                                </video>
                                <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform opacity-0 transition-opacity group-hover:opacity-100">
                                    <span 
                                        class="icon-bin cursor-pointer text-2xl text-black"
                                        @click="remove(index)"
                                    >
                                    </span>
                                </div>
                            </div>
                        </template>
                    </li>
                </ul>
            </div>
        </div>
    </script>

    <script type="module">
        app.component("v-media", {
            template: '#v-media-template',

            props: {
                name: {
                    type: String, 
                    default: 'attachments',
                },

                isMultiple: {
                    type: Boolean,
                    default: false,
                },

                rules: {
                    type: String,
                },

                acceptedTypes: {
                    type: String, 
                    default: 'image/*, video/*,'
                },

                label: {
                    type: String, 
                    default: 'Add attachments'
                },

                src: {
                    type: String,
                    default: ''
                },

                height: {
                    type: String,
                    default: '200px',
                },

                width: {
                    type: String,
                    default: '200px',
                },
            },

            data() {
                return {
                    uploadedFiles: [],

                    isDragOver: false,

                    appliedRules: '',
                };
            },

            created() {
                this.appliedRules = this.rules;

                if (this.src != '') {
                    this.appliedRules = '';

                    this.uploadedFiles = {
                        isPicked: true,
                        url: this.src,
                    }
                }
            },

            methods: {
                onFileChange(event) {
                    let files = event.target.files;

                    for (let i = 0; i < files.length; i++) {
                        let file = files[i];

                        let reader = new FileReader();

                        reader.onload = () => {
                            if (! this.isMultiple) {
                                this.uploadedFiles = {
                                    isPicked: true,
                                    name: file.name,
                                    url: reader.result,
                                }

                                return;
                            }

                            this.uploadedFiles.push({
                                name: file.name,
                                url: reader.result,
                                file: new File([file], file.name),
                            });
                        };

                        reader.readAsDataURL(file);
                    }
                },

                handleDroppedFiles(files) {
                    for (let i = 0; i < files.length; i++) {
                        let file = files[i];

                        let reader = new FileReader();
                        
                        reader.onload = () => {
                            if (! this.isMultiple) {
                                this.uploadedFiles = {
                                    isPicked: true,
                                    name: file.name,
                                    url: reader.result,
                                }

                                return;
                            }

                            this.uploadedFiles.push({
                                name: file.name,
                                url: reader.result,
                            });
                        };

                        reader.readAsDataURL(file);
                    }
                },

                isImage(file) {
                    if (! file.name) {
                        return;
                    }

                    return file.name.match(/\.(jpg|jpeg|png|gif)$/i);
                },

                onDragOver(event) {
                    event.preventDefault();

                    this.isDragOver = true;
                },

                onDragLeave(event) {
                    event.preventDefault();

                    this.isDragOver = false;
                },
                
                onDrop(event) {
                    event.preventDefault();

                    this.isDragOver = false;

                    let files = event.dataTransfer.files;

                    this.handleDroppedFiles(files);
                },

                remove(index) {
                    if (! this.isMultiple) {
                        this.uploadedFiles = [];

                        this.appliedRules = this.rules;
                        
                        return;
                    }

                    this.uploadedFiles.splice(index, 1);
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-product-ratings-template"
    >
        <div>
            <span class="text-sm font-medium text-black max-sm:text-xs">
                {{ average }}
            </span>
        
            <span
                class="icon-star-fill -mt-1 text-xl text-amber-500 max-sm:-mt-1 max-sm:text-lg"
                role="presentation"
            >
            </span>
            
            <span class="border-l border-zinc-300 text-sm font-medium text-black max-sm:border-zinc-300 max-sm:text-xs ltr:pl-1 rtl:pr-1">
                {{ abbreviatedTotal }}

                <span v-if="rating">Ratings</span>
            </span>
        </div>
    </script>

    <script type="module">
        app.component("v-product-ratings", {
            template: "#v-product-ratings-template",

            props: {
                average: {
                    type: String,
                    required: true,
                },

                total: {
                    type: String,
                    required: true,
                },

                rating: {
                    type: Boolean,
                    required: false,
                },
            },

            computed: {
                starColorClasses() {
                    return {
                        'text-emerald-600': this.average > 4,
                        'text-emerald-500': this.average >= 4 && this.average < 5,
                        'text-emerald-400': this.average >= 3 && this.average < 4,
                        'text-amber-500': this.average >= 2 && this.average < 3,
                        'text-red-500': this.average >= 1 && this.average < 2,
                        'text-gray-400': this.average <= 0,
                    };
                },

                abbreviatedTotal() {
                    if (this.total >= 1000) {
                        return `${(this.total / 1000).toFixed(1)}k`;
                    }

                    return this.total;
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-button-template"
    >
        <button
            v-if="! loading"
            :class="[buttonClass, '']"
        >
            {{ title }}
        </button>

        <button
            v-else
            :class="[buttonClass, '']"
        >
            <!-- Spinner -->
            <svg
                class="text-blue absolute h-5 w-5 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none" 
                aria-hidden="true"
                viewBox="0 0 24 24"
            >
                <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                >
                </circle>

                <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                >
                </path>
            </svg>

            <span class="relative h-full w-full opacity-0">
                {{ title }}
            </span>
        </button>
    </script>

    <script type="module">
        app.component('v-button', {
            template: '#v-button-template',

            props: {
                loading: Boolean,
                buttonType: String,
                title: String,
                buttonClass: String,
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-flash-item-template"
    >
        <div
            class="flex w-max max-w-[408px] justify-between gap-12 rounded-lg px-5 py-3 max-sm:max-w-80 max-sm:items-center max-sm:gap-2 max-sm:p-3"
            :style="typeStyles[flash.type]['container']"
        >
            <p
                class="flex items-center break-words text-sm"
                :style="typeStyles[flash.type]['message']"
            >
                <span
                    class="icon-toast-done text-2xl ltr:mr-2.5 rtl:ml-2.5"
                    :class="iconClasses[flash.type]"
                    :style="typeStyles[flash.type]['icon']"
                ></span>

                {{ flash.message }}
            </p>

			<span
                class="icon-cancel max-h-4 max-w-4 cursor-pointer"
                :style="typeStyles[flash.type]['icon']"
                @click="remove"
            ></span>
        </div>
    </script>

    <script type="module">
        app.component('v-flash-item', {
            template: '#v-flash-item-template',

            props: ['flash'],

            data() {
                return {
                    iconClasses: {
                        success: 'icon-toast-done',

                        error: 'icon-toast-error',

                        warning: 'icon-toast-exclamation-mark',

                        info: 'icon-toast-info',
                    },

                    typeStyles: {
                        success: {
                            container: 'background: #D4EDDA',

                            message: 'color: #155721',

                            icon: 'color: #155721'
                        },

                        error: {
                            container: 'background: #F8D7DA',

                            message: 'color: #721C24',

                            icon: 'color: #721C24'
                        },

                        warning: {
                            container: 'background: #FFF3CD',

                            message: 'color: #856404',

                            icon: 'color: #856404'
                        },

                        info: {
                            container: 'background: #E2E3E5',

                            message: 'color: #383D41',

                            icon: 'color: #383D41'
                        },
                    },
                };
            },

            created() {
                var self = this;

                setTimeout(function() {
                    self.remove()
                }, 5000)
            },

            methods: {
                remove() {
                    this.$emit('onRemove', this.flash)
                }
            }
        });
    </script>
    <script type="text/x-template" id="v-mobile-drawer-template">
        <v-drawer
    @close="onDrawerClose"
    is-active=""
    position="left"
    width="100%"
>
            <template v-slot:toggle>
            <span class="icon-hamburger cursor-pointer text-2xl"></span>
        </template>
    
            <template v-slot:header="{ close }">
            <div class="grid gap-y-2.5 p-6 pb-5 max-md:gap-y-1.5 max-md:border-b max-md:border-zinc-200 max-md:p-4 max-md:gap-y-1 max-md:font-semibold">
                <div class="flex items-center justify-between">
                    <a href="http://localhost/onlinestore/bagisto-2.3/public">
                        <img
                            src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/logo-CZWQQgOF.svg"
                            alt="OnlineStore"
                            width="131"
                            height="29"
                        >
                    </a>
                </div>

                <div class="absolute top-5 max-sm:top-4 ltr:right-5 rtl:left-5">
                    <span
                        class="icon-cancel cursor-pointer text-3xl max-md:text-2xl"
                        @click="close"
                    >
                    </span>
                </div>
            </div>
        </template>
    
            <template v-slot:content>
            <div class="flex-1 overflow-auto px-6 max-md:px-4 !p-0">
                <!-- Account Profile Hero Section -->
                <div class="border-b border-zinc-200 p-4">
                    <div class="grid grid-cols-[auto_1fr] items-center gap-4 rounded-xl border border-zinc-200 p-2.5">
                        <div>
                            <img
                                src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/user-placeholder-C_FiyGd9.png"
                                class="h-[60px] w-[60px] rounded-full max-md:rounded-full"
                            >
                        </div>

                        
                                                    <div class="flex flex-col justify-between gap-2.5 max-md:gap-0">
                                <p class="font-mediums break-all text-2xl max-md:text-xl">Hello! Vignesh</p>

                                <p class="text-zinc-500 no-underline max-md:text-sm"><EMAIL></p>
                            </div>
                                            </div>
                </div>

                

                <!-- Mobile category view -->
                <v-mobile-category ref="mobileCategory"></v-mobile-category>
            </div>
        </template>
    
            <template v-slot:footer>
            <div class="pb-8 max-md:pb-2">
                <!-- Localization & Currency Section -->
            </div>
        </template>
    </v-drawer>

    </script>

    <script
        type="text/x-template"
        id="v-mobile-category-template"
    >
        <!-- Wrapper with transition effects -->
        <div class="relative h-full overflow-hidden">
            <!-- Sliding container -->
            <div
                class="flex h-full transition-transform duration-300"
                :class="{
                    'ltr:translate-x-0 rtl:translate-x-0': currentViewLevel !== 'third',
                    'ltr:-translate-x-full rtl:translate-x-full': currentViewLevel === 'third'
                }"
            >
                <!-- First level view -->
                <div class="h-full w-full flex-shrink-0 overflow-auto px-6">
                    <div class="py-4">
                        <div
                            v-for="category in categories"
                            :key="category.id"
                            :class="{'mb-2': category.children && category.children.length}"
                        >
                            <div class="flex cursor-pointer items-center justify-between py-2 transition-colors duration-200">
                                <a :href="category.url" class="text-base font-medium text-black">
                                    {{ category.name }}
                                </a>
                            </div>

                            <!-- Second Level Categories -->
                            <div v-if="category.children && category.children.length" >
                                <div
                                    v-for="secondLevelCategory in category.children"
                                    :key="secondLevelCategory.id"
                                >
                                    <div
                                        class="flex cursor-pointer items-center justify-between py-2 transition-colors duration-200"
                                        @click="showThirdLevel(secondLevelCategory, category, $event)"
                                    >
                                        <a :href="secondLevelCategory.url" class="text-sm font-normal">
                                            {{ secondLevelCategory.name }}
                                        </a>

                                        <span
                                            v-if="secondLevelCategory.children && secondLevelCategory.children.length"
                                            class="icon-arrow-right rtl:icon-arrow-left"
                                        ></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Third level view -->
                <div
                    class="h-full w-full flex-shrink-0"
                    v-if="currentViewLevel === 'third'"
                >
                    <div class="border-b border-gray-200 px-6 py-4">
                        <button
                            @click="goBackToMainView"
                            class="flex items-center justify-center gap-2 focus:outline-none"
                            aria-label="Go back"
                        >
                            <span class="icon-arrow-left rtl:icon-arrow-right text-lg"></span>
                            <div class="text-base font-medium text-black">
                                Back to Main Menu                            </div>
                        </button>
                    </div>

                    <!-- Third Level Content -->
                    <div class="px-6 py-4">
                        <div
                            v-for="thirdLevelCategory in currentSecondLevelCategory?.children"
                            :key="thirdLevelCategory.id"
                            class="mb-2"
                        >
                            <a
                                :href="thirdLevelCategory.url"
                                class="block py-2 text-sm transition-colors duration-200"
                            >
                                {{ thirdLevelCategory.name }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-mobile-category', {
            template: '#v-mobile-category-template',

            data() {
                return  {
                    categories: [],
                    currentViewLevel: 'main',
                    currentSecondLevelCategory: null,
                    currentParentCategory: null
                }
            },

            mounted() {
                this.getCategories();
            },

            computed: {
                getCurrentScreenHeight() {
                    return window.innerHeight - (window.innerWidth < 920 ? 61 : 0) + 'px';
                },
            },

            methods: {
                getCategories() {
                    this.$axios.get("http://localhost/onlinestore/bagisto-2.3/public/api/categories/tree")
                        .then(response => {
                            this.categories = response.data.data;
                        })
                        .catch(error => {
                            console.log(error);
                        });
                },

                showThirdLevel(secondLevelCategory, parentCategory, event) {
                    if (secondLevelCategory.children && secondLevelCategory.children.length) {
                        this.currentSecondLevelCategory = secondLevelCategory;
                        this.currentParentCategory = parentCategory;
                        this.currentViewLevel = 'third';

                        if (event) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                    }
                },

                goBackToMainView() {
                    this.currentViewLevel = 'main';
                }
            },
        });

        app.component('v-mobile-drawer', {
            template: '#v-mobile-drawer-template',

            methods: {
                onDrawerClose() {
                    this.$refs.mobileCategory.currentViewLevel = 'main';
                }
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-gallery-zoomer-template"
    >
        <transition
            tag="div"
            class="bg-white"
            name="modal-content"
            enter-class="duration-300 ease-out"
            enter-from-class="translate-y-4 opacity-0 md:translate-y-0 md:scale-95"
            enter-to-class="translate-y-0 opacity-100 md:scale-100"
            leave-class="duration-200 ease-in"
            leave-from-class="translate-y-0 opacity-100 md:scale-100"
            leave-to-class="translate-y-4 opacity-0 md:translate-y-0 md:scale-95"
        >
            <div
                ref="parentContainer" 
                class="fixed inset-0 z-10 flex transform flex-col gap-4 overflow-y-auto transition"
                v-show="isOpen"
            >
                <!-- Close -->
                <span
                    class="icon-cancel absolute top-3 z-[1000] cursor-pointer text-3xl ltr:right-3 rtl:left-3"
                    @click="toggle"
                >
                </span>

                <span
                    class="icon-arrow-left fixed left-2.5 top-1/2 z-10 -mt-12 w-auto cursor-pointer rounded-full bg-[rgba(0,0,0,0.8)] p-3 text-2xl font-bold text-white opacity-30 transition-all hover:opacity-100"
                    v-if="attachments.length >= 2"
                    @click="navigate(currentIndex -= 1)"
                >
                </span>

                <span
                    class="icon-arrow-right fixed right-2.5 top-1/2 z-10 -mt-12 w-auto cursor-pointer rounded-full bg-[rgba(0,0,0,0.8)] p-3 text-2xl font-bold text-white opacity-30 transition-all hover:opacity-100"
                    v-if="attachments.length >= 2"
                    @click="navigate(currentIndex += 1)"
                >
                </span>
                    
                <!-- Main Image -->
                <div 
                    ref="mediaContainer" 
                    class="h-full w-full overflow-hidden"
                >
                    <div
                        class="relative m-auto flex w-full items-center justify-center"
                        :class="{
                            'h-full': ! isZooming,
                            'h-auto': isZooming
                        }"
                    >
                        <div
                            v-for="(attachment, index) in attachments"
                            class="h-full items-center justify-center"
                            ref="slides"
                        >
                            <video 
                                class="max-h-full max-w-full transition-transform duration-300 ease-out"
                                controls 
                                v-if="attachment.type == 'video'"
                            >
                                <source :src="attachment.url" type="video/mp4">
                                <source :src="attachment.url" type="video/ogg">
                                    Your browser does not support HTML video.
                            </video>

                            <template v-if="attachment.type === 'image'">
                                <!-- For Desktop -->
                                <img
                                    :src="attachment.url"
                                    class="max-h-full max-w-full transition-transform duration-300 ease-out max-md:hidden"
                                    :class="{
                                        'cursor-zoom-in': ! isZooming,
                                        'cursor-grab': ! isDragging && isZooming,
                                        'cursor-grabbing': isDragging && isZooming,
                                    }"
                                    :style="{transform: `translate(${translateX}px, ${translateY}px)`}"
                                    @click.stop="handleClick"
                                    @mousedown.prevent="handleMouseDown"
                                    @mousemove.prevent="handleMouseMove"
                                    @mouseleave.prevent="resetImagePosition"
                                    @mouseup.prevent="resetImagePosition"
                                    @mousewheel="handleMouseWheel"
                                />

                                <!-- For Mobile -->
                                <img
                                    :src="attachment.url"
                                    class="max-h-full max-w-full transition-transform duration-300 ease-out md:hidden"
                                    :class="{
                                        'cursor-zoom-in': ! isZooming,
                                        'cursor-grab': ! isDragging && isZooming,
                                        'cursor-grabbing': isDragging && isZooming,
                                    }"
                                    :style="{transform: `translate(${translateX}px, ${translateY}px)`}"
                                />    
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Thumbnails -->
                <div class="mb-4 flex justify-center gap-x-2">
                    <template v-for="(attachment, index) in attachments">
                        <img
                            class="h-16 w-16 transform cursor-pointer rounded-md border border-navyBlue border-transparent object-cover transition-transform hover:!border-navyBlue"
                            :class="{
                                '!border-navyBlue': currentIndex === index + 1,
                            }"
                            :src="attachment.url"
                            :key="index"
                            v-if="attachment.type === 'image'"
                            @click="navigate(currentIndex = index + 1)"
                        />

                        <video
                            class="h-16 w-16 transform cursor-pointer rounded-md border border-navyBlue border-transparent object-cover transition-transform hover:!border-navyBlue"
                            :class="{
                                '!border-navyBlue': currentIndex === index + 1,
                            }"
                            :src="attachment.url"
                            :key="index"
                            v-if="attachment.type === 'video'"
                            @click="navigate(currentIndex = index + 1)"
                        />
                    </template>
                </div>
            </div>
        </transition>
    </script>

    <script type="module">
        app.component('v-gallery-zoomer', {
            template: '#v-gallery-zoomer-template',

            props: {
                attachments: {
                    type: Object,

                    required: true,

                    default: () => [],
                },

                isImageZooming: {
                    type: Boolean,

                    default: false,
                },

                initialIndex: {
                    type: String,
                    
                    default: 0,
                },
            },

            watch: {
                isImageZooming(newVal, oldVal) {  
                    this.currentIndex = parseInt(this.initialIndex.split('_').pop()) + 1;

                    this.navigate(this.currentIndex);

                    this.toggle();
                },
            },
        
            data() {
                return {
                    isOpen: this.isImageZooming,

                    isDragging: false,

                    isZooming: false,

                    currentIndex: 1,

                    startDragX: 0,

                    startDragY: 0,

                    translateX: 0,

                    translateY: 0,

                    isMouseMoveTriggered: false,

                    isMouseDownTriggered: false,
                };
            },

            methods: {
                toggle() {
                    this.isOpen = ! this.isOpen;

                    document.body.style.overflow = this.isOpen ? 'hidden' : '';
                },

                open() {
                    this.isOpen = true;

                    document.body.style.overflow = 'hidden';
                },

                navigate(index) {
                    if (index > this.attachments.length) {
                        this.currentIndex = 1;
                    }

                    if (index < 1) {
                        this.currentIndex = this.attachments.length;
                    }

                    let slides = this.$refs.slides;

                    for (let i = 0; i < slides.length; i++) {
                        if (i == this.currentIndex - 1) {
                            continue;
                        }

                        slides[i].style.display = 'none';
                    }
                    
                    slides[this.currentIndex - 1].style.display = 'flex';

                    this.isZooming = false;

                    this.resetDrag();
                },

                handleClick(event) {
                    if (
                        this.isMouseMoveTriggered
                        && ! this.isMouseDownTriggered
                    ) {
                        return;
                    }

                    this.resetDrag();

                    this.isZooming = ! this.isZooming;
                },

                handleOuterClick() {
                    if (! this.isZooming) {
                        return;
                    }

                    this.isZooming = false;

                    resetDrag();
                },

                handleMouseDown(event) {
                    this.isMouseDownTriggered = true;

                    this.isDragging = true;

                    this.startDragX = event.clientX;

                    this.startDragY = event.clientY;
                },

                handleMouseMove(event) {
                    this.isMouseMoveTriggered = true;
                    
                    this.isMouseDownTriggered = false;

                    if (! this.isDragging) {
                        return;
                    }

                    const deltaX = event.clientX - this.startDragX;
                    
                    const deltaY = event.clientY - this.startDragY;
                    
                    const newTranslateY = this.translateY + deltaY;

                    const remainingHeight = this.$refs.parentContainer.clientHeight - this.$refs.mediaContainer.clientHeight;

                    const maxTranslateY = Math.min(0, window.innerHeight - (event.srcElement.height + remainingHeight));

                    const clampedTranslateY = Math.max(maxTranslateY, Math.min(newTranslateY, 0));

                    this.translateY = clampedTranslateY;
                    
                    this.startDragY = event.clientY;
                    
                    this.startDragX = event.clientX;

                    this.translateX += deltaX;
                },

                handleMouseWheel(event) {
                    const deltaY = event.clientY - this.startDragY;

                    let newTranslateY = this.translateY - event.deltaY / Math.abs(event.deltaY) * 100;
                    
                    const remainingHeight = this.$refs.parentContainer.clientHeight - this.$refs.mediaContainer.clientHeight;

                    const maxTranslateY = Math.min(0, window.innerHeight - (event.srcElement.height + remainingHeight));

                    this.translateY = Math.max(maxTranslateY, Math.min(newTranslateY, 0));
                },

                resetImagePosition() {
                    this.isDragging = false;

                    this.translateX  = 0;

                    this.startDragX = 0;
                },

                resetDrag() {
                    this.isDragging = false;

                    this.startDragX = 0;

                    this.startDragY = 0;

                    this.translateX = 0;

                    this.translateY = 0;
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-product-card-template"
    >
        <!-- Grid Card -->
        <div
            class="1180:transtion-all group w-full rounded-md 1180:relative 1180:grid 1180:content-start 1180:overflow-hidden 1180:duration-300 1180:hover:shadow-[0_5px_10px_rgba(0,0,0,0.1)]"
            v-if="mode != 'list'"
        >
            <div class="relative max-h-[300px] max-w-[291px] overflow-hidden max-md:max-h-60 max-md:max-w-full max-md:rounded-lg max-sm:max-h-[200px] max-sm:max-w-full">
                

                <!-- Product Image -->
                <a
                    :href="`http://localhost/onlinestore/bagisto-2.3/public/${product.url_key}`"
                    :aria-label="product.name + ' '"
                >
                    <v-shimmer-image class="after:content-[' '] relative bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105" :src="product.base_image.medium_image_url" :key="product.id" :index="product.id" width="291" height="300" :alt="product.name">
    <div class="shimmer after:content-[' '] relative bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105" :src="product.base_image.medium_image_url" :key="product.id" :index="product.id" width="291" height="300" :alt="product.name"></div>
</v-shimmer-image>

                </a>

                
                
                <!-- Product Ratings -->
                

                                    <v-product-ratings
    class="flex w-max items-center rounded-md border border-zinc-200 px-4 py-2 absolute bottom-1.5 items-center !border-white bg-white/80 !px-2 !py-1 text-xs max-sm:!px-1.5 max-sm:!py-0.5 ltr:left-1.5 rtl:right-1.5" :average="product.ratings.average" :total="product.reviews.total" :rating="false" v-if="product.reviews.total"
    average="0"
    total="0"
>
</v-product-ratings>

                
                

                <div class="action-items bg-black">
                    <!-- Product Sale Badge -->
                    <p
                        class="absolute top-1.5 inline-block rounded-[44px] bg-red-600 px-2.5 text-sm text-white max-sm:rounded-l-none max-sm:rounded-r-xl max-sm:px-2 max-sm:py-0.5 max-sm:text-xs ltr:left-1.5 max-sm:ltr:left-0 rtl:right-5 max-sm:rtl:right-0"
                        v-if="product.on_sale"
                    >
                        Sale                    </p>

                    <!-- Product New Badge -->
                    <p
                        class="absolute top-1.5 inline-block rounded-[44px] bg-navyBlue px-2.5 text-sm text-white max-sm:rounded-l-none max-sm:rounded-r-xl max-sm:px-2 max-sm:py-0.5 max-sm:text-xs ltr:left-1.5 max-sm:ltr:left-0 rtl:right-1.5 max-sm:rtl:right-0"
                        v-else-if="product.is_new"
                    >
                        New                    </p>

                    <div class="opacity-0 transition-all duration-300 group-hover:bottom-0 group-hover:opacity-100 max-lg:opacity-100 max-sm:opacity-100">

                        

                                                    <span
                                class="absolute top-2.5 flex h-6 w-6 items-center justify-center rounded-full border border-zinc-200 bg-white text-lg md:hidden ltr:right-1.5 rtl:left-1.5"
                                role="button"
                                aria-label="Add To Wishlist"
                                tabindex="0"
                                :class="product.is_wishlist ? 'icon-heart-fill text-red-500' : 'icon-heart'"
                                @click="addToWishlist()"
                            >
                            </span>
                        
                        

                        

                                                    <span
                                class="icon-compare absolute top-10 flex h-6 w-6 items-center justify-center rounded-full border border-zinc-200 bg-white text-lg sm:hidden ltr:right-1.5 rtl:left-1.5"
                                role="button"
                                aria-label="Add To Compare"
                                tabindex="0"
                                @click="addToCompare(product.id)"
                            >
                            </span>
                        
                        

                    </div>
                </div>
            </div>

            <!-- Product Information Section -->
            <div class="-mt-9 grid max-w-[291px] translate-y-9 content-start gap-2.5 bg-white p-2.5 transition-transform duration-300 ease-out group-hover:-translate-y-0 group-hover:rounded-t-lg max-md:relative max-md:mt-0 max-md:translate-y-0 max-md:gap-0 max-md:px-0 max-md:py-1.5 max-sm:min-w-[170px] max-sm:max-w-[192px]">

                

                <p class="break-all text-base font-medium max-md:mb-1.5 max-md:max-w-56 max-md:whitespace-break-spaces max-md:leading-6 max-sm:max-w-[192px] max-sm:text-sm max-sm:leading-4">
                    {{ product.name }}
                </p>

                

                <!-- Pricing -->
                

                <div
                    class="flex items-center gap-2.5 text-lg font-semibold max-sm:text-sm max-sm:leading-6"
                    v-html="product.price_html"
                >
                </div>

                

                <!-- Product Actions Section -->
                <div class="action-items flex items-center justify-between opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 max-md:hidden">
                                            

                        <button
                            class="secondary-button w-full max-w-full p-2.5 text-sm font-medium max-sm:rounded-xl max-sm:p-2"
                            :disabled="! product.is_saleable || isAddingToCart"
                            @click="addToCart()"
                        >
                            Add To Cart                        </button>

                        
                    
                    

                                            <span
                            class="cursor-pointer p-2.5 text-2xl max-sm:hidden"
                            role="button"
                            aria-label="Add To Wishlist"
                            tabindex="0"
                            :class="product.is_wishlist ? 'icon-heart-fill text-red-600' : 'icon-heart'"
                            @click="addToWishlist()"
                        >
                        </span>
                    
                    

                    

                                            <span
                            class="icon-compare cursor-pointer p-2.5 text-2xl max-sm:hidden"
                            role="button"
                            aria-label="Add To Compare"
                            tabindex="0"
                            @click="addToCompare(product.id)"
                        >
                        </span>
                    
                    
                </div>
            </div>
        </div>

        <!-- List Card -->
        <div
            class="relative flex max-w-max grid-cols-2 gap-4 overflow-hidden rounded max-sm:flex-wrap"
            v-else
        >
            <div class="group relative max-h-[258px] max-w-[250px] overflow-hidden"> 

                

                <a :href="`http://localhost/onlinestore/bagisto-2.3/public/${product.url_key}`">
                    <v-shimmer-image class="after:content-[' '] relative min-w-[250px] bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105" :src="product.base_image.medium_image_url" :key="product.id" :index="product.id" width="291" height="300" :alt="product.name">
    <div class="shimmer after:content-[' '] relative min-w-[250px] bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105" :src="product.base_image.medium_image_url" :key="product.id" :index="product.id" width="291" height="300" :alt="product.name"></div>
</v-shimmer-image>

                </a>

                

                <div class="action-items bg-black">
                    <p
                        class="absolute top-5 inline-block rounded-[44px] bg-red-500 px-2.5 text-sm text-white ltr:left-5 max-sm:ltr:left-2 rtl:right-5"
                        v-if="product.on_sale"
                    >
                        Sale                    </p>

                    <p
                        class="absolute top-5 inline-block rounded-[44px] bg-navyBlue px-2.5 text-sm text-white ltr:left-5 max-sm:ltr:left-2 rtl:right-5"
                        v-else-if="product.is_new"
                    >
                        New                    </p>

                    <div class="opacity-0 transition-all duration-300 group-hover:bottom-0 group-hover:opacity-100 max-sm:opacity-100">

                        

                                                    <span 
                                class="absolute top-5 flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-md bg-white text-2xl ltr:right-5 rtl:left-5"
                                role="button"
                                aria-label="Add To Wishlist"
                                tabindex="0"
                                :class="product.is_wishlist ? 'icon-heart-fill text-red-600' : 'icon-heart'"
                                @click="addToWishlist()"
                            >
                            </span>
                        
                        

                        

                                                    <span
                                class="icon-compare absolute top-16 flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-md bg-white text-2xl ltr:right-5 rtl:left-5"
                                role="button"
                                aria-label="Add To Compare"
                                tabindex="0"
                                @click="addToCompare(product.id)"
                            >
                            </span>
                        
                        
                    </div>
                </div>
            </div>

            <div class="grid content-start gap-4">

                

                <p class="text-base">
                    {{ product.name }}
                </p>

                

                

                <div
                    class="flex gap-2.5 text-lg font-semibold"
                    v-html="product.price_html"
                >
                </div>

                

                <!-- Needs to implement that in future -->
                <div class="flex hidden gap-4">
                    <span class="block h-[30px] w-[30px] rounded-full bg-[#B5DCB4]">
                    </span>

                    <span class="block h-[30px] w-[30px] rounded-full bg-zinc-500">
                    </span>
                </div>

                

                <p class="text-sm text-zinc-500">
                    <template  v-if="! product.ratings.total">
                        <p class="text-sm text-zinc-500">
                            Be the first to review this product                        </p>
                    </template>

                    <template v-else>
                                                    <v-product-ratings
    class="flex w-max items-center rounded-md border border-zinc-200 px-4 py-2" :average="product.ratings.average" :total="product.reviews.total" :rating="false"
    average="0"
    total="0"
>
</v-product-ratings>

                                            </template>
                </p>

                

                
                    

                    <v-button class="primary-button whitespace-nowrap px-8 py-2.5" title="Add To Cart" :loading="isAddingToCart" :disabled="! product.is_saleable || isAddingToCart" @click="addToCart()"></v-button>


                    

                            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-product-card', {
            template: '#v-product-card-template',

            props: ['mode', 'product'],

            data() {
                return {
                    isCustomer: '1',

                    isAddingToCart: false,
                }
            },

            methods: {
                addToWishlist() {
                    if (this.isCustomer) {
                        this.$axios.post(`http://localhost/onlinestore/bagisto-2.3/public/api/customer/wishlist`, {
                                product_id: this.product.id
                            })
                            .then(response => {
                                this.product.is_wishlist = ! this.product.is_wishlist;

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                            })
                            .catch(error => {});
                        } else {
                            window.location.href = "http://localhost/onlinestore/bagisto-2.3/public/customer/login";
                        }
                },

                addToCompare(productId) {
                    /**
                     * This will handle for customers.
                     */
                    if (this.isCustomer) {
                        this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/api/compare-items', {
                                'product_id': productId
                            })
                            .then(response => {
                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                            })
                            .catch(error => {
                                if ([400, 422].includes(error.response.status)) {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: error.response.data.data.message });

                                    return;
                                }

                                this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message});
                            });

                        return;
                    }

                    /**
                     * This will handle for guests.
                     */
                    let items = this.getStorageValue() ?? [];

                    if (items.length) {
                        if (! items.includes(productId)) {
                            items.push(productId);

                            localStorage.setItem('compare_items', JSON.stringify(items));

                            this.$emitter.emit('add-flash', { type: 'success', message: "Item added successfully to compare list." });
                        } else {
                            this.$emitter.emit('add-flash', { type: 'warning', message: "Item is already added to compare list." });
                        }
                    } else {
                        localStorage.setItem('compare_items', JSON.stringify([productId]));

                        this.$emitter.emit('add-flash', { type: 'success', message: "Item added successfully to compare list." });

                    }
                },

                getStorageValue(key) {
                    let value = localStorage.getItem('compare_items');

                    if (! value) {
                        return [];
                    }

                    return JSON.parse(value);
                },

                addToCart() {
                    this.isAddingToCart = true;

                    this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/api/checkout/cart', {
                            'quantity': 1,
                            'product_id': this.product.id,
                        })
                        .then(response => {
                            if (response.data.message) {
                                this.$emitter.emit('update-mini-cart', response.data.data );

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });
                            } else {
                                this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                            }

                            this.isAddingToCart = false;
                        })
                        .catch(error => {
                            this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });

                            if (error.response.data.redirect_uri) {
                                window.location.href = error.response.data.redirect_uri;
                            }
                            
                            this.isAddingToCart = false;
                        });
                },
            },
        });
    </script>
    <script 
        type="text/x-template"
        id="v-product-carousel-template"
    >
        <div class="relative m-auto flex w-full overflow-hidden">
            <!-- Slider -->
            <div 
                class="inline-flex translate-x-0 cursor-pointer transition-transform duration-700 ease-out will-change-transform" 
                ref="sliderContainer"
            >
                <div 
                    class="grid max-h-screen w-screen content-center bg-cover bg-no-repeat" 
                    v-for="(media, index) in options" 
                    ref="slide"
                >
                    <template v-if="media.type == 'videos'">
                        <video 
                            controls 
                            width="100%" 
                            :alt="media.video_url" 
                            :key="media.video_url"
                        >
                            <source 
                                :src="media.video_url" 
                                type="video/mp4"
                            />
                        </video>
                    </template>
                    
                    <template v-else>
                        <img 
                            class="aspect-square max-h-full w-full max-w-full select-none transition-transform duration-300 ease-in-out" 
                            :src="media.large_image_url" 
                            :alt="media.large_image_url" 
                        />
                    </template>
                </div>
            </div>

            <!-- Pagination -->
            <div
                class="absolute bottom-3 left-0 flex w-full justify-center max-sm:bottom-2.5"
                v-if="options?.length > 1"
            >
                <div 
                    v-for="(media, index) in options" 
                    class="mx-1 h-1.5 w-1.5 cursor-pointer rounded-full" 
                    :class="{ 'bg-navyBlue': index === Math.abs(currentIndex), 'opacity-30 bg-gray-500': index !== Math.abs(currentIndex) }"
                    role="button"
                >
                </div>
            </div>
        </div>
    </script>

    <script type="module">
        app.component("v-product-carousel", {
            template: '#v-product-carousel-template',
            
            props: ['options'],
            
            data() {
                return {
                    isDragging: false,
                    startPos: 0,
                    currentTranslate: 0,
                    prevTranslate: 0,
                    animationID: 0,
                    currentIndex: 0,
                    slider: '',
                    slides: [],
                    autoPlayInterval: null,
                    direction: 'ltr',
                    startFrom: 1,
                    viewportWidth: window.innerWidth,
                };
            },
            
            mounted() {
                this.slider = this.$refs.sliderContainer;

                if (
                    this.$refs.slide 
                    && typeof this.$refs.slide[Symbol.iterator] === 'function'
                ) {
                    this.slides = Array.from(this.$refs.slide);
                }

                this.init();
                
                window.addEventListener('resize', this.onResize);
            },
            
            watch: {
                options: function() {
                    this.slider = this.$refs.sliderContainer;

                    if (
                        this.$refs.slide 
                        && typeof this.$refs.slide[Symbol.iterator] === 'function'
                    ) {
                        this.slides = Array.from(this.$refs.slide);
                    }

                    this.resetIndex();
                    
                    this.init();
                }
            },
            
            methods: {
                init() {
                    this.direction = document.dir;

                    if (this.direction === 'rtl') {
                        this.startFrom = -1;
                    }

                    this.slides.forEach((slide, index) => {
                        slide.querySelector('img')?.addEventListener('dragstart', (e) => e.preventDefault());

                        slide.addEventListener('touchstart', this.handleDragStart);
                        
                        slide.addEventListener('touchend', this.handleDragEnd);
                        
                        slide.addEventListener('touchmove', this.handleDrag, { passive: true });
                    });

                    this.setPositionByIndex();
                },
                
                resetIndex() {
                    if (this.currentIndex >= this.slides.length) {
                        
                        this.currentIndex = this.slides.length - 1;
                    }
                    
                    this.setPositionByIndex();
                },
                
                handleDragStart(event) {
                    this.startPos = event.type === 'mousedown' ? event.clientX : event.touches[0].clientX;
                    
                    this.isDragging = true;
                    
                    this.animationID = requestAnimationFrame(this.animation);
                },
                
                handleDrag(event) {
                    if (! this.isDragging) {
                        return;
                    }

                    const currentPosition = event.type === 'mousemove' ? event.clientX : event.touches[0].clientX;
                    
                    this.currentTranslate = this.prevTranslate + currentPosition - this.startPos;
                },
                
                handleDragEnd(event) {
                    clearInterval(this.autoPlayInterval);

                    cancelAnimationFrame(this.animationID);
                    
                    this.isDragging = false;

                    const movedBy = this.currentTranslate - this.prevTranslate;

                    if (this.direction === 'ltr') {
                        if (
                            movedBy < -100 
                            && this.currentIndex < this.slides.length - 1
                        ) {
                            this.currentIndex += 1;
                        }
                        
                        if (
                            movedBy > 100 
                            && this.currentIndex > 0
                        ) {
                            this.currentIndex -= 1;
                        }
                    } else {
                        if (
                            movedBy > 100 
                            && this.currentIndex < this.slides.length - 1
                        ) {
                            if (Math.abs(this.currentIndex) !== this.slides.length - 1) {
                                this.currentIndex -= 1;
                            }
                        }
                        
                        if (
                            movedBy < -100 
                            && this.currentIndex < 0
                        ) {
                            this.currentIndex += 1;
                        }
                    }
                    
                    this.setPositionByIndex();
                },
                
                animation() {
                    this.setSliderPosition();
                    
                    if (this.isDragging) {
                        requestAnimationFrame(this.animation);
                    }
                },
                
                setPositionByIndex() {
                    this.currentTranslate = this.currentIndex * -this.viewportWidth;
                    
                    this.prevTranslate = this.currentTranslate;
                    
                    this.setSliderPosition();
                },
                
                setSliderPosition() {
                    if (this.slider) {
                        this.slider.style.transform = `translateX(${this.currentTranslate}px)`;
                    }
                },
                
                onResize() {
                    this.viewportWidth = window.innerWidth;
                    this.setPositionByIndex();
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-flash-group-template"
    >
        <transition-group
            tag='div'
            name="flash-group"
            enter-from-class="ltr:translate-x-full rtl:-translate-x-full"
            enter-active-class="transform transition duration-200 ease-in-out"
            enter-to-class="ltr:translate-x-0 rtl:-translate-x-0"
            leave-from-class="ltr:translate-x-0 rtl:-translate-x-0"
            leave-active-class="transform transition duration-200 ease-in-out"
            leave-to-class="ltr:translate-x-full rtl:-translate-x-full"
            class='fixed top-5 z-[1001] grid justify-items-end gap-2.5 max-sm:hidden ltr:right-5 rtl:left-5'
        >
            <v-flash-item
    v-for='flash in flashes'
    :key='flash.uid'
    :flash="flash"
    @onRemove="remove($event)"
/>

        </transition-group>

        <transition-group
            tag='div'
            name="flash-group"
            enter-from-class="ltr:translate-y-full rtl:-translate-y-full"
            enter-active-class="transform transition duration-200 ease-in-out"
            enter-to-class="ltr:translate-y-0 rtl:-translate-y-0"
            leave-from-class="ltr:translate-y-0 rtl:-translate-y-0"
            leave-active-class="transform transition duration-200 ease-in-out"
            leave-to-class="ltr:translate-y-full rtl:-translate-y-full"
            class='fixed bottom-10 left-1/2 z-[1001] grid -translate-x-1/2 -translate-y-1/2 transform justify-items-center gap-2.5 sm:hidden'
        >
            <v-flash-item
    v-for='flash in flashes'
    :key='flash.uid'
    :flash="flash"
    @onRemove="remove($event)"
/>

        </transition-group>
    </script>

    <script type="module">
        app.component('v-flash-group', {
            template: '#v-flash-group-template',

            data() {
                return {
                    uid: 0,

                    flashes: []
                }
            },

            created() {
                                                                                                                                                                
                this.registerGlobalEvents();
            },

            methods: {
                add(flash) {
                    flash.uid = this.uid++;

                    this.flashes.push(flash);
                },

                remove(flash) {
                    let index = this.flashes.indexOf(flash);

                    this.flashes.splice(index, 1);
                },

                registerGlobalEvents() {
                    this.$emitter.on('add-flash', this.add);
                },
            }
        });
    </script>
    <script
        type="text/x-template"
        id="v-modal-confirm-template"
    >
        <div>
            <transition
                tag="div"
                name="modal-overlay"
                enter-class="duration-300 ease-out"
                enter-from-class="opacity-0"
                enter-to-class="opacity-100"
                leave-class="duration-200 ease-in"
                leave-from-class="opacity-100"
                leave-to-class="opacity-0"
            >
                <div
                    class="fixed inset-0 z-20 bg-gray-500 bg-opacity-50 transition-opacity"
                    v-show="isOpen"
                ></div>
            </transition>

            <transition
                tag="div"
                name="modal-content"
                enter-class="duration-300 ease-out"
                enter-from-class="translate-y-4 opacity-0 md:translate-y-0 md:scale-95"
                enter-to-class="translate-y-0 opacity-100 md:scale-100"
                leave-class="duration-200 ease-in"
                leave-from-class="translate-y-0 opacity-100 md:scale-100"
                leave-to-class="translate-y-4 opacity-0 md:translate-y-0 md:scale-95"
            >
                <div
                    class="fixed inset-0 z-20 transform overflow-y-auto transition" v-show="isOpen"
                >
                    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                        <div class="absolute left-1/2 top-1/2 z-[999] w-full max-w-[475px] -translate-x-1/2 -translate-y-1/2 overflow-hidden rounded-xl bg-white p-5 max-md:w-[90%] max-sm:p-4">
                            <div class="flex gap-2.5">
                                <div>
                                    <span class="flex rounded-full border border-gray-300 p-2.5">
                                        <i class="icon-error text-3xl max-sm:text-xl"></i>
                                    </span>
                                </div>

                                <div>
                                    <div class="flex items-center justify-between gap-5 text-xl max-sm:text-lg">
                                        {{ title }}
                                    </div>

                                    <div class="pb-5 pt-1.5 text-left text-sm text-gray-500">
                                        {{ message }}
                                    </div>

                                    <div class="flex justify-end gap-2.5">
                                        <button
                                            type="button"
                                            class="secondary-button max-md:py-3 max-sm:px-6 max-sm:py-2.5"
                                            @click="disagree"
                                        >
                                            {{ options.btnDisagree }}
                                        </button>

                                        <button
                                            type="button"
                                            class="primary-button max-md:py-3 max-sm:px-6 max-sm:py-2.5"
                                            @click="agree"
                                        >
                                            {{ options.btnAgree }} 
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </transition>
        </div>
    </script>

    <script type="module">
        app.component('v-modal-confirm', {
            template: '#v-modal-confirm-template',

            data() {
                return {
                    isOpen: false,

                    title: '',

                    message: '',

                    options: {
                        btnDisagree: '',
                        btnAgree: '',
                    },

                    agreeCallback: null,

                    disagreeCallback: null,
                };
            },

            created() {
                this.registerGlobalEvents();
            },

            methods: {
                open({
                    title = "Are you sure?",
                    message = "Are you sure you want to perform this action?",
                    options = {
                        btnDisagree: "Disagree",
                        btnAgree: "Agree",
                    },
                    agree = () => {},
                    disagree = () => {},
                }) {
                    this.isOpen = true;

                    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

                    document.body.style.overflow = 'hidden';

                    document.body.style.paddingRight = `${scrollbarWidth}px`;

                    this.title = title;

                    this.message = message;

                    this.options = options;

                    this.agreeCallback = agree;

                    this.disagreeCallback = disagree;
                },

                disagree() {
                    this.isOpen = false;

                    document.body.style.overflow = 'auto';

                    document.body.style.paddingRight = '';

                    this.disagreeCallback();
                },

                agree() {
                    this.isOpen = false;

                    document.body.style.overflow = 'auto';

                    document.body.style.paddingRight = '';

                    this.agreeCallback();
                },

                registerGlobalEvents() {
                    this.$emitter.on('open-confirm-modal', this.open);
                },
            }
        });
    </script>
        <script
            type="text/x-template"
            id="v-product-template"
        >
            <!--
    If a component has the `as` attribute, it indicates that it uses
    the ajaxified form or some customized slot form.
-->
    <v-form v-slot="{ meta, errors, handleSubmit }" as="div">
        <form
                    ref="formData"
                    @submit="handleSubmit($event, addToCart)"
                >
                    <input
                        type="hidden"
                        name="product_id"
                        value="2"
                    >

                    <input
                        type="hidden"
                        name="is_buy_now"
                        v-model="is_buy_now"
                    >

                    <div class="container px-[60px] max-1180:px-0">
                        <div class="mt-12 flex gap-9 max-1180:flex-wrap max-lg:mt-0 max-sm:gap-y-4">
                            <!-- Gallery Blade Inclusion -->
                            <v-product-gallery ref="gallery">
    <div class="sticky top-8 flex h-max gap-8 max-1180:hidden">
    <div class="flex max-h-[100px] max-w-[100px] flex-wrap gap-2.5">
        <div class="flex-24 h-509 flex max-w-[100px] flex-wrap place-content-start justify-center gap-2.5">
            <span class="shimmer h-6 w-6 text-2xl"></span>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <div class="shimmer h-[100px] min-h-[100px] w-[100px] min-w-[100px] rounded-xl"></div>
            <span class="shimmer h-6 w-6 text-2xl"></span>
        </div>
    </div>
    
    <div class="max-h-[610px] max-w-[560px]">
        <div class="shimmer min-h-[607px] min-w-[560px] rounded-xl"></div>
    </div>
</div>

<div class="overflow-hidden 1180:hidden">
    <div class="shimmer aspect-square max-h-screen w-screen"></div>
</div></v-product-gallery>


                            <!-- Details -->
                            <div class="relative max-w-[590px] max-1180:w-full max-1180:max-w-full max-1180:px-5 max-sm:px-4">
                                

                                <div class="flex justify-between gap-4">
                                    <h1 class="break-words text-3xl font-medium max-sm:text-xl">
                                        Arctic Bliss Stylish Winter Scarf
                                    </h1>

                                                                            <div
                                            class="flex max-h-[46px] min-h-[46px] min-w-[46px] cursor-pointer items-center justify-center rounded-full border bg-white text-2xl transition-all hover:opacity-[0.8] max-sm:max-h-7 max-sm:min-h-7 max-sm:min-w-7 max-sm:text-base"
                                            role="button"
                                            aria-label="Add To Wishlist"
                                            tabindex="0"
                                            :class="isWishlist ? 'icon-heart-fill text-red-600' : 'icon-heart'"
                                            @click="addToWishlist"
                                        >
                                        </div>
                                                                    </div>

                                

                                <!-- Rating -->
                                

                                
                                

                                <!-- Pricing -->
                                

                                <p class="mt-[22px] flex items-center gap-2.5 text-2xl !font-medium max-sm:mt-2 max-sm:gap-x-2.5 max-sm:gap-y-0 max-sm:text-lg">
                                    <p class="final-price font-semibold max-sm:leading-4">
        ₹17.00
    </p>

                                </p>

                                
                                
                                

                                

                                <p class="mt-6 text-lg text-zinc-500 max-sm:mt-1.5 max-sm:text-sm">
                                    Experience the embrace of warmth and style with our Arctic Bliss Winter Scarf. Crafted from a luxurious blend of acrylic and wool, this cozy scarf is designed to keep you snug during the coldest days. Its stylish and versatile design, combined with an extra-long length, offers customizable styling options. Elevate your winter wardrobe or delight someone special with this essential winter accessory.
                                </p>

                                

                                
                                
                                
                                
                                
                                
                                <!-- Product Actions and Quantity Box -->
                                <div class="mt-8 flex max-w-[470px] gap-4 max-sm:mt-4">

                                    

                                                                            <v-quantity-changer
    class="flex items-center border border-navyBlue gap-x-4 rounded-xl px-7 py-4 max-md:py-3 max-sm:gap-x-5 max-sm:rounded-lg max-sm:px-4 max-sm:py-1.5"
    name="quantity"
    value="1"
    min-value="1"
>
</v-quantity-changer>

                                    
                                    

                                                                            <!-- Add To Cart Button -->
                                        

                                        <v-button type="submit" class="secondary-button w-full max-w-full max-md:py-3 max-sm:rounded-lg max-sm:py-1.5" button-type="secondary-button" title="Add To Cart" :loading="isStoring.addToCart" :disabled="isStoring.addToCart" @click="is_buy_now=0;"></v-button>


                                        
                                                                    </div>

                                <!-- Buy Now Button -->
                                                                    

                                    
                                    
                                
                                

                                <!-- Share Buttons -->
                                <div class="mt-10 flex gap-9 max-md:mt-4 max-md:flex-wrap max-sm:justify-center max-sm:gap-3">
                                    

                                    <div
                                        class="flex cursor-pointer items-center justify-center gap-2.5 max-sm:gap-1.5 max-sm:text-base"
                                        role="button"
                                        tabindex="0"
                                        @click="is_buy_now=0; addToCompare(2)"
                                    >
                                                                                    <span
                                                class="icon-compare text-2xl"
                                                role="presentation"
                                            ></span>

                                            Compare                                                                            </div>

                                    
                                </div>

                                
                            </div>
                        </div>
                    </div>
                </form>
    </v-form>

<!--
    Otherwise, a traditional form will be provided with a minimal
    set of configurations.
-->
        </script>

        <script type="module">
            app.component('v-product', {
                template: '#v-product-template',

                data() {
                    return {
                        isWishlist: Boolean(""),

                        isCustomer: '1',

                        is_buy_now: 0,

                        isStoring: {
                            addToCart: false,

                            buyNow: false,
                        },
                    }
                },

                methods: {
                    addToCart(params) {
                        const operation = this.is_buy_now ? 'buyNow' : 'addToCart';

                        this.isStoring[operation] = true;

                        let formData = new FormData(this.$refs.formData);

                        this.ensureQuantity(formData);

                        this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/api/checkout/cart', formData, {
                                headers: {
                                    'Content-Type': 'multipart/form-data'
                                }
                            })
                            .then(response => {
                                if (response.data.message) {
                                    this.$emitter.emit('update-mini-cart', response.data.data);

                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });

                                    if (response.data.redirect) {
                                        window.location.href= response.data.redirect;
                                    }
                                } else {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                                }

                                this.isStoring[operation] = false;
                            })
                            .catch(error => {
                                this.isStoring[operation] = false;

                                this.$emitter.emit('add-flash', { type: 'warning', message: error.response.data.message });
                            });
                    },

                    addToWishlist() {
                        if (this.isCustomer) {
                            this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/api/customer/wishlist', {
                                    product_id: "2"
                                })
                                .then(response => {
                                    this.isWishlist = ! this.isWishlist;

                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                                })
                                .catch(error => {});
                        } else {
                            window.location.href = "http://localhost/onlinestore/bagisto-2.3/public/customer/login";
                        }
                    },

                    addToCompare(productId) {
                        /**
                         * This will handle for customers.
                         */
                        if (this.isCustomer) {
                            this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/api/compare-items', {
                                    'product_id': productId
                                })
                                .then(response => {
                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                                })
                                .catch(error => {
                                    if ([400, 422].includes(error.response.status)) {
                                        this.$emitter.emit('add-flash', { type: 'warning', message: error.response.data.data.message });

                                        return;
                                    }

                                    this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message});
                                });

                            return;
                        }

                        /**
                         * This will handle for guests.
                         */
                        let existingItems = this.getStorageValue(this.getCompareItemsStorageKey()) ?? [];

                        if (existingItems.length) {
                            if (! existingItems.includes(productId)) {
                                existingItems.push(productId);

                                this.setStorageValue(this.getCompareItemsStorageKey(), existingItems);

                                this.$emitter.emit('add-flash', { type: 'success', message: "Product added in compare." });
                            } else {
                                this.$emitter.emit('add-flash', { type: 'warning', message: "Product is already added in compare." });
                            }
                        } else {
                            this.setStorageValue(this.getCompareItemsStorageKey(), [productId]);

                            this.$emitter.emit('add-flash', { type: 'success', message: "Product added in compare." });
                        }
                    },

                    updateQty(quantity, id) {
                        this.isLoading = true;

                        let qty = {};

                        qty[id] = quantity;

                        this.$axios.put('http://localhost/onlinestore/bagisto-2.3/public/api/checkout/cart', { qty })
                            .then(response => {
                                if (response.data.message) {
                                    this.cart = response.data.data;
                                } else {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                                }

                                this.isLoading = false;
                            }).catch(error => this.isLoading = false);
                    },

                    getCompareItemsStorageKey() {
                        return 'compare_items';
                    },

                    setStorageValue(key, value) {
                        localStorage.setItem(key, JSON.stringify(value));
                    },

                    getStorageValue(key) {
                        let value = localStorage.getItem(key);

                        if (value) {
                            value = JSON.parse(value);
                        }

                        return value;
                    },

                    scrollToReview() {
                        let accordianElement = document.querySelector('#review-accordian-button');

                        if (accordianElement) {
                            accordianElement.click();

                            accordianElement.scrollIntoView({
                                behavior: 'smooth'
                            });
                        }

                        let tabElement = document.querySelector('#review-tab-button');

                        if (tabElement) {
                            tabElement.click();

                            tabElement.scrollIntoView({
                                behavior: 'smooth'
                            });
                        }
                    },

                    ensureQuantity(formData) {
                        if (! formData.has('quantity')) {
                            formData.append('quantity', 1);
                        }
                    },
                },
            });
        </script>
        <script
        type="text/x-template"
        id="v-image-search-template"
    >
        <div>
            <label
                class="icon-camera absolute top-3 flex items-center text-xl max-sm:top-2.5 ltr:right-3 ltr:pr-3 max-md:ltr:right-1.5 rtl:left-3 rtl:pl-3 max-md:rtl:left-1.5"
                aria-label="Search"
                :for="'v-image-search-' + $.uid"
                v-if="! isSearching"
            >
            </label>

            <label
                class="absolute top-2.5 flex cursor-pointer items-center text-xl ltr:right-3 ltr:pr-3 max-md:ltr:pr-1 rtl:left-3 rtl:pl-3 max-md:rtl:pl-1"
                v-else
            >
                <!-- Spinner -->
                <svg
                    class="h-5 w-5 animate-spin text-black"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                >
                    <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                    >
                    </circle>

                    <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    >
                    </path>
                </svg>
            </label>

            <input
                type="file"
                class="hidden"
                ref="imageSearchInput"
                :id="'v-image-search-' + $.uid"
                @change="loadLibrary()"
            />

            <img
                id="uploaded-image-url"
                class="hidden"
                :src="uploadedImageUrl"
                alt="uploaded image url"
                width="20"
                height="20"
            />
        </div>
    </script>

    <script type="module">
        app.component('v-image-search', {
            template: '#v-image-search-template',

            data() {
                return {
                    uploadedImageUrl: '',

                    isSearching: false,
                };
            },

            methods: {
                /**
                 * This method will dynamically load the scripts. Because image search library
                 * only used when someone clicks or interact with the image button. This will
                 * reduce some data usage for mobile user.
                 * 
                 * @return {void}
                 */
                loadLibrary() {
                    // External TensorFlow libraries disabled for privacy
                    alert('Image search feature has been disabled for privacy reasons.');
                    return;

                    // this.$shop.loadDynamicScript(
                    //     'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest/dist/tf.min.js', () => {
                    //         this.$shop.loadDynamicScript(
                    //             'https://cdn.jsdelivr.net/npm/tensorflow-models-mobilenet-patch@2.1.1/dist/mobilenet.min.js', () => {
                    //                 this.analyzeImage();
                    //             }
                    //         );
                    //     }
                    // );
                },

                /**
                 * This method will analyze the image and load the sets on the bases of trained model.
                 * 
                 * @return {void}
                 */
                analyzeImage() {
                    this.isSearching = true;

                    let imageInput = this.$refs.imageSearchInput;

                    if (imageInput.files && imageInput.files[0]) {
                        if (imageInput.files[0].type.includes('image/')) {
                            if (imageInput.files[0].size <= 2000000) {
                                let formData = new FormData();

                                formData.append('image', imageInput.files[0]);

                                this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/search/upload', formData, {
                                        headers: {
                                            'Content-Type': 'multipart/form-data'
                                        }
                                    })
                                    .then(response => {
                                        let net;

                                        let self = this;

                                        this.uploadedImageUrl = response.data;

                                        async function app() {
                                            let analysedResult = [];

                                            let queryString = '';

                                            net = await mobilenet.load();

                                            try {
                                                const result = await net.classify(document.getElementById(
                                                    'uploaded-image-url'));

                                                result.forEach(function(value) {
                                                    queryString = value.className.split(',');

                                                    if (queryString.length > 1) {
                                                        analysedResult = analysedResult.concat(
                                                            queryString);
                                                    } else {
                                                        analysedResult.push(queryString[0]);
                                                    }
                                                });
                                            } catch (error) {
                                                this.$emitter.emit('add-flash', {
                                                    type: 'error',
                                                    message: "Something went wrong, please try again later."
                                                });
                                            }

                                            localStorage.searchedImageUrl = self.uploadedImageUrl;

                                            queryString = localStorage.searchedTerms = analysedResult.join(
                                                '_');

                                            queryString = localStorage.searchedTerms.split('_').map(
                                            term => {
                                                return term.split(' ').join('+');
                                            });

                                            window.location.href =
                                                `${'http://localhost/onlinestore/bagisto-2.3/public/search'}?query=${queryString[0]}&image-search=1`;
                                        }

                                        app();
                                    })
                                    .catch((error) => {
                                        this.$emitter.emit('add-flash', {
                                            type: 'error',
                                            message: "Something went wrong, please try again later."
                                        });

                                        this.isSearching = false;
                                    });
                            } else {
                                imageInput.value = '';

                                this.$emitter.emit('add-flash', {
                                    type: 'error',
                                    message: 'Size Limit Error'
                                });

                                this.isSearching = false;
                            }
                        } else {
                            imageInput.value = '';

                            this.$emitter.emit('add-flash', {
                                type: 'error',
                                message: 'Only images (.jpeg, .jpg, .png, ..) are allowed.'
                            });

                            this.isSearching = false;
                        }
                    }
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-mini-cart-template"
    >
        

                    <v-drawer
    
    is-active=""
    position="right"
    width="500px"
>
            <template v-slot:toggle>
            <span class="relative">
                        <span
                            class="icon-cart cursor-pointer text-2xl"
                            role="button"
                            aria-label="Shopping Cart"
                            tabindex="0"
                        ></span>

                                                    <span
                                class="absolute -top-4 rounded-[44px] bg-navyBlue px-2 py-1.5 text-xs font-semibold leading-[9px] text-white ltr:left-5 rtl:right-5 max-md:px-2 max-md:py-1.5 max-md:ltr:left-4 max-md:rtl:right-4"
                                v-if="cart?.items_count"
                            >
                                {{ cart.items_count }}
                            </span>
                                            </span>
        </template>
    
            <template v-slot:header="{ close }">
            <div class="grid gap-y-2.5 p-6 pb-5 max-md:gap-y-1.5 max-md:border-b max-md:border-zinc-200 max-md:p-4 max-md:gap-y-1 max-md:font-semibold">
                <div class="flex items-center justify-between">
                        <p class="text-2xl font-medium max-md:text-xl max-sm:text-xl">
                            Shopping Cart                        </p>
                    </div>

                    <p class="text-base max-md:text-zinc-500 max-sm:text-xs">
                        Get Up To 30% OFF on your 1st order
                    </p>

                <div class="absolute top-5 max-sm:top-4 ltr:right-5 rtl:left-5">
                    <span
                        class="icon-cancel cursor-pointer text-3xl max-md:text-2xl"
                        @click="close"
                    >
                    </span>
                </div>
            </div>
        </template>
    
            <template v-slot:content>
            <div class="flex-1 overflow-auto px-6 max-md:px-4">
                <!-- Cart Item Listing -->
                    <div
                        class="mt-9 grid gap-12 max-md:mt-2.5 max-md:gap-5"
                        v-if="cart?.items?.length"
                    >
                        <div
                            class="flex gap-x-5 max-md:gap-x-4"
                            v-for="item in cart?.items"
                        >
                            <!-- Cart Item Image -->
                            

                            <div class="">
                                <a :href="`http://localhost/onlinestore/bagisto-2.3/public/${item.product_url_key}`">
                                    <img
                                        :src="item.base_image.small_image_url"
                                        class="max-w-28 max-h-28 rounded-xl max-md:max-h-20 max-md:max-w-[76px]"
                                    />
                                </a>
                            </div>

                            

                        <!-- Cart Item Information -->
                        <div class="grid flex-1 place-content-start justify-stretch gap-y-2.5">
                            <div class="flex justify-between gap-2 max-md:gap-0 max-sm:flex-wrap">

                                    

                                    <a
                                    class="max-w-4/5 max-md:w-full"
                                    :href="`http://localhost/onlinestore/bagisto-2.3/public/${item.product_url_key}`"
                                >
                                        <p class="text-base font-medium max-md:font-normal max-sm:text-sm">
                                            {{ item.name }}
                                        </p>
                                    </a>

                                    

                                    

                                    <template v-if="displayTax.prices == 'including_tax'">
                                        <p class="text-lg max-md:font-semibold max-sm:text-sm">
                                            {{ item.formatted_price_incl_tax }}
                                        </p>
                                    </template>

                                    <template v-else-if="displayTax.prices == 'both'">
                                        <p class="flex flex-col text-lg max-md:font-semibold max-sm:text-sm">
                                            {{ item.formatted_price_incl_tax }}

                                            <span class="text-xs font-normal text-zinc-500">
                                                Excl. Tax:
                                                <span class="font-medium text-black">{{ item.formatted_price }}</span>
                                            </span>
                                        </p>
                                    </template>

                                    <template v-else>
                                        <p class="text-lg max-md:font-semibold max-sm:text-sm">
                                            {{ item.formatted_price }}
                                        </p>
                                    </template>

                                    
                                </div>

                                <!-- Cart Item Options Container -->
                                <div
                                    class="grid select-none gap-x-2.5 gap-y-1.5 max-sm:gap-y-0.5"
                                    v-if="item.options.length"
                                >

                                    

                                    <!-- Details Toggler -->
                                    <div class="">
                                        <p
                                            class="flex cursor-pointer items-center gap-x-4 text-base max-md:gap-x-1.5 max-md:text-sm max-sm:text-xs"
                                            @click="item.option_show = ! item.option_show"
                                        >
                                            See Details
                                            <span
                                                class="text-2xl max-md:text-xl max-sm:text-lg"
                                                :class="{'icon-arrow-up': item.option_show, 'icon-arrow-down': ! item.option_show}"
                                            ></span>
                                        </p>
                                    </div>

                                    <!-- Option Details -->
                                    <div
                                        class="grid gap-2"
                                        v-show="item.option_show"
                                    >
                                        <template v-for="attribute in item.options">
                                            <div class="max-md:grid max-md:gap-0.5">
                                                <p class="text-sm font-medium text-zinc-500 max-md:font-normal max-sm:text-xs">
                                                    {{ attribute.attribute_name + ':' }}
                                                </p>

                                                <p class="text-sm max-sm:text-xs">
                                                    <template v-if="attribute?.attribute_type === 'file'">
                                                        <a
                                                            :href="attribute.file_url"
                                                            class="text-blue-700"
                                                            target="_blank"
                                                            :download="attribute.file_name"
                                                        >
                                                            {{ attribute.file_name }}
                                                        </a>
                                                    </template>

                                                    <template v-else>
                                                        {{ attribute.option_label }}
                                                    </template>
                                                </p>
                                            </div>
                                        </template>
                                    </div>

                                    
                                </div>

                                <div class="flex flex-wrap items-center gap-5 max-md:gap-2.5">
                                    

                                <!-- Cart Item Quantity Changer -->
                                <v-quantity-changer
    class="flex items-center border border-navyBlue max-h-9 max-w-[150px] gap-x-2.5 rounded-[54px] px-3.5 py-1.5 max-md:gap-x-2 max-md:px-1 max-md:py-0.5" :value="item?.quantity" @change="updateItem($event, item)"
    name="quantity"
    value="1"
    min-value="1"
>
</v-quantity-changer>


                                    

                                

                                <!-- Cart Item Remove Button -->
                                <button
                                    type="button"
                                    class="text-blue-700 max-md:text-sm"
                                    @click="removeItem(item.id)"
                                >
                                    Remove                                </button>

                                    
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Empty Cart Section -->
                    <div
                        class="mt-32 pb-8 max-md:mt-32"
                        v-else
                    >
                        <div class="b-0 grid place-items-center gap-y-5 max-md:gap-y-0">
                            <img
                                class="max-md:h-[100px] max-md:w-[100px]"
                                src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/thank-you-mhMpuEVL.png"
                            >

                            <p
                                class="text-xl max-md:text-sm"
                                role="heading"
                            >
                                Your cart is empty                            </p>
                        </div>
                    </div>
            </div>
        </template>
    
            <template v-slot:footer>
            <div class="pb-8 max-md:pb-2">
                <div
                    v-if="cart?.items?.length"
                    class="grid-col-1 grid gap-5 max-md:gap-2.5"
                >
                    <div
                        class="my-8 flex items-center justify-between border-b border-zinc-200 px-6 pb-2 max-md:my-0 max-md:border-t max-md:px-5 max-md:py-2"
                        :class="{'!justify-end': isLoading}"
                    >
                        

                        <template v-if="! isLoading">
                            <p class="text-sm font-medium text-zinc-500">
                                Subtotal                            </p>

                        <template v-if="displayTax.subtotal == 'including_tax'">
                            <p class="text-3xl font-semibold max-md:text-base">
                                {{ cart.formatted_sub_total_incl_tax }}
                            </p>
                        </template>

                        <template v-else-if="displayTax.subtotal == 'both'">
                            <p class="flex flex-col text-3xl font-semibold max-md:text-sm max-sm:text-right">
                                {{ cart.formatted_sub_total_incl_tax }}

                                <span class="text-sm font-normal text-zinc-500 max-sm:text-xs">
                                    Excl. Tax:
                                    <span class="font-medium text-black">{{ cart.formatted_sub_total }}</span>
                                </span>
                            </p>
                        </template>

                        <template v-else>
                            <p class="text-3xl font-semibold max-md:text-base">
                                {{ cart.formatted_sub_total }}
                            </p>
                        </template>
                    </template>

                        <template v-else>
                            <!-- Spinner -->
                            <svg
                                class="text-blue h-8 w-8 animate-spin text-[5px] font-semibold max-md:h-7 max-md:w-7 max-sm:h-4 max-sm:w-4"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                aria-hidden="true"
                                viewBox="0 0 24 24"
                            >
                                <circle
                                    class="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    stroke-width="4"
                                ></circle>

                                <path
                                    class="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                            </svg>
                        </template>

                            
                        </div>

                        

                        <!-- Cart Action Container -->
                        <div class="grid gap-2.5 px-6 max-md:px-4 max-sm:gap-1.5">
                            

                        <a
                            href="http://localhost/onlinestore/bagisto-2.3/public/checkout/onepage"
                            class="mx-auto block w-full cursor-pointer rounded-2xl bg-navyBlue px-11 py-4 text-center text-base font-medium text-white max-md:rounded-lg max-md:px-5 max-md:py-2"
                        >
                            Continue to Checkout                        </a>

                            

                            <div class="block cursor-pointer text-center text-base font-medium max-md:py-1.5">
                                <a href="http://localhost/onlinestore/bagisto-2.3/public/checkout/cart">
                                    View Cart                                </a>
                            </div>
                        </div>

                        
                    </div>
            </div>
        </template>
    </v-drawer>


        
        
    </script>

    <script type="module">
        app.component("v-mini-cart", {
            template: '#v-mini-cart-template',

            data() {
                return  {
                    cart: null,

                    isLoading:false,

                    displayTax: {
                        prices: "excluding_tax",
                        subtotal: "excluding_tax",
                    },
                }
            },

            mounted() {
                this.getCart();

                /**
                 * To Do: Implement this.
                 *
                 * Action.
                 */
                this.$emitter.on('update-mini-cart', (cart) => {
                    this.cart = cart;
                });
            },

            methods: {
                getCart() {
                    this.$axios.get('http://localhost/onlinestore/bagisto-2.3/public/api/checkout/cart')
                        .then(response => {
                            this.cart = response.data.data;
                        })
                        .catch(error => {});
                },

                updateItem(quantity, item) {
                    this.isLoading = true;

                    let qty = {};

                    qty[item.id] = quantity;

                    this.$axios.put('http://localhost/onlinestore/bagisto-2.3/public/api/checkout/cart', { qty })
                        .then(response => {
                            if (response.data.message) {
                                this.cart = response.data.data;
                            } else {
                                this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                            }

                            this.isLoading = false;
                        }).catch(error => this.isLoading = false);
                },

                removeItem(itemId) {
                    this.$emitter.emit('open-confirm-modal', {
                        agree: () => {
                            this.isLoading = true;

                            this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/api/checkout/cart', {
                                '_method': 'DELETE',
                                'cart_item_id': itemId,
                            })
                            .then(response => {
                                this.cart = response.data.data;

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });

                                this.isLoading = false;
                            })
                            .catch(error => {
                                this.$emitter.emit('add-flash', { type: 'error', message: response.data.message });

                                this.isLoading = false;
                            });
                        }
                    });
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-dropdown-template"
    >
        <div>
            <div
                class="select-none"
                ref="toggleBlock"
                @click="toggle()"
            >
                <slot name="toggle">Toggle</slot>
            </div>

            <transition
                tag="div"
                name="dropdown"
                enter-active-class="transition duration-100 ease-out"
                enter-from-class="scale-95 transform opacity-0"
                enter-to-class="scale-100 transform opacity-100"
                leave-active-class="transition duration-75 ease-in"
                leave-from-class="scale-100 transform opacity-100"
                leave-to-class="scale-95 transform opacity-0"
            >
                <div
                    class="absolute z-20 w-max rounded-[20px] bg-white shadow-[0px_10px_84px_rgba(0,0,0,0.1)] max-md:rounded-lg"
                    :style="positionStyles"
                    v-show="isActive"
                >
                    <slot name="content"></slot>

                    <slot name="menu"></slot>
                </div>
            </transition>
        </div>
    </script>

    <script type="module">
        app.component('v-dropdown', {
            template: '#v-dropdown-template',

            props: {
                position: String,

                closeOnClick: {
                    type: Boolean,
                    required: false,
                    default: true
                },
            },

            data() {
                return {
                    toggleBlockWidth: 0,

                    toggleBlockHeight: 0,

                    isActive: false,
                };
            },

            created() {
                window.addEventListener('click', this.handleFocusOut);
            },

            mounted() {
                this.toggleBlockWidth = this.$refs.toggleBlock.clientWidth;

                this.toggleBlockHeight = this.$refs.toggleBlock.clientHeight;
            },

            beforeDestroy() {
                window.removeEventListener('click', this.handleFocusOut);
            },

            computed: {
                positionStyles() {
                    switch (this.position) {
                        case 'bottom-left':
                            return [
                                `min-width: ${this.toggleBlockWidth}px`,
                                `top: ${this.toggleBlockHeight}px`,
                                'left: 0',
                            ];

                        case 'bottom-right':
                            return [
                                `min-width: ${this.toggleBlockWidth}px`,
                                `top: ${this.toggleBlockHeight}px`,
                                'right: 0',
                            ];

                        case 'top-left':
                            return [
                                `min-width: ${this.toggleBlockWidth}px`,
                                `bottom: ${this.toggleBlockHeight}px`,
                                'left: 0',
                            ];

                        case 'top-right':
                            return [
                                `min-width: ${this.toggleBlockWidth}px`,
                                `bottom: ${this.toggleBlockHeight}px`,
                                'right: 0',
                            ];

                        default:
                            return [
                                `min-width: ${this.toggleBlockWidth}px`,
                                `top: ${this.toggleBlockHeight}px`,
                                'left: 0',
                            ];
                    }
                },
            },

            methods: {
                toggle() {
                    /**
                     * If still somehow width is zero then this will check for width one more time.
                     */
                    if (this.toggleBlockWidth === 0) {
                        this.toggleBlockWidth = this.$refs.toggleBlock.clientWidth;
                    }

                    /**
                     * If still somehow height is zero then this will check for height one more time.
                     */
                    if (this.toggleBlockHeight === 0) {
                        this.toggleBlockHeight = this.$refs.toggleBlock.clientHeight;
                    }

                    this.isActive = ! this.isActive;
                },

                handleFocusOut(e) {
                    if (! this.$el.contains(e.target) || (this.closeOnClick && this.$el.children[1].contains(e.target))) {
                        this.isActive = false;
                    }
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-drawer-template"
    >
        <div>
            <!-- Toggler -->
            <div @click="open">
                <slot name="toggle"></slot>
            </div>

            <!-- Overlay -->
            <transition
                tag="div"
                name="drawer-overlay"
                enter-class="duration-300 ease-out"
                enter-from-class="opacity-0"
                enter-to-class="opacity-100"
                leave-class="duration-200 ease-in"
                leave-from-class="opacity-100"
                leave-to-class="opacity-0"
            >
                <div
                    class="fixed inset-0 z-20 bg-gray-500 bg-opacity-50 transition-opacity"
                    v-show="isOpen"
                ></div>
            </transition>

            <!-- Content -->
            <transition
                tag="div"
                name="drawer"
                :enter-from-class="enterFromLeaveToClasses"
                enter-active-class="transform transition duration-200 ease-in-out"
                enter-to-class="translate-x-0"
                leave-from-class="translate-x-0"
                leave-active-class="transform transition duration-200 ease-in-out"
                :leave-to-class="enterFromLeaveToClasses"
            >
                <div
                    class="fixed z-[1000] overflow-hidden bg-white max-md:!w-full"
                    :class="{
                        'inset-x-0 top-0': position == 'top',
                        'inset-x-0 bottom-0 max-sm:max-h-full': position == 'bottom',
                        'inset-y-0 ltr:right-0 rtl:left-0': position == 'right',
                        'inset-y-0 ltr:left-0 rtl:right-0': position == 'left'
                    }"
                    :style="'width:' + width"
                    v-show="isOpen"
                >
                    <div class="pointer-events-auto h-full w-full overflow-auto bg-white">
                        <div class="flex h-full w-full flex-col">
                            <div class="min-h-0 min-w-0 flex-1 overflow-auto">
                                <div class="flex h-full flex-col">
                                    <slot
                                        name="header"
                                        :close="close"
                                    >
                                        Default Header
                                    </slot>

                                    <!-- Content Slot -->
                                    <slot name="content"></slot>

                                    <!-- Footer Slot -->
                                    <slot name="footer"></slot>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </transition>
        </div>
    </script>

    <script type="module">
        app.component('v-drawer', {
            template: '#v-drawer-template',

            props: [
                'isActive',
                'position',
                'width'
            ],

            data() {
                return {
                    isOpen: this.isActive,
                };
            },

            watch: {
                isActive: function(newVal, oldVal) {
                    this.isOpen = newVal;
                }
            },

            computed: {
                enterFromLeaveToClasses() {
                    if (this.position == 'top') {
                        return '-translate-y-full';
                    } else if (this.position == 'bottom') {
                        return 'translate-y-full';
                    } else if (this.position == 'left') {
                        return 'ltr:-translate-x-full rtl:translate-x-full';
                    } else if (this.position == 'right') {
                        return 'ltr:translate-x-full rtl:-translate-x-full';
                    }
                }
            },

            methods: {
                toggle() {
                    this.isOpen = ! this.isOpen;

                    if (this.isOpen) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow ='auto';
                    }

                    document.body.style.paddingRight = '';

                    this.$emit('toggle', { isActive: this.isOpen });
                },

                open() {
                    this.isOpen = true;

                    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

                    document.body.style.overflow = 'hidden';

                    document.body.style.paddingRight = `${scrollbarWidth}px`;

                    this.$emit('open', { isActive: this.isOpen });
                },

                close() {
                    this.isOpen = false;

                    document.body.style.overflow = 'auto';

                    document.body.style.paddingRight = '';

                    this.$emit('close', { isActive: this.isOpen });
                }
            },
        });
    </script>

        
        <script>
            /**
             * Load event, the purpose of using the event is to mount the application
             * after all of our `Vue` components which is present in blade file have
             * been registered in the app. No matter what `app.mount()` should be
             * called in the last.
             */
            window.addEventListener("load", function (event) {
                app.mount("#app");
            });
        </script>

        

        <script type="text/javascript">
            
        </script>
    </body>
</html>
";s:4:"type";s:6:"normal";}";