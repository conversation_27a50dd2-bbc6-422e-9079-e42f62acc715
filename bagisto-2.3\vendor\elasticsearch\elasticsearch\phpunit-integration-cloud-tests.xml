<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd" bootstrap="vendor/autoload.php" colors="true" failOnRisky="true" verbose="true" beStrictAboutChangesToGlobalState="true" beStrictAboutOutputDuringTests="true" beStrictAboutTestsThatDoNotTestAnything="false">
  <coverage>
    <include>
      <directory suffix=".php">src</directory>
    </include>
  </coverage>
  <php>
    <ini name="memory_limit" value="-1"/>
  </php>
  <testsuites>
    <testsuite name="Elastic Cloud tests">
      <directory>tests/Integration</directory>
    </testsuite>
  </testsuites>
  <groups>
    <include>
      <group>cloud</group>
    </include>
  </groups>
  <logging>
    <junit outputFile="tests/yaml-test-junit.xml"/>
  </logging>
</phpunit>
