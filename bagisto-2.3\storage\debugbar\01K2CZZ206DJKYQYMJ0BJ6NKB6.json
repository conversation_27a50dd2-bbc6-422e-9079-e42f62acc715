{"__meta": {"id": "01K2CZZ206DJKYQYMJ0BJ6NKB6", "datetime": "2025-08-11 22:08:37", "utime": **********.319702, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/cache/original/product/11/efHA8AczeIrNc1oaYw9hpIVDillxjmmEooocShEA.webp", "ip": "::1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754930316.491542, "end": **********.367438, "duration": 0.8758959770202637, "duration_str": "876ms", "measures": [{"label": "Booting", "start": 1754930316.491542, "relative_start": 0, "end": **********.251119, "relative_end": **********.251119, "duration": 0.****************, "duration_str": "760ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.251143, "relative_start": 0.****************, "end": **********.367441, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.292782, "relative_start": 0.****************, "end": **********.305011, "relative_end": **********.305011, "duration": 0.012228965759277344, "duration_str": "12.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.314065, "relative_start": 0.****************, "end": **********.314426, "relative_end": **********.314426, "duration": 0.0003609657287597656, "duration_str": "361μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.314465, "relative_start": 0.***************, "end": **********.31453, "relative_end": **********.31453, "duration": 6.4849853515625e-05, "duration_str": "65μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/cache/original/product/11/efHA8AczeIrNc1oaYw9hpIVDil...", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "875ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-781450618 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-781450618\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-430974598 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-430974598\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-757803670 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"101 characters\">http://localhost/onlinestore/bagisto-2.3/public/omniheat-mens-solid-hooded-puffer-jacket-blue-green-l</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1090 characters\">bagisto_session=eyJpdiI6ImpDWlNrdnV1KzVRcHJsU1NOQ1RaRHc9PSIsInZhbHVlIjoiNit6V3FHZEdhMzRvcGk4WnFBZUlOK09WTVZxNm5NbElac2JoaE5hV1krZGFOeExFMVpLZVcyMVdJOUFkdk0rRVZZczB3bkF0R0hxRzVjdnMrUm1KU3pkNmlHeGJYT2JuNXVyblByaUg1Qlozc3BmaEFzMkZEZmx4VzBTT3lydnEiLCJtYWMiOiIyYzI4MGJhZTYyZDJkN2I1MDdjMGE4YjIxM2M1ZGJiOTFiYWY3OGYxNGU5ZGIyZTg4ODlkN2U5NTY4ZGIwYTg4IiwidGFnIjoiIn0%3D; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImhmN1h2L1JCUG0xREUvYXdvbGd5aVE9PSIsInZhbHVlIjoidm1JdUJCSkJESmFRMlNEeEQyNGxJSzJlWWlydXpMeXMxc1pHTi84bXh5dGlXNEZ0MmdMYWQ5TmpmQ29vckFndWc0aDJEZkE0U2Z5QTBnZC91dGZrVTdibGQ5Wm9QZm84REVwTVFtVUduSWU5bjNnVysxanRvNXpwVWNTZ1FIOTgiLCJtYWMiOiIzZGM3ODMxODQ1NjNmNjQwZjQ2NDdmMGExNjg5NjhlM2M1NzEwNDM5MDkxMWUwYjI1MTlhNDY3NTA2OTkxNGZlIiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6Ikk2TXNPbUQ0a29zbURSbzBYdlE0Z0E9PSIsInZhbHVlIjoiV1U5SDQybmdKMjRHQmtNV21ZWUJjNXllYmZjZFErS1VMa0J5YmNFZEJKaHV6emVUVzBEV21VZFBMellVRWd5VEJ5SEI1ZC92NVJCMStZazhTVG82bVlWcERxOXozam1abkZabUJQSUlsSGgrUDJnQTB3eGFFQkY1SnJITWR4bWwiLCJtYWMiOiJjMTYxNTRmOTM5ODYwNDBhYzRjNzYxYmI4ZjlmYjQ4ZjViZDExMjgzMGZjYTA3ZWYzNjM5MDNmMGVkNmE3NDJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757803670\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1828894064 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImpDWlNrdnV1KzVRcHJsU1NOQ1RaRHc9PSIsInZhbHVlIjoiNit6V3FHZEdhMzRvcGk4WnFBZUlOK09WTVZxNm5NbElac2JoaE5hV1krZGFOeExFMVpLZVcyMVdJOUFkdk0rRVZZczB3bkF0R0hxRzVjdnMrUm1KU3pkNmlHeGJYT2JuNXVyblByaUg1Qlozc3BmaEFzMkZEZmx4VzBTT3lydnEiLCJtYWMiOiIyYzI4MGJhZTYyZDJkN2I1MDdjMGE4YjIxM2M1ZGJiOTFiYWY3OGYxNGU5ZGIyZTg4ODlkN2U5NTY4ZGIwYTg4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImhmN1h2L1JCUG0xREUvYXdvbGd5aVE9PSIsInZhbHVlIjoidm1JdUJCSkJESmFRMlNEeEQyNGxJSzJlWWlydXpMeXMxc1pHTi84bXh5dGlXNEZ0MmdMYWQ5TmpmQ29vckFndWc0aDJEZkE0U2Z5QTBnZC91dGZrVTdibGQ5Wm9QZm84REVwTVFtVUduSWU5bjNnVysxanRvNXpwVWNTZ1FIOTgiLCJtYWMiOiIzZGM3ODMxODQ1NjNmNjQwZjQ2NDdmMGExNjg5NjhlM2M1NzEwNDM5MDkxMWUwYjI1MTlhNDY3NTA2OTkxNGZlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ikk2TXNPbUQ0a29zbURSbzBYdlE0Z0E9PSIsInZhbHVlIjoiV1U5SDQybmdKMjRHQmtNV21ZWUJjNXllYmZjZFErS1VMa0J5YmNFZEJKaHV6emVUVzBEV21VZFBMellVRWd5VEJ5SEI1ZC92NVJCMStZazhTVG82bVlWcERxOXozam1abkZabUJQSUlsSGgrUDJnQTB3eGFFQkY1SnJITWR4bWwiLCJtYWMiOiJjMTYxNTRmOTM5ODYwNDBhYzRjNzYxYmI4ZjlmYjQ4ZjViZDExMjgzMGZjYTA3ZWYzNjM5MDNmMGVkNmE3NDJlIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828894064\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-426009971 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">24766</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">dcaa77642e28a735cf2cab8270864e7a</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 16:38:37 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-426009971\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2097528693 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2097528693\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/cache/original/product/11/efHA8AczeIrNc1oaYw9hpIVDil...", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}