1755577834s:237840:"a:4:{s:10:"statusCode";i:200;s:7:"headers";O:50:"Symfony\Component\HttpFoundation\ResponseHeaderBag":5:{s:10:" * headers";a:4:{s:12:"content-type";a:1:{i:0;s:24:"text/html; charset=UTF-8";}s:13:"cache-control";a:1:{i:0;s:17:"no-cache, private";}s:4:"date";a:1:{i:0;s:29:"Tu<PERSON>, 12 Aug 2025 04:30:27 GMT";}s:21:"laravel-responsecache";a:1:{i:0;s:31:"Tu<PERSON>, 12 Aug 2025 10:00:34 +0530";}}s:15:" * cacheControl";a:0:{}s:23:" * computedCacheControl";a:2:{s:8:"no-cache";b:1;s:7:"private";b:1;}s:10:" * cookies";a:0:{}s:14:" * headerNames";a:4:{s:12:"content-type";s:12:"Content-Type";s:13:"cache-control";s:13:"Cache-Control";s:4:"date";s:4:"Date";s:21:"laravel-responsecache";s:21:"laravel-responsecache";}}s:7:"content";s:237089:"<!-- SEO Meta Content -->

<!DOCTYPE html>

<html
    lang="en"
    dir="ltr"
>
    <head>

        

        <title>Men Category Meta Title</title>

        <meta charset="UTF-8">

        <meta
            http-equiv="X-UA-Compatible"
            content="IE=edge"
        >
        <meta
            http-equiv="content-language"
            content="en"
        >

        <meta
            name="viewport"
            content="width=device-width, initial-scale=1"
        >
        <meta
            name="base-url"
            content="http://localhost/onlinestore/bagisto-2.3/public"
        >
        <meta
            name="currency"
            content="{&quot;id&quot;:1,&quot;code&quot;:&quot;INR&quot;,&quot;name&quot;:&quot;Indian Rupee&quot;,&quot;symbol&quot;:&quot;\u20b9&quot;,&quot;decimal&quot;:2,&quot;group_separator&quot;:&quot;,&quot;,&quot;decimal_separator&quot;:&quot;.&quot;,&quot;currency_position&quot;:null,&quot;created_at&quot;:null,&quot;updated_at&quot;:null}"
        >

            <meta
        name="description"
        content="Men Category Meta Description"
    />

    <meta
        name="keywords"
        content="Men Category Meta Keywords"
    />

    
        <link
            rel="icon"
            sizes="16x16"
            href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/favicon-Df9chQdB.ico"
        />

        <link rel="preload" as="style" href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-F65DyJd8.css" /><link rel="preload" as="style" href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-DsP8OK1c.css" /><link rel="modulepreload" href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-zmUk0CX0.js" /><link rel="stylesheet" href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-F65DyJd8.css" /><link rel="stylesheet" href="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-DsP8OK1c.css" /><script type="module" src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/app-zmUk0CX0.js"></script>
        <link
            rel="preload"
            href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap"
            as="style"
        >
        <link
            rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap"
        >

        <link
            rel="preload"
            href="https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap"
            as="style"
        >
        <link
            rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap"
        >

        
        <style>
            
        </style>

                    <script type="speculationrules">
                {"prerender":[{"source":"document","where":{"and":[{"href_matches":"/*"},{"not":{"href_matches":"account"}},{"not":{"href_matches":"checkout"}},{"not":{"href_matches":"onepage"}},{"not":{"href_matches":"cart"}}]},"eagerness":"moderate"}]}            </script>
        
        

    </head>

    <body>
        

        <a
            href="#main"
            class="skip-to-main-content-link"
        >
            Skip to main content
        </a>

        <div id="app">
            <!-- Flash Message Blade Component -->
            <v-flash-group ref='flashes'></v-flash-group>


            <!-- Confirm Modal Blade Component -->
            <v-modal-confirm ref="confirmModal"></v-modal-confirm>


            <!-- Page Header Blade Component -->
                            <header class="shadow-gray sticky top-0 z-10 bg-white shadow-sm max-lg:shadow-none">
    <div class="flex flex-wrap max-lg:hidden">
    <div class="flex min-h-[78px] w-full justify-between border border-b border-l-0 border-r-0 border-t-0 px-[60px] max-1180:px-8">
    <!--
        This section will provide categories for the first, second, and third levels. If
        additional levels are required, users can customize them according to their needs.
    -->
    <!-- Left Nagivation Section -->
    <div class="flex items-center gap-x-10 max-[1180px]:gap-x-5">
        

        <a
            href="http://localhost/onlinestore/bagisto-2.3/public"
            aria-label="Bagisto"
        >
            <img
                src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/logo-CZWQQgOF.svg"
                width="131"
                height="29"
                alt="OnlineStore"
            >
        </a>

        

        

        <v-desktop-category>
            <div class="flex items-center gap-5">
                <span
                    class="shimmer h-6 w-20 rounded"
                    role="presentation"
                ></span>

                <span
                    class="shimmer h-6 w-20 rounded"
                    role="presentation"
                ></span>

                <span
                    class="shimmer h-6 w-20 rounded"
                    role="presentation"
                ></span>
            </div>
        </v-desktop-category>

        
    </div>

    <!-- Right Nagivation Section -->
    <div class="flex items-center gap-x-9 max-[1100px]:gap-x-6 max-lg:gap-x-8">

        

        <!-- Search Bar Container -->
        <div class="relative w-full">
            <form
                action="http://localhost/onlinestore/bagisto-2.3/public/search"
                class="flex max-w-[445px] items-center"
                role="search"
            >
                <label
                    for="organic-search"
                    class="sr-only"
                >
                    Search                </label>

                <div class="icon-search pointer-events-none absolute top-2.5 flex items-center text-xl ltr:left-3 rtl:right-3"></div>

                <input
                    type="text"
                    name="query"
                    value=""
                    class="block w-full rounded-lg border border-transparent bg-zinc-100 px-11 py-3 text-xs font-medium text-gray-900 transition-all hover:border-gray-400 focus:border-gray-400"
                    minlength="0"
                    maxlength="1000"
                    placeholder="Search products here"
                    aria-label="Search products here"
                    aria-required="true"
                    pattern="[^\\]+"
                    required
                >

                <button
                    type="submit"
                    class="hidden"
                    aria-label="Submit"
                >
                </button>

                                    <v-image-search>
    <button type="button"
        class="icon-camera absolute top-3 flex items-center text-xl max-sm:top-2.5 ltr:right-3 ltr:pr-3 max-md:ltr:right-1.5 rtl:left-3 rtl:pl-3 max-md:rtl:left-1.5"
        aria-label="Search">
    </button>
</v-image-search>

                            </form>
        </div>

        

        <!-- Right Navigation Links -->
        <div class="mt-1.5 flex gap-x-8 max-[1100px]:gap-x-6 max-lg:gap-x-8">

            

            <!-- Compare -->
                            <a
                    href="http://localhost/onlinestore/bagisto-2.3/public/compare"
                    aria-label="Compare"
                >
                    <span
                        class="icon-compare inline-block cursor-pointer text-2xl"
                        role="presentation"
                    ></span>
                </a>
            
            

            

            <!-- Mini cart -->
                            <!-- Mini Cart Vue Component -->
<v-mini-cart>
    <span
        class="icon-cart cursor-pointer text-2xl"
        role="button"
        aria-label="Shopping Cart"
    ></span>
</v-mini-cart>

            
            

            

            <!-- user profile -->
            <v-dropdown position="bottom-right" class="relative">
            <span
                        class="icon-users inline-block cursor-pointer text-2xl"
                        role="button"
                        aria-label="Profile"
                        tabindex="0"
                    ></span>

        <template v-slot:toggle>
            <span
                        class="icon-users inline-block cursor-pointer text-2xl"
                        role="button"
                        aria-label="Profile"
                        tabindex="0"
                    ></span>
        </template>
    
            <template v-slot:content>
            <div class="p-5 !p-0">
                <div class="grid gap-2.5 p-5 pb-0">
                            <p class="font-dmserif text-xl">
                                Welcome’
                                Vignesh
                            </p>

                            <p class="text-sm">
                                Manage Cart, Orders & Wishlist                            </p>
                        </div>

                        <p class="mt-3 w-full border border-zinc-200"></p>

                        <div class="mt-2.5 grid gap-1 pb-2.5">
                            

                            <a
                                class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/profile"
                            >
                                Profile                            </a>

                            <a
                                class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/orders"
                            >
                                Orders                            </a>

                                                            <a
                                    class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                    href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/wishlist"
                                >
                                    Wishlist                                </a>
                            
                            <!--Customers logout-->
                                                            <!--
    If a component has the `as` attribute, it indicates that it uses
    the ajaxified form or some customized slot form.
-->
    
    
    <v-form
        method="POST"
        :initial-errors="[]"
        v-slot="{ meta, errors }"
        action="http://localhost/onlinestore/bagisto-2.3/public/customer/logout" id="customerLogout"
    >
                    <input type="hidden" name="_token" value="<laravel-responsecache-csrf-token-here>" autocomplete="off">        
                    <input type="hidden" name="_method" value="DELETE">        
        
    </v-form>

                                <a
                                    class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100"
                                    href="http://localhost/onlinestore/bagisto-2.3/public/customer/logout"
                                    onclick="event.preventDefault(); document.getElementById('customerLogout').submit();"
                                >
                                    Logout                                </a>
                            
                            
                        </div>
            </div>
        </template>
    
    </v-dropdown>


            
        </div>
    </div>
</div>


</div>

    <!--
    This code needs to be refactored to reduce the amount of PHP in the Blade
    template as much as possible.
-->

<div class="flex flex-wrap gap-4 px-4 pb-4 pt-6 shadow-sm lg:hidden">
    <div class="flex w-full items-center justify-between">
        <!-- Left Navigation -->
        <div class="flex items-center gap-x-1.5">
            

            <!-- Drawer -->
            <v-mobile-drawer></v-mobile-drawer>

            

            

            <a
                href="http://localhost/onlinestore/bagisto-2.3/public"
                class="max-h-[30px]"
                aria-label="Bagisto"
            >
                <img
                    src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/logo-CZWQQgOF.svg"
                    alt="OnlineStore"
                    width="131"
                    height="29"
                >
            </a>

            
        </div>

        <!-- Right Navigation -->
        <div>
            <div class="flex items-center gap-x-5 max-md:gap-x-4">
                

                                    <a
                        href="http://localhost/onlinestore/bagisto-2.3/public/compare"
                        aria-label="Compare"
                    >
                        <span class="icon-compare cursor-pointer text-2xl"></span>
                    </a>
                
                

                

                                    <!-- Mini Cart Vue Component -->
<v-mini-cart>
    <span
        class="icon-cart cursor-pointer text-2xl"
        role="button"
        aria-label="Shopping Cart"
    ></span>
</v-mini-cart>

                
                

                <!-- For Large screens -->
                <div class="max-md:hidden">
                    <v-dropdown position="bottom-right" class="relative">
            <span class="icon-users cursor-pointer text-2xl"></span>

        <template v-slot:toggle>
            <span class="icon-users cursor-pointer text-2xl"></span>
        </template>
    
            <template v-slot:content>
            <div class="p-5 !p-0">
                <div class="grid gap-2.5 p-5 pb-0">
                                    <p class="font-dmserif text-xl">
                                        Welcome’
                                        Vignesh
                                    </p>

                                    <p class="text-sm">
                                        Manage Cart, Orders & Wishlist                                    </p>
                                </div>

                                <p class="mt-3 w-full border border-zinc-200"></p>

                                <div class="mt-2.5 grid gap-1 pb-2.5">
                                    

                                    <a
                                        class="cursor-pointer px-5 py-2 text-base"
                                        href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/profile"
                                    >
                                        Profile                                    </a>

                                    <a
                                        class="cursor-pointer px-5 py-2 text-base"
                                        href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/orders"
                                    >
                                        Orders                                    </a>

                                                                            <a
                                            class="cursor-pointer px-5 py-2 text-base"
                                            href="http://localhost/onlinestore/bagisto-2.3/public/customer/account/wishlist"
                                        >
                                            Wishlist                                        </a>
                                    
                                    <!--Customers logout-->
                                                                            <!--
    If a component has the `as` attribute, it indicates that it uses
    the ajaxified form or some customized slot form.
-->
    
    
    <v-form
        method="POST"
        :initial-errors="[]"
        v-slot="{ meta, errors }"
        action="http://localhost/onlinestore/bagisto-2.3/public/customer/logout" id="customerLogout"
    >
                    <input type="hidden" name="_token" value="<laravel-responsecache-csrf-token-here>" autocomplete="off">        
                    <input type="hidden" name="_method" value="DELETE">        
        
    </v-form>

                                        <a
                                            class="cursor-pointer px-5 py-2 text-base"
                                            href="http://localhost/onlinestore/bagisto-2.3/public/customer/logout"
                                            onclick="event.preventDefault(); document.getElementById('customerLogout').submit();"
                                        >
                                            Logout                                        </a>
                                    
                                    
                                </div>
            </div>
        </template>
    
    </v-dropdown>

                </div>

                <!-- For Medium and small screen -->
                <div class="md:hidden">
                    
                    <!-- Customers Dropdown -->
                                            <a
                            href="http://localhost/onlinestore/bagisto-2.3/public/customer/account"
                            aria-label="Account"
                        >
                            <span class="icon-users cursor-pointer text-2xl"></span>
                        </a>
                                    </div>
            </div>
        </div>
    </div>

    

    <!-- Serach Catalog Form -->
    <form action="http://localhost/onlinestore/bagisto-2.3/public/search" class="flex w-full items-center">
        <label
            for="organic-search"
            class="sr-only"
        >
            Search        </label>

        <div class="relative w-full">
            <div class="icon-search pointer-events-none absolute top-3 flex items-center text-2xl max-md:text-xl max-sm:top-2.5 ltr:left-3 rtl:right-3"></div>

            <input
                type="text"
                class="block w-full rounded-xl border border-['#E3E3E3'] px-11 py-3.5 text-sm font-medium text-gray-900 max-md:rounded-lg max-md:px-10 max-md:py-3 max-md:font-normal max-sm:text-xs"
                name="query"
                value=""
                placeholder="Search products here"
                required
            >

                            <v-image-search>
    <button type="button"
        class="icon-camera absolute top-3 flex items-center text-xl max-sm:top-2.5 ltr:right-3 ltr:pr-3 max-md:ltr:right-1.5 rtl:left-3 rtl:pl-3 max-md:rtl:left-1.5"
        aria-label="Search">
    </button>
</v-image-search>

                    </div>
    </form>

    
</div>

</header>


            
            
            

            <!-- Page Content Blade Component -->
            <main id="main" class="bg-white">
                <!-- Page Title -->
     
    

    <!-- Hero Image -->
    
    

    

                        <div class="container mt-[34px] px-[60px] max-lg:px-8 max-md:mt-4 max-md:px-4 max-md:text-sm max-sm:text-xs">
                Men Category Description
            </div>
            
    

            <!-- Category Vue Component -->
        <v-category>
            <!-- Category Shimmer Effect -->
            <div class="container px-[60px] max-lg:px-8 max-sm:px-4">
    <div class="flex items-start gap-10 max-lg:gap-5 md:mt-10">
        <!-- Desktop Filter Shimmer Effect -->
        <div class="max-md:hidden">
            <div class="panel-side journal-scroll grid max-h-[1320px] min-w-[342px] max-w-[400px] grid-cols-[1fr] overflow-y-auto overflow-x-hidden max-xl:min-w-[270px] ltr:pr-7 rtl:pl-7">
    <div class="flex h-[50px] items-center justify-between border-b border-zinc-200 py-2.5 max-md:hidden">
        <p class="shimmer h-6 w-[30%]"></p>
        <p class="shimmer h-5 w-1/5"></p>
    </div>

    <!-- Price Range Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-7 w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="z-10 rounded-lg bg-white">
            <div>
    <div class="mt-1 flex items-center gap-4">
        <p class="shimmer h-5 w-12"></p>

        <p class="shimmer h-5 w-28"></p>
    </div>

    <!-- Price range slider effect -->
    <div class="relative mx-auto flex h-20 w-full items-center justify-center p-2">
        <div class="shimmer relative h-1 w-full rounded-2xl bg-gray-200">
            <div class="shimmer absolute -left-1 -top-2.5 h-6 w-6 rounded-full"></div>
            <div class="shimmer absolute -right-1 -top-2.5 h-6 w-6 rounded-full"></div>
        </div>
    </div>
</div>
        </div>
    </div>

    <!-- Checkbox Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-[27px] w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="flex flex-col items-center justify-between gap-2">
            <p class="shimmer h-[52px] w-full rounded-xl"></p>

            <div class="shimmer h-5 w-[50%] self-end rounded"></div>
        </div>

        <div class="z-10 grid gap-1 rounded-lg bg-white pb-3">
            <div class="flex items-center gap-x-4 ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Checkbox Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-[27px] w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="flex flex-col items-center justify-between gap-2">
            <p class="shimmer h-[52px] w-full rounded-xl"></p>

            <div class="shimmer h-5 w-[50%] self-end rounded"></div>
        </div>

        <div class="z-10 grid gap-1 rounded-lg bg-white pb-3">
            <div class="flex items-center gap-x-4 ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>
        </div>
    </div>
</div>
        </div>

        <div class="flex-1">
            <!-- Desktop Toolbar Shimmer Effect -->
            <div class="max-md:hidden">
                <div class="flex justify-between max-md:hidden max-md:items-center">
    <div class="hidden max-md:block"></div>

    <div class="shimmer h-[54px] w-[185px] rounded-lg"></div>

    <div class="flex items-center gap-10 max-md:hidden">
        <div class="shimmer h-[54px] w-[84px] rounded-lg"></div>

        <div class="flex items-center gap-5">
            <span class="shimmer h-6 w-6"></span>
            <span class="shimmer h-6 w-6"></span>
        </div>
    </div>
</div>            </div>

            <!-- Product Card Container -->
                            <div class="mt-8 grid grid-cols-3 gap-8 max-1060:grid-cols-2 max-md:mt-5 max-md:justify-items-center max-md:gap-x-4 max-md:gap-y-5">
                    <!-- Product Card Shimmer Effect -->
                    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
                </div> 
            
            <button class="shimmer mx-auto mt-14 block h-12 w-[171.516px] rounded-2xl py-3"></button>
        </div>
    </div>
</div>        </v-category>
            </main>

            


            <!-- Page Services Blade Component -->
                            <!--
    The ThemeCustomizationRepository repository is injected directly here because there is no way
    to retrieve it from the view composer, as this is an anonymous component.
-->


<!-- Features -->
    <div class="container mt-20 max-lg:px-8 max-md:mt-10 max-md:px-4">
        <div class="max-md:max-y-6 flex justify-center gap-6 max-lg:flex-wrap max-md:grid max-md:grid-cols-2 max-md:gap-x-2.5 max-md:text-center">
                            <div class="flex items-center gap-5 bg-white max-md:grid max-md:gap-2.5 max-sm:gap-1 max-sm:px-2">
                    <span
                        class="icon-truck flex items-center justify-center w-[60px] h-[60px] bg-white border border-black rounded-full text-4xl text-navyBlue p-2.5 max-md:m-auto max-md:w-16 max-md:h-16 max-sm:w-10 max-sm:h-10 max-sm:text-2xl"
                        role="presentation"
                    ></span>

                    <div class="max-lg:grid max-lg:justify-center">
                        <!-- Service Title -->
                        <p class="font-dmserif text-base font-medium max-md:text-xl max-sm:text-sm">Free Shipping</p>

                        <!-- Service Description -->
                        <p class="mt-2.5 max-w-[217px] text-sm font-medium text-zinc-500 max-md:mt-0 max-md:text-base max-sm:text-xs">
                            Enjoy free shipping on all orders
                        </p>
                    </div>
                </div>
                            <div class="flex items-center gap-5 bg-white max-md:grid max-md:gap-2.5 max-sm:gap-1 max-sm:px-2">
                    <span
                        class="icon-product flex items-center justify-center w-[60px] h-[60px] bg-white border border-black rounded-full text-4xl text-navyBlue p-2.5 max-md:m-auto max-md:w-16 max-md:h-16 max-sm:w-10 max-sm:h-10 max-sm:text-2xl"
                        role="presentation"
                    ></span>

                    <div class="max-lg:grid max-lg:justify-center">
                        <!-- Service Title -->
                        <p class="font-dmserif text-base font-medium max-md:text-xl max-sm:text-sm">Product Replace</p>

                        <!-- Service Description -->
                        <p class="mt-2.5 max-w-[217px] text-sm font-medium text-zinc-500 max-md:mt-0 max-md:text-base max-sm:text-xs">
                            Easy Product Replacement Available!
                        </p>
                    </div>
                </div>
                            <div class="flex items-center gap-5 bg-white max-md:grid max-md:gap-2.5 max-sm:gap-1 max-sm:px-2">
                    <span
                        class="icon-dollar-sign flex items-center justify-center w-[60px] h-[60px] bg-white border border-black rounded-full text-4xl text-navyBlue p-2.5 max-md:m-auto max-md:w-16 max-md:h-16 max-sm:w-10 max-sm:h-10 max-sm:text-2xl"
                        role="presentation"
                    ></span>

                    <div class="max-lg:grid max-lg:justify-center">
                        <!-- Service Title -->
                        <p class="font-dmserif text-base font-medium max-md:text-xl max-sm:text-sm">Emi Available</p>

                        <!-- Service Description -->
                        <p class="mt-2.5 max-w-[217px] text-sm font-medium text-zinc-500 max-md:mt-0 max-md:text-base max-sm:text-xs">
                            No cost EMI available on all major credit cards
                        </p>
                    </div>
                </div>
                            <div class="flex items-center gap-5 bg-white max-md:grid max-md:gap-2.5 max-sm:gap-1 max-sm:px-2">
                    <span
                        class="icon-support flex items-center justify-center w-[60px] h-[60px] bg-white border border-black rounded-full text-4xl text-navyBlue p-2.5 max-md:m-auto max-md:w-16 max-md:h-16 max-sm:w-10 max-sm:h-10 max-sm:text-2xl"
                        role="presentation"
                    ></span>

                    <div class="max-lg:grid max-lg:justify-center">
                        <!-- Service Title -->
                        <p class="font-dmserif text-base font-medium max-md:text-xl max-sm:text-sm">24/7 Support</p>

                        <!-- Service Description -->
                        <p class="mt-2.5 max-w-[217px] text-sm font-medium text-zinc-500 max-md:mt-0 max-md:text-base max-sm:text-xs">
                            Dedicated 24/7 support via chat and email
                        </p>
                    </div>
                </div>
                    </div>
    </div>

            
            <!-- Page Footer Blade Component -->
                            <!--
    The category repository is injected directly here because there is no way
    to retrieve it from the view composer, as this is an anonymous component.
-->

<!--
    This code needs to be refactored to reduce the amount of PHP in the Blade
    template as much as possible.
-->

<footer class="mt-9 bg-lightOrange max-sm:mt-10">
    <div class="flex justify-between gap-x-6 gap-y-8 p-[60px] max-1060:flex-col-reverse max-md:gap-5 max-md:p-8 max-sm:px-4 max-sm:py-5">
        <!-- For Desktop View -->
        <div class="flex flex-wrap items-start gap-24 max-1180:gap-6 max-1060:hidden">
                                                <ul class="grid gap-5 text-sm">
                        
                                                    <li>
                                <a href="http://localhost/page/about-us">
                                    About Us
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/contact-us">
                                    Contact Us
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/customer-service">
                                    Customer Service
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/whats-new">
                                    What&#039;s New
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/terms-of-use">
                                    Terms of Use
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/terms-conditions">
                                    Terms &amp; Conditions
                                </a>
                            </li>
                                            </ul>
                                    <ul class="grid gap-5 text-sm">
                        
                                                    <li>
                                <a href="http://localhost/page/privacy-policy">
                                    Privacy Policy
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/payment-policy">
                                    Payment Policy
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/shipping-policy">
                                    Shipping Policy
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/refund-policy">
                                    Refund Policy
                                </a>
                            </li>
                                                    <li>
                                <a href="http://localhost/page/return-policy">
                                    Return Policy
                                </a>
                            </li>
                                            </ul>
                                    </div>

        <!-- For Mobile view -->
        <div class="border-b border-zinc-200 hidden !w-full rounded-xl !border-2 !border-[#e9decc] max-1060:block max-sm:rounded-lg">
    <v-accordion
        
        is-active=""
    >
                    <template v-slot:header="{ toggle, isOpen }">
                <div
                    class="flex cursor-pointer select-none items-center justify-between p-4 rounded-t-lg bg-[#F1EADF] font-medium max-md:p-2.5 max-sm:px-3 max-sm:py-2 max-sm:text-sm"
                    role="button"
                    tabindex="0"
                    @click="toggle"
                >
                    Footer Content

                    <span
                        v-bind:class="isOpen ? 'icon-arrow-up text-2xl' : 'icon-arrow-down text-2xl'"
                        role="button"
                        aria-label="Toggle accordion"
                        tabindex="0"
                    ></span>
                </div>
            </template>
        
                    <template v-slot:content="{ isOpen }">
                <div
                    class="z-10 rounded-lg bg-white p-1.5 flex justify-between !bg-transparent !p-4"
                    v-show="isOpen"
                >
                    <ul class="grid gap-5 text-sm">
                            
                                                            <li>
                                    <a
                                        href="http://localhost/page/about-us"
                                        class="text-sm font-medium max-sm:text-xs">
                                        About Us
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/contact-us"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Contact Us
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/customer-service"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Customer Service
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/whats-new"
                                        class="text-sm font-medium max-sm:text-xs">
                                        What&#039;s New
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/terms-of-use"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Terms of Use
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/terms-conditions"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Terms &amp; Conditions
                                    </a>
                                </li>
                                                    </ul>
                                            <ul class="grid gap-5 text-sm">
                            
                                                            <li>
                                    <a
                                        href="http://localhost/page/privacy-policy"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Privacy Policy
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/payment-policy"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Payment Policy
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/shipping-policy"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Shipping Policy
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/refund-policy"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Refund Policy
                                    </a>
                                </li>
                                                            <li>
                                    <a
                                        href="http://localhost/page/return-policy"
                                        class="text-sm font-medium max-sm:text-xs">
                                        Return Policy
                                    </a>
                                </li>
                                                    </ul>
                </div>
            </template>
            </v-accordion>
</div>


        

        <!-- News Letter subscription -->
                    <div class="grid gap-2.5">
                <p
                    class="max-w-[288px] text-3xl italic leading-[45px] text-navyBlue max-md:text-2xl max-sm:text-lg"
                    role="heading"
                    aria-level="2"
                >
                    Get Ready for our Fun Newsletter!                </p>

                <p class="text-xs">
                    Subscribe to stay in touch.                </p>

                <div>
                    <!--
    If a component has the `as` attribute, it indicates that it uses
    the ajaxified form or some customized slot form.
-->
    
    
    <v-form
        method="POST"
        :initial-errors="[]"
        v-slot="{ meta, errors }"
        action="http://localhost/onlinestore/bagisto-2.3/public/subscription" class="mt-2.5 rounded max-sm:mt-0"
    >
                    <input type="hidden" name="_token" value="<laravel-responsecache-csrf-token-here>" autocomplete="off">        
        
        <div class="relative w-full">
                            <v-field
            v-slot="{ field, errors }"
            rules="required|email" label="Email"
            name="email"
        >
            <input
                type="email"
                name="email"
                v-bind="field"
                :class="[errors.length ? 'border !border-red-500 hover:border-red-500' : '']"
                class="mb-1.5 w-full rounded-lg border px-5 py-3 text-base font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm block w-[420px] max-w-full rounded-xl border-2 border-[#e9decc] bg-[#F1EADF] px-5 py-4 text-base max-1060:w-full max-md:p-3.5 max-sm:mb-0 max-sm:rounded-lg max-sm:border-2 max-sm:p-2 max-sm:text-sm" aria-label="Email" placeholder="<EMAIL>"
            >
        </v-field>
            
                            <v-error-message
    
    name="email"
    v-slot="{ message }"
>
    <p
        class="text-red-500 text-xs italic"
        v-text="message"
    >
    </p>
</v-error-message>
    
                            <button
                                type="submit"
                                class="absolute top-1.5 flex w-max items-center rounded-xl bg-white px-7 py-2.5 font-medium hover:bg-zinc-100 max-md:top-1 max-md:px-5 max-md:text-xs max-sm:mt-0 max-sm:rounded-lg max-sm:px-4 max-sm:py-2 ltr:right-2 rtl:left-2"
                            >
                                Subscribe                            </button>
                        </div>
    </v-form>
                </div>
            </div>
        
        
    </div>

    <div class="flex justify-between bg-[#F1EADF] px-[60px] py-3.5 max-md:justify-center max-sm:px-5">
        

        <p class="text-sm text-zinc-600 max-md:text-center">
            © Copyright 2010 - 2025, Webkul Software (Registered in India). All rights reserved.        </p>

        
    </div>
</footer>


                    </div>

        <style>
    .path-hint {
        border: solid 1px transparent;
        padding: 1px;
    }

    .path-hint:hover {
        border: 1px solid red;
    }

    .path-hint-tooltip {
        padding: 0px 10px;
        position: absolute;
        background: #000000;
        z-index: 10000;
        color: #fff;
        font-size: 10px;
    }

    .path-hint-tooltip h4 {
        margin-top: 5px;
        margin-bottom: 3px;
        color: #fff;
        font-size: 12px;
    }

    .path-hint-tooltip ul li {
        margin-bottom: 3px;
    }

    .main-container-wrapper .product-card .product-image img {
        max-width: 100%;
        height: 260px;
        object-fit: cover;
    }
</style>


            <script
        type="text/x-template"
        id="v-drawer-template"
    >
        <div>
            <!-- Toggler -->
            <div @click="open">
                <slot name="toggle"></slot>
            </div>

            <!-- Overlay -->
            <transition
                tag="div"
                name="drawer-overlay"
                enter-class="duration-300 ease-out"
                enter-from-class="opacity-0"
                enter-to-class="opacity-100"
                leave-class="duration-200 ease-in"
                leave-from-class="opacity-100"
                leave-to-class="opacity-0"
            >
                <div
                    class="fixed inset-0 z-20 bg-gray-500 bg-opacity-50 transition-opacity"
                    v-show="isOpen"
                ></div>
            </transition>

            <!-- Content -->
            <transition
                tag="div"
                name="drawer"
                :enter-from-class="enterFromLeaveToClasses"
                enter-active-class="transform transition duration-200 ease-in-out"
                enter-to-class="translate-x-0"
                leave-from-class="translate-x-0"
                leave-active-class="transform transition duration-200 ease-in-out"
                :leave-to-class="enterFromLeaveToClasses"
            >
                <div
                    class="fixed z-[1000] overflow-hidden bg-white max-md:!w-full"
                    :class="{
                        'inset-x-0 top-0': position == 'top',
                        'inset-x-0 bottom-0 max-sm:max-h-full': position == 'bottom',
                        'inset-y-0 ltr:right-0 rtl:left-0': position == 'right',
                        'inset-y-0 ltr:left-0 rtl:right-0': position == 'left'
                    }"
                    :style="'width:' + width"
                    v-show="isOpen"
                >
                    <div class="pointer-events-auto h-full w-full overflow-auto bg-white">
                        <div class="flex h-full w-full flex-col">
                            <div class="min-h-0 min-w-0 flex-1 overflow-auto">
                                <div class="flex h-full flex-col">
                                    <slot
                                        name="header"
                                        :close="close"
                                    >
                                        Default Header
                                    </slot>

                                    <!-- Content Slot -->
                                    <slot name="content"></slot>

                                    <!-- Footer Slot -->
                                    <slot name="footer"></slot>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </transition>
        </div>
    </script>

    <script type="module">
        app.component('v-drawer', {
            template: '#v-drawer-template',

            props: [
                'isActive',
                'position',
                'width'
            ],

            data() {
                return {
                    isOpen: this.isActive,
                };
            },

            watch: {
                isActive: function(newVal, oldVal) {
                    this.isOpen = newVal;
                }
            },

            computed: {
                enterFromLeaveToClasses() {
                    if (this.position == 'top') {
                        return '-translate-y-full';
                    } else if (this.position == 'bottom') {
                        return 'translate-y-full';
                    } else if (this.position == 'left') {
                        return 'ltr:-translate-x-full rtl:translate-x-full';
                    } else if (this.position == 'right') {
                        return 'ltr:translate-x-full rtl:-translate-x-full';
                    }
                }
            },

            methods: {
                toggle() {
                    this.isOpen = ! this.isOpen;

                    if (this.isOpen) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow ='auto';
                    }

                    document.body.style.paddingRight = '';

                    this.$emit('toggle', { isActive: this.isOpen });
                },

                open() {
                    this.isOpen = true;

                    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

                    document.body.style.overflow = 'hidden';

                    document.body.style.paddingRight = `${scrollbarWidth}px`;

                    this.$emit('open', { isActive: this.isOpen });
                },

                close() {
                    this.isOpen = false;

                    document.body.style.overflow = 'auto';

                    document.body.style.paddingRight = '';

                    this.$emit('close', { isActive: this.isOpen });
                }
            },
        });
    </script>
    <script
        type="text/x-template"
        id='v-toolbar-template'
    >
        <div>
            <!-- Desktop Toolbar -->
            <div class="flex justify-between max-md:hidden">
                

                <!-- Product Sorting Filters -->
                <v-dropdown position="bottom-left" class="relative z-[1]">
            <!-- Dropdown Toggler -->
                        <button class="flex w-full max-w-[200px] cursor-pointer items-center justify-between gap-4 rounded-lg border border-zinc-200 bg-white p-3.5 text-base transition-all hover:border-gray-400 focus:border-gray-400 max-md:w-[110px] max-md:border-0 max-md:pl-2.5 max-md:pr-2.5">
                            {{ sortLabel ?? "Sort By" }}

                            <span
                                class="icon-arrow-down text-2xl"
                                role="presentation"
                            ></span>
                        </button>

        <template v-slot:toggle>
            <!-- Dropdown Toggler -->
                        <button class="flex w-full max-w-[200px] cursor-pointer items-center justify-between gap-4 rounded-lg border border-zinc-200 bg-white p-3.5 text-base transition-all hover:border-gray-400 focus:border-gray-400 max-md:w-[110px] max-md:border-0 max-md:pl-2.5 max-md:pr-2.5">
                            {{ sortLabel ?? "Sort By" }}

                            <span
                                class="icon-arrow-down text-2xl"
                                role="presentation"
                            ></span>
                        </button>
        </template>
    
    
            <template v-slot:menu>
            <ul class="py-4">
                <li class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100 max-sm:text-sm" v-for="(sort, key) in filters.available.sort" :class="{'bg-gray-100': sort.value == filters.applied.sort}" @click="apply('sort', sort.value)">
    {{ sort.title }}
</li>
            </ul>
        </template>
    </v-dropdown>


                

                

                <!-- Product Pagination Limit -->
                <div class="flex items-center gap-10">
                    <!-- Product Pagination Limit -->
                    <v-dropdown position="bottom-right" class="relative">
            <!-- Dropdown Toggler -->
                            <button class="flex w-full max-w-[200px] cursor-pointer items-center justify-between gap-4 rounded-lg border border-zinc-200 bg-white p-3.5 text-base transition-all hover:border-gray-400 focus:border-gray-400 max-md:w-[110px] max-md:border-0 max-md:pl-2.5 max-md:pr-2.5">
                                {{ filters.applied.limit ?? "Show" }}

                                <span
                                    class="icon-arrow-down text-2xl"
                                    role="presentation"
                                ></span>
                            </button>

        <template v-slot:toggle>
            <!-- Dropdown Toggler -->
                            <button class="flex w-full max-w-[200px] cursor-pointer items-center justify-between gap-4 rounded-lg border border-zinc-200 bg-white p-3.5 text-base transition-all hover:border-gray-400 focus:border-gray-400 max-md:w-[110px] max-md:border-0 max-md:pl-2.5 max-md:pr-2.5">
                                {{ filters.applied.limit ?? "Show" }}

                                <span
                                    class="icon-arrow-down text-2xl"
                                    role="presentation"
                                ></span>
                            </button>
        </template>
    
    
            <template v-slot:menu>
            <ul class="py-4">
                <li class="cursor-pointer px-5 py-2 text-base hover:bg-gray-100 max-sm:text-sm" v-for="(limit, key) in filters.available.limit" :class="{'bg-gray-100': limit == filters.applied.limit}" @click="apply('limit', limit)">
    {{ limit }}
</li>
            </ul>
        </template>
    </v-dropdown>


                    <!-- Listing Mode Switcher -->
                    <div class="flex items-center gap-5">
                        <span
                            class="cursor-pointer text-2xl"
                            role="button"
                            aria-label="List"
                            tabindex="0"
                            :class="(filters.applied.mode === 'list') ? 'icon-listing-fill' : 'icon-listing'"
                            @click="changeMode('list')"
                        >
                        </span>

                        <span
                            class="cursor-pointer text-2xl"
                            role="button"
                            aria-label="Grid"
                            tabindex="0"
                            :class="(filters.applied.mode === 'grid') ? 'icon-grid-view-fill' : 'icon-grid-view'"
                            @click="changeMode('grid')"
                        >
                        </span>
                    </div>
                </div>

                
            </div>

            <!-- Mobile Toolbar -->
            <div class="md:hidden">
                <ul>
                    <li
                        class="px-4 py-2.5"
                        :class="{'bg-gray-100': sort.value == filters.applied.sort}"
                        v-for="(sort, key) in filters.available.sort"
                        @click="apply('sort', sort.value)"
                    >
                        {{ sort.title }}
                    </li>
                </ul>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-toolbar', {
            template: '#v-toolbar-template',

            data() {
                return {
                    filters: {
                        available: {
                            sort: [{"title":"From A-Z","value":"name-asc","sort":"name","order":"asc","position":1},{"title":"From Z-A","value":"name-desc","sort":"name","order":"desc","position":2},{"title":"Newest First","value":"created_at-desc","sort":"created_at","order":"desc","position":3},{"title":"Oldest First","value":"created_at-asc","sort":"created_at","order":"asc","position":4},{"title":"Cheapest First","value":"price-asc","sort":"price","order":"asc","position":5},{"title":"Expensive First","value":"price-desc","sort":"price","order":"desc","position":6}],

                            limit: [12,24,36,48],

                            mode: ["grid","list"],
                        },

                        default: {
                            sort: 'price-desc',

                            limit: '12',

                            mode: 'grid',
                        },

                        applied: {
                            sort: 'price-desc',

                            limit: '12',

                            mode: 'grid',
                        }
                    }
                };
            },

            created() {
                let queryParams = new URLSearchParams(window.location.search);

                queryParams.forEach((value, filter) => {
                    if (['sort', 'limit', 'mode'].includes(filter)) {
                        this.filters.applied[filter] = value;
                    }
                });
            },

            mounted() {
                this.setFilters();
            },

            computed: {
                sortLabel() {
                    return this.filters.available.sort.find(sort => sort.value === this.filters.applied.sort).title;
                }
            },

            methods: {
                apply(type, value) {
                    this.filters.applied[type] = value;

                    this.setFilters();
                },

                changeMode(value = 'grid') {
                    this.filters.applied['mode'] = value;

                    this.setFilters();
                },

                setFilters() {
                    let filters = {};

                    for (let key in this.filters.applied) {
                        if (this.filters.applied[key] != this.filters.default[key]) {
                            filters[key] = this.filters.applied[key];
                        }
                    }

                    this.$emit('filter-applied', {
                        default: this.filters.default,
                        applied: filters,
                    });
                }
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-accordion-template"
    >
        <div>
            <slot
                name="header"
                :toggle="toggle"
                :isOpen="isOpen"
            >
                admin::app.components.accordion.default-content            </slot>

            <slot
                name="content"
                :isOpen="isOpen"
            >
                admin::app.components.accordion.default-content            </slot>
        </div>
    </script>

    <script type="module">
        app.component('v-accordion', {
            template: '#v-accordion-template',

            props: [
                'isActive',
            ],

            data() {
                return {
                    isOpen: this.isActive,
                };
            },

            methods: {
                toggle() {
                    this.isOpen = ! this.isOpen;

                    this.$emit('toggle', { isActive: this.isOpen });
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-range-slider-template"
    >
        <div>
            <div class="flex items-center gap-4">
                <p class="text-base max-sm:text-sm">
                    Range:                </p>

                <p class="text-base font-semibold max-sm:text-sm">
                    {{ rangeText }}
                </p>
            </div>

            <div class="relative mx-auto flex h-20 w-full items-center justify-center p-2">
                <div class="relative h-1 w-full rounded-2xl bg-gray-200">
                    <div
                        ref="progress"
                        class="absolute left-1/4 right-0 h-full rounded-xl bg-navyBlue"
                    >
                    </div>

                    <span>
                        <input
                            :step="allowedMaxRange - Math.floor(allowedMaxRange) > 0 ? 0.01 : 1"
                            ref="minRange"
                            type="range"
                            :value="minRange"
                            class="pointer-events-none absolute h-1 w-full cursor-pointer appearance-none bg-transparent outline-none [&::-moz-range-thumb]:pointer-events-auto [&::-moz-range-thumb]:h-[18px] [&::-moz-range-thumb]:w-[18px] [&::-moz-range-thumb]:appearance-none [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:bg-white [&::-moz-range-thumb]:ring [&::-moz-range-thumb]:ring-navyBlue [&::-ms-thumb]:pointer-events-auto [&::-ms-thumb]:h-[18px] [&::-ms-thumb]:w-[18px] [&::-ms-thumb]:appearance-none [&::-ms-thumb]:rounded-full [&::-ms-thumb]:bg-white [&::-ms-thumb]:ring [&::-ms-thumb]:ring-navyBlue [&::-webkit-slider-thumb]:pointer-events-auto [&::-webkit-slider-thumb]:h-[18px] [&::-webkit-slider-thumb]:w-[18px] [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:ring [&::-webkit-slider-thumb]:ring-navyBlue"
                            :min="allowedMinRange"
                            :max="allowedMaxRange"
                            aria-label="Min Range"
                            @input="handle('min')"
                            @change="change"
                        >
                    </span>

                    <span>
                        <input
                            :step="allowedMaxRange - Math.floor(allowedMaxRange) > 0 ? 0.01 : 1"
                            ref="maxRange"
                            type="range"
                            :value="maxRange"
                            class="pointer-events-none absolute h-1 w-full cursor-pointer appearance-none bg-transparent outline-none [&::-moz-range-thumb]:pointer-events-auto [&::-moz-range-thumb]:h-[18px] [&::-moz-range-thumb]:w-[18px] [&::-moz-range-thumb]:appearance-none [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:bg-white [&::-moz-range-thumb]:ring [&::-moz-range-thumb]:ring-navyBlue [&::-ms-thumb]:pointer-events-auto [&::-ms-thumb]:h-[18px] [&::-ms-thumb]:w-[18px] [&::-ms-thumb]:appearance-none [&::-ms-thumb]:rounded-full [&::-ms-thumb]:bg-white [&::-ms-thumb]:ring [&::-ms-thumb]:ring-navyBlue [&::-webkit-slider-thumb]:pointer-events-auto [&::-webkit-slider-thumb]:h-[18px] [&::-webkit-slider-thumb]:w-[18px] [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:ring [&::-webkit-slider-thumb]:ring-navyBlue"
                            :min="allowedMinRange"
                            :max="allowedMaxRange"
                            aria-label="Max Range"
                            @input="handle('max')"
                            @change="change"
                        >
                    </span>
                </div>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-range-slider', {
            template: '#v-range-slider-template',

            props: [
                'defaultType',
                'defaultAllowedMinRange',
                'defaultAllowedMaxRange',
                'defaultMinRange',
                'defaultMaxRange',
            ],

            data() {
                return {
                    gap: this.defaultAllowedMaxRange * 0.10,

                    supportedTypes: ['integer', 'float', 'price'],

                    allowedMinRange: parseFloat(this.defaultAllowedMinRange ?? 0),

                    allowedMaxRange: parseFloat(this.defaultAllowedMaxRange ?? 100),

                    minRange: parseFloat(this.defaultMinRange ?? 0),

                    maxRange: parseFloat(this.defaultMaxRange ?? 100),
                };
            },

            computed: {
                rangeText() {
                    let { formattedMinRange, formattedMaxRange } = this.getFormattedData();

                    return `${formattedMinRange} - ${formattedMaxRange}`;
                },
            },

            mounted() {
                this.handleProgressBar();
            },

            methods: {
                getData() {
                    return {
                        allowedMinRange: this.allowedMinRange,
                        allowedMaxRange: this.allowedMaxRange,
                        minRange: this.minRange,
                        maxRange: this.maxRange,
                    };
                },

                getFormattedData() {
                    /**
                     * If someone is passing invalid props, this case will check first if they are valid, then continue.
                     */
                     if (this.isTypeSupported()) {
                        switch (this.defaultType) {
                            case 'price':
                                return {
                                    formattedAllowedMinRange: this.$shop.formatPrice(this.allowedMinRange),
                                    formattedAllowedMaxRange: this.$shop.formatPrice(this.allowedMaxRange),
                                    formattedMinRange: this.$shop.formatPrice(this.minRange),
                                    formattedMaxRange: this.$shop.formatPrice(this.maxRange),
                                };

                            case 'float':
                                return {
                                    formattedAllowedMinRange: parseFloat(this.allowedMinRange).toFixed(2),
                                    formattedAllowedMaxRange: parseFloat(this.allowedMaxRange).toFixed(2),
                                    formattedMinRange: parseFloat(this.minRange).toFixed(2),
                                    formattedMaxRange: parseFloat(this.maxRange).toFixed(2),
                                };

                            default:
                                return {
                                    formattedAllowedMinRange: this.allowedMinRange,
                                    formattedAllowedMaxRange: this.allowedMaxRange,
                                    formattedMinRange: this.minRange,
                                    formattedMaxRange: this.maxRange,
                                };
                        }
                    }

                    /**
                     * Otherwise, we will load the default formatting.
                     */
                    return {
                        formattedAllowedMinRange: this.allowedMinRange,
                        formattedAllowedMaxRange: this.allowedMaxRange,
                        formattedMinRange: this.minRange,
                        formattedMaxRange: this.maxRange,
                    };
                },

                handle(rangeType) {
                    this.minRange = parseFloat(this.$refs.minRange.value);

                    this.maxRange = parseFloat(this.$refs.maxRange.value);

                    if (this.maxRange - this.minRange < this.gap) {
                        if (rangeType === 'min') {
                            this.minRange = this.maxRange - this.gap;
                        } else {
                            this.maxRange = this.minRange + this.gap;
                        }
                    } else {
                        this.handleProgressBar();
                    }
                },

                handleProgressBar() {
                    const direction = document.dir == 'ltr' ? 'left' : 'right';

                    this.$refs.progress.style[direction] = (this.minRange / this.allowedMaxRange) * 100 + '%';

                    this.$refs.progress.style[direction == 'left' ? 'right' : 'left'] = 100 - (this.maxRange / this.allowedMaxRange) * 100 + '%';
                },

                change() {
                    this.$emit('change-range', {
                        ...this.getData(),
                        ...this.getFormattedData(),
                    });
                },

                isTypeSupported() {
                    return this.supportedTypes.includes(this.defaultType);
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-shimmer-image-template"
    >
        <div
            :id="'image-shimmer-' + $.uid"
            class="shimmer"
            v-bind="$attrs"
            v-show="isLoading"
        >
        </div>
        
        <img
            v-bind="$attrs"
            :data-src="src"
            :id="'image-' + $.uid"
            @load="onLoad"
            v-show="! isLoading"
            v-if="lazy"
        >

        <img
            v-bind="$attrs"
            :data-src="src"
            :id="'image-' + $.uid"
            @load="onLoad"
            v-else
            v-show="! isLoading"
        >
    </script>

    <script type="module">
        app.component('v-shimmer-image', {
            template: '#v-shimmer-image-template',

            props: {
                lazy: {
                    type: Boolean, 
                    default: true,
                },

                src: {
                    type: String, 
                    default: '',
                },
            },

            data() {
                return {
                    isLoading: true,
                };
            },

            mounted() {
                let self = this;

                if (! this.lazy) {
                    return;
                }
                
                let lazyImageObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            let lazyImage = document.getElementById('image-' + self.$.uid);

                            lazyImage.src = lazyImage.dataset.src;

                            lazyImageObserver.unobserve(lazyImage);
                        }
                    });
                });

                lazyImageObserver.observe(document.getElementById('image-shimmer-' + this.$.uid));
            },
            
            methods: {
                onLoad() {
                    this.isLoading = false;
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-product-ratings-template"
    >
        <div>
            <span class="text-sm font-medium text-black max-sm:text-xs">
                {{ average }}
            </span>
        
            <span
                class="icon-star-fill -mt-1 text-xl text-amber-500 max-sm:-mt-1 max-sm:text-lg"
                role="presentation"
            >
            </span>
            
            <span class="border-l border-zinc-300 text-sm font-medium text-black max-sm:border-zinc-300 max-sm:text-xs ltr:pl-1 rtl:pr-1">
                {{ abbreviatedTotal }}

                <span v-if="rating">Ratings</span>
            </span>
        </div>
    </script>

    <script type="module">
        app.component("v-product-ratings", {
            template: "#v-product-ratings-template",

            props: {
                average: {
                    type: String,
                    required: true,
                },

                total: {
                    type: String,
                    required: true,
                },

                rating: {
                    type: Boolean,
                    required: false,
                },
            },

            computed: {
                starColorClasses() {
                    return {
                        'text-emerald-600': this.average > 4,
                        'text-emerald-500': this.average >= 4 && this.average < 5,
                        'text-emerald-400': this.average >= 3 && this.average < 4,
                        'text-amber-500': this.average >= 2 && this.average < 3,
                        'text-red-500': this.average >= 1 && this.average < 2,
                        'text-gray-400': this.average <= 0,
                    };
                },

                abbreviatedTotal() {
                    if (this.total >= 1000) {
                        return `${(this.total / 1000).toFixed(1)}k`;
                    }

                    return this.total;
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-button-template"
    >
        <button
            v-if="! loading"
            :class="[buttonClass, '']"
        >
            {{ title }}
        </button>

        <button
            v-else
            :class="[buttonClass, '']"
        >
            <!-- Spinner -->
            <svg
                class="text-blue absolute h-5 w-5 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none" 
                aria-hidden="true"
                viewBox="0 0 24 24"
            >
                <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                >
                </circle>

                <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                >
                </path>
            </svg>

            <span class="relative h-full w-full opacity-0">
                {{ title }}
            </span>
        </button>
    </script>

    <script type="module">
        app.component('v-button', {
            template: '#v-button-template',

            props: {
                loading: Boolean,
                buttonType: String,
                title: String,
                buttonClass: String,
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-flash-group-template"
    >
        <transition-group
            tag='div'
            name="flash-group"
            enter-from-class="ltr:translate-x-full rtl:-translate-x-full"
            enter-active-class="transform transition duration-200 ease-in-out"
            enter-to-class="ltr:translate-x-0 rtl:-translate-x-0"
            leave-from-class="ltr:translate-x-0 rtl:-translate-x-0"
            leave-active-class="transform transition duration-200 ease-in-out"
            leave-to-class="ltr:translate-x-full rtl:-translate-x-full"
            class='fixed top-5 z-[1001] grid justify-items-end gap-2.5 max-sm:hidden ltr:right-5 rtl:left-5'
        >
            <v-flash-item
    v-for='flash in flashes'
    :key='flash.uid'
    :flash="flash"
    @onRemove="remove($event)"
/>

        </transition-group>

        <transition-group
            tag='div'
            name="flash-group"
            enter-from-class="ltr:translate-y-full rtl:-translate-y-full"
            enter-active-class="transform transition duration-200 ease-in-out"
            enter-to-class="ltr:translate-y-0 rtl:-translate-y-0"
            leave-from-class="ltr:translate-y-0 rtl:-translate-y-0"
            leave-active-class="transform transition duration-200 ease-in-out"
            leave-to-class="ltr:translate-y-full rtl:-translate-y-full"
            class='fixed bottom-10 left-1/2 z-[1001] grid -translate-x-1/2 -translate-y-1/2 transform justify-items-center gap-2.5 sm:hidden'
        >
            <v-flash-item
    v-for='flash in flashes'
    :key='flash.uid'
    :flash="flash"
    @onRemove="remove($event)"
/>

        </transition-group>
    </script>

    <script type="module">
        app.component('v-flash-group', {
            template: '#v-flash-group-template',

            data() {
                return {
                    uid: 0,

                    flashes: []
                }
            },

            created() {
                                                                                                                                                                
                this.registerGlobalEvents();
            },

            methods: {
                add(flash) {
                    flash.uid = this.uid++;

                    this.flashes.push(flash);
                },

                remove(flash) {
                    let index = this.flashes.indexOf(flash);

                    this.flashes.splice(index, 1);
                },

                registerGlobalEvents() {
                    this.$emitter.on('add-flash', this.add);
                },
            }
        });
    </script>
    <script
        type="text/x-template"
        id="v-modal-confirm-template"
    >
        <div>
            <transition
                tag="div"
                name="modal-overlay"
                enter-class="duration-300 ease-out"
                enter-from-class="opacity-0"
                enter-to-class="opacity-100"
                leave-class="duration-200 ease-in"
                leave-from-class="opacity-100"
                leave-to-class="opacity-0"
            >
                <div
                    class="fixed inset-0 z-20 bg-gray-500 bg-opacity-50 transition-opacity"
                    v-show="isOpen"
                ></div>
            </transition>

            <transition
                tag="div"
                name="modal-content"
                enter-class="duration-300 ease-out"
                enter-from-class="translate-y-4 opacity-0 md:translate-y-0 md:scale-95"
                enter-to-class="translate-y-0 opacity-100 md:scale-100"
                leave-class="duration-200 ease-in"
                leave-from-class="translate-y-0 opacity-100 md:scale-100"
                leave-to-class="translate-y-4 opacity-0 md:translate-y-0 md:scale-95"
            >
                <div
                    class="fixed inset-0 z-20 transform overflow-y-auto transition" v-show="isOpen"
                >
                    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                        <div class="absolute left-1/2 top-1/2 z-[999] w-full max-w-[475px] -translate-x-1/2 -translate-y-1/2 overflow-hidden rounded-xl bg-white p-5 max-md:w-[90%] max-sm:p-4">
                            <div class="flex gap-2.5">
                                <div>
                                    <span class="flex rounded-full border border-gray-300 p-2.5">
                                        <i class="icon-error text-3xl max-sm:text-xl"></i>
                                    </span>
                                </div>

                                <div>
                                    <div class="flex items-center justify-between gap-5 text-xl max-sm:text-lg">
                                        {{ title }}
                                    </div>

                                    <div class="pb-5 pt-1.5 text-left text-sm text-gray-500">
                                        {{ message }}
                                    </div>

                                    <div class="flex justify-end gap-2.5">
                                        <button
                                            type="button"
                                            class="secondary-button max-md:py-3 max-sm:px-6 max-sm:py-2.5"
                                            @click="disagree"
                                        >
                                            {{ options.btnDisagree }}
                                        </button>

                                        <button
                                            type="button"
                                            class="primary-button max-md:py-3 max-sm:px-6 max-sm:py-2.5"
                                            @click="agree"
                                        >
                                            {{ options.btnAgree }} 
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </transition>
        </div>
    </script>

    <script type="module">
        app.component('v-modal-confirm', {
            template: '#v-modal-confirm-template',

            data() {
                return {
                    isOpen: false,

                    title: '',

                    message: '',

                    options: {
                        btnDisagree: '',
                        btnAgree: '',
                    },

                    agreeCallback: null,

                    disagreeCallback: null,
                };
            },

            created() {
                this.registerGlobalEvents();
            },

            methods: {
                open({
                    title = "Are you sure?",
                    message = "Are you sure you want to perform this action?",
                    options = {
                        btnDisagree: "Disagree",
                        btnAgree: "Agree",
                    },
                    agree = () => {},
                    disagree = () => {},
                }) {
                    this.isOpen = true;

                    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

                    document.body.style.overflow = 'hidden';

                    document.body.style.paddingRight = `${scrollbarWidth}px`;

                    this.title = title;

                    this.message = message;

                    this.options = options;

                    this.agreeCallback = agree;

                    this.disagreeCallback = disagree;
                },

                disagree() {
                    this.isOpen = false;

                    document.body.style.overflow = 'auto';

                    document.body.style.paddingRight = '';

                    this.disagreeCallback();
                },

                agree() {
                    this.isOpen = false;

                    document.body.style.overflow = 'auto';

                    document.body.style.paddingRight = '';

                    this.agreeCallback();
                },

                registerGlobalEvents() {
                    this.$emitter.on('open-confirm-modal', this.open);
                },
            }
        });
    </script>
    <script
        type="text/x-template"
        id="v-dropdown-template"
    >
        <div>
            <div
                class="select-none"
                ref="toggleBlock"
                @click="toggle()"
            >
                <slot name="toggle">Toggle</slot>
            </div>

            <transition
                tag="div"
                name="dropdown"
                enter-active-class="transition duration-100 ease-out"
                enter-from-class="scale-95 transform opacity-0"
                enter-to-class="scale-100 transform opacity-100"
                leave-active-class="transition duration-75 ease-in"
                leave-from-class="scale-100 transform opacity-100"
                leave-to-class="scale-95 transform opacity-0"
            >
                <div
                    class="absolute z-20 w-max rounded-[20px] bg-white shadow-[0px_10px_84px_rgba(0,0,0,0.1)] max-md:rounded-lg"
                    :style="positionStyles"
                    v-show="isActive"
                >
                    <slot name="content"></slot>

                    <slot name="menu"></slot>
                </div>
            </transition>
        </div>
    </script>

    <script type="module">
        app.component('v-dropdown', {
            template: '#v-dropdown-template',

            props: {
                position: String,

                closeOnClick: {
                    type: Boolean,
                    required: false,
                    default: true
                },
            },

            data() {
                return {
                    toggleBlockWidth: 0,

                    toggleBlockHeight: 0,

                    isActive: false,
                };
            },

            created() {
                window.addEventListener('click', this.handleFocusOut);
            },

            mounted() {
                this.toggleBlockWidth = this.$refs.toggleBlock.clientWidth;

                this.toggleBlockHeight = this.$refs.toggleBlock.clientHeight;
            },

            beforeDestroy() {
                window.removeEventListener('click', this.handleFocusOut);
            },

            computed: {
                positionStyles() {
                    switch (this.position) {
                        case 'bottom-left':
                            return [
                                `min-width: ${this.toggleBlockWidth}px`,
                                `top: ${this.toggleBlockHeight}px`,
                                'left: 0',
                            ];

                        case 'bottom-right':
                            return [
                                `min-width: ${this.toggleBlockWidth}px`,
                                `top: ${this.toggleBlockHeight}px`,
                                'right: 0',
                            ];

                        case 'top-left':
                            return [
                                `min-width: ${this.toggleBlockWidth}px`,
                                `bottom: ${this.toggleBlockHeight}px`,
                                'left: 0',
                            ];

                        case 'top-right':
                            return [
                                `min-width: ${this.toggleBlockWidth}px`,
                                `bottom: ${this.toggleBlockHeight}px`,
                                'right: 0',
                            ];

                        default:
                            return [
                                `min-width: ${this.toggleBlockWidth}px`,
                                `top: ${this.toggleBlockHeight}px`,
                                'left: 0',
                            ];
                    }
                },
            },

            methods: {
                toggle() {
                    /**
                     * If still somehow width is zero then this will check for width one more time.
                     */
                    if (this.toggleBlockWidth === 0) {
                        this.toggleBlockWidth = this.$refs.toggleBlock.clientWidth;
                    }

                    /**
                     * If still somehow height is zero then this will check for height one more time.
                     */
                    if (this.toggleBlockHeight === 0) {
                        this.toggleBlockHeight = this.$refs.toggleBlock.clientHeight;
                    }

                    this.isActive = ! this.isActive;
                },

                handleFocusOut(e) {
                    if (! this.$el.contains(e.target) || (this.closeOnClick && this.$el.children[1].contains(e.target))) {
                        this.isActive = false;
                    }
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-flash-item-template"
    >
        <div
            class="flex w-max max-w-[408px] justify-between gap-12 rounded-lg px-5 py-3 max-sm:max-w-80 max-sm:items-center max-sm:gap-2 max-sm:p-3"
            :style="typeStyles[flash.type]['container']"
        >
            <p
                class="flex items-center break-words text-sm"
                :style="typeStyles[flash.type]['message']"
            >
                <span
                    class="icon-toast-done text-2xl ltr:mr-2.5 rtl:ml-2.5"
                    :class="iconClasses[flash.type]"
                    :style="typeStyles[flash.type]['icon']"
                ></span>

                {{ flash.message }}
            </p>

			<span
                class="icon-cancel max-h-4 max-w-4 cursor-pointer"
                :style="typeStyles[flash.type]['icon']"
                @click="remove"
            ></span>
        </div>
    </script>

    <script type="module">
        app.component('v-flash-item', {
            template: '#v-flash-item-template',

            props: ['flash'],

            data() {
                return {
                    iconClasses: {
                        success: 'icon-toast-done',

                        error: 'icon-toast-error',

                        warning: 'icon-toast-exclamation-mark',

                        info: 'icon-toast-info',
                    },

                    typeStyles: {
                        success: {
                            container: 'background: #D4EDDA',

                            message: 'color: #155721',

                            icon: 'color: #155721'
                        },

                        error: {
                            container: 'background: #F8D7DA',

                            message: 'color: #721C24',

                            icon: 'color: #721C24'
                        },

                        warning: {
                            container: 'background: #FFF3CD',

                            message: 'color: #856404',

                            icon: 'color: #856404'
                        },

                        info: {
                            container: 'background: #E2E3E5',

                            message: 'color: #383D41',

                            icon: 'color: #383D41'
                        },
                    },
                };
            },

            created() {
                var self = this;

                setTimeout(function() {
                    self.remove()
                }, 5000)
            },

            methods: {
                remove() {
                    this.$emit('onRemove', this.flash)
                }
            }
        });
    </script>
    <script type="text/x-template" id="v-mobile-drawer-template">
        <v-drawer
    @close="onDrawerClose"
    is-active=""
    position="left"
    width="100%"
>
            <template v-slot:toggle>
            <span class="icon-hamburger cursor-pointer text-2xl"></span>
        </template>
    
            <template v-slot:header="{ close }">
            <div class="grid gap-y-2.5 p-6 pb-5 max-md:gap-y-1.5 max-md:border-b max-md:border-zinc-200 max-md:p-4 max-md:gap-y-1 max-md:font-semibold">
                <div class="flex items-center justify-between">
                    <a href="http://localhost/onlinestore/bagisto-2.3/public">
                        <img
                            src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/logo-CZWQQgOF.svg"
                            alt="OnlineStore"
                            width="131"
                            height="29"
                        >
                    </a>
                </div>

                <div class="absolute top-5 max-sm:top-4 ltr:right-5 rtl:left-5">
                    <span
                        class="icon-cancel cursor-pointer text-3xl max-md:text-2xl"
                        @click="close"
                    >
                    </span>
                </div>
            </div>
        </template>
    
            <template v-slot:content>
            <div class="flex-1 overflow-auto px-6 max-md:px-4 !p-0">
                <!-- Account Profile Hero Section -->
                <div class="border-b border-zinc-200 p-4">
                    <div class="grid grid-cols-[auto_1fr] items-center gap-4 rounded-xl border border-zinc-200 p-2.5">
                        <div>
                            <img
                                src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/user-placeholder-C_FiyGd9.png"
                                class="h-[60px] w-[60px] rounded-full max-md:rounded-full"
                            >
                        </div>

                        
                                                    <div class="flex flex-col justify-between gap-2.5 max-md:gap-0">
                                <p class="font-mediums break-all text-2xl max-md:text-xl">Hello! Vignesh</p>

                                <p class="text-zinc-500 no-underline max-md:text-sm"><EMAIL></p>
                            </div>
                                            </div>
                </div>

                

                <!-- Mobile category view -->
                <v-mobile-category ref="mobileCategory"></v-mobile-category>
            </div>
        </template>
    
            <template v-slot:footer>
            <div class="pb-8 max-md:pb-2">
                <!-- Localization & Currency Section -->
            </div>
        </template>
    </v-drawer>

    </script>

    <script
        type="text/x-template"
        id="v-mobile-category-template"
    >
        <!-- Wrapper with transition effects -->
        <div class="relative h-full overflow-hidden">
            <!-- Sliding container -->
            <div
                class="flex h-full transition-transform duration-300"
                :class="{
                    'ltr:translate-x-0 rtl:translate-x-0': currentViewLevel !== 'third',
                    'ltr:-translate-x-full rtl:translate-x-full': currentViewLevel === 'third'
                }"
            >
                <!-- First level view -->
                <div class="h-full w-full flex-shrink-0 overflow-auto px-6">
                    <div class="py-4">
                        <div
                            v-for="category in categories"
                            :key="category.id"
                            :class="{'mb-2': category.children && category.children.length}"
                        >
                            <div class="flex cursor-pointer items-center justify-between py-2 transition-colors duration-200">
                                <a :href="category.url" class="text-base font-medium text-black">
                                    {{ category.name }}
                                </a>
                            </div>

                            <!-- Second Level Categories -->
                            <div v-if="category.children && category.children.length" >
                                <div
                                    v-for="secondLevelCategory in category.children"
                                    :key="secondLevelCategory.id"
                                >
                                    <div
                                        class="flex cursor-pointer items-center justify-between py-2 transition-colors duration-200"
                                        @click="showThirdLevel(secondLevelCategory, category, $event)"
                                    >
                                        <a :href="secondLevelCategory.url" class="text-sm font-normal">
                                            {{ secondLevelCategory.name }}
                                        </a>

                                        <span
                                            v-if="secondLevelCategory.children && secondLevelCategory.children.length"
                                            class="icon-arrow-right rtl:icon-arrow-left"
                                        ></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Third level view -->
                <div
                    class="h-full w-full flex-shrink-0"
                    v-if="currentViewLevel === 'third'"
                >
                    <div class="border-b border-gray-200 px-6 py-4">
                        <button
                            @click="goBackToMainView"
                            class="flex items-center justify-center gap-2 focus:outline-none"
                            aria-label="Go back"
                        >
                            <span class="icon-arrow-left rtl:icon-arrow-right text-lg"></span>
                            <div class="text-base font-medium text-black">
                                Back to Main Menu                            </div>
                        </button>
                    </div>

                    <!-- Third Level Content -->
                    <div class="px-6 py-4">
                        <div
                            v-for="thirdLevelCategory in currentSecondLevelCategory?.children"
                            :key="thirdLevelCategory.id"
                            class="mb-2"
                        >
                            <a
                                :href="thirdLevelCategory.url"
                                class="block py-2 text-sm transition-colors duration-200"
                            >
                                {{ thirdLevelCategory.name }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-mobile-category', {
            template: '#v-mobile-category-template',

            data() {
                return  {
                    categories: [],
                    currentViewLevel: 'main',
                    currentSecondLevelCategory: null,
                    currentParentCategory: null
                }
            },

            mounted() {
                this.getCategories();
            },

            computed: {
                getCurrentScreenHeight() {
                    return window.innerHeight - (window.innerWidth < 920 ? 61 : 0) + 'px';
                },
            },

            methods: {
                getCategories() {
                    this.$axios.get("http://localhost/onlinestore/bagisto-2.3/public/api/categories/tree")
                        .then(response => {
                            this.categories = response.data.data;
                        })
                        .catch(error => {
                            console.log(error);
                        });
                },

                showThirdLevel(secondLevelCategory, parentCategory, event) {
                    if (secondLevelCategory.children && secondLevelCategory.children.length) {
                        this.currentSecondLevelCategory = secondLevelCategory;
                        this.currentParentCategory = parentCategory;
                        this.currentViewLevel = 'third';

                        if (event) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                    }
                },

                goBackToMainView() {
                    this.currentViewLevel = 'main';
                }
            },
        });

        app.component('v-mobile-drawer', {
            template: '#v-mobile-drawer-template',

            methods: {
                onDrawerClose() {
                    this.$refs.mobileCategory.currentViewLevel = 'main';
                }
            },
        });
    </script>
    <!-- Filters Vue template -->
    <script
        type="text/x-template"
        id="v-filters-template"
    >
        <!-- Filter Shimmer Effect -->
        <template v-if="isLoading">
            <div class="panel-side journal-scroll grid max-h-[1320px] min-w-[342px] max-w-[400px] grid-cols-[1fr] overflow-y-auto overflow-x-hidden max-xl:min-w-[270px] ltr:pr-7 rtl:pl-7">
    <div class="flex h-[50px] items-center justify-between border-b border-zinc-200 py-2.5 max-md:hidden">
        <p class="shimmer h-6 w-[30%]"></p>
        <p class="shimmer h-5 w-1/5"></p>
    </div>

    <!-- Price Range Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-7 w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="z-10 rounded-lg bg-white">
            <div>
    <div class="mt-1 flex items-center gap-4">
        <p class="shimmer h-5 w-12"></p>

        <p class="shimmer h-5 w-28"></p>
    </div>

    <!-- Price range slider effect -->
    <div class="relative mx-auto flex h-20 w-full items-center justify-center p-2">
        <div class="shimmer relative h-1 w-full rounded-2xl bg-gray-200">
            <div class="shimmer absolute -left-1 -top-2.5 h-6 w-6 rounded-full"></div>
            <div class="shimmer absolute -right-1 -top-2.5 h-6 w-6 rounded-full"></div>
        </div>
    </div>
</div>
        </div>
    </div>

    <!-- Checkbox Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-[27px] w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="flex flex-col items-center justify-between gap-2">
            <p class="shimmer h-[52px] w-full rounded-xl"></p>

            <div class="shimmer h-5 w-[50%] self-end rounded"></div>
        </div>

        <div class="z-10 grid gap-1 rounded-lg bg-white pb-3">
            <div class="flex items-center gap-x-4 ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Checkbox Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-[27px] w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="flex flex-col items-center justify-between gap-2">
            <p class="shimmer h-[52px] w-full rounded-xl"></p>

            <div class="shimmer h-5 w-[50%] self-end rounded"></div>
        </div>

        <div class="z-10 grid gap-1 rounded-lg bg-white pb-3">
            <div class="flex items-center gap-x-4 ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>
        </div>
    </div>
</div>
        </template>

        <!-- Filters Container -->
        <template v-else>
            <div class="panel-side journal-scroll grid max-h-[1320px] min-w-[342px] grid-cols-[1fr] overflow-y-auto overflow-x-hidden max-xl:min-w-[270px] md:max-w-[342px] md:ltr:pr-7 md:rtl:pl-7">
                <!-- Filters Header Container -->
                <div class="flex h-[50px] items-center justify-between border-b border-zinc-200 pb-2.5 max-md:hidden">
                    <p class="text-lg font-semibold max-sm:font-medium">
                        Filters:                    </p>

                    <p
                        class="cursor-pointer text-xs font-medium"
                        tabindex="0"
                        @click="clear()"
                    >
                        Clear All                    </p>
                </div>

                <!-- Filters Items Vue Component -->
                <v-filter-item
                    ref="filterItemComponent"
                    :key="filterIndex"
                    :filter="filter"
                    v-for='(filter, filterIndex) in filters.available'
                    @values-applied="applyFilter(filter, $event)"
                >
                </v-filter-item>
            </div>
        </template>
    </script>

    <!-- Filter Item Vue template -->
    <script
        type="text/x-template"
        id="v-filter-item-template"
    >
        <div class="border-b border-zinc-200 last:border-b-0">
    <v-accordion
        
        is-active="1"
    >
                    <template v-slot:header="{ toggle, isOpen }">
                <div
                    class="flex cursor-pointer select-none items-center justify-between p-4 px-0 py-2.5 max-sm:!pb-1.5"
                    role="button"
                    tabindex="0"
                    @click="toggle"
                >
                    <div class="flex items-center justify-between">
                    <p class="text-lg font-semibold max-sm:text-base max-sm:font-medium">
                        {{ filter.name }}
                    </p>
                </div>

                    <span
                        v-bind:class="isOpen ? 'icon-arrow-up text-2xl' : 'icon-arrow-down text-2xl'"
                        role="button"
                        aria-label="Toggle accordion"
                        tabindex="0"
                    ></span>
                </div>
            </template>
        
                    <template v-slot:content="{ isOpen }">
                <div
                    class="z-10 rounded-lg bg-white p-1.5 !p-0"
                    v-show="isOpen"
                >
                    <!-- Price Range Filter -->
                <ul v-if="filter.type === 'price'">
                    <li>
                        <v-price-filter
                            :key="refreshKey"
                            :default-price-range="appliedValues"
                            @set-price-range="applyValue($event)"
                        >
                        </v-price-filter>
                    </li>
                </ul>

                <!-- Checkbox Filter Options -->
                <template v-else>
                    <!-- Search Box For Options -->
                    <div
                        class="flex flex-col gap-1"
                        v-if="filter.type !== 'boolean'"
                    >
                        <div class="relative">
                            <div class="icon-search pointer-events-none absolute top-3 flex items-center text-2xl max-md:text-xl max-sm:top-2.5 ltr:left-3 rtl:right-3"></div>

                            <input
                                type="text"
                                class="block w-full rounded-xl border border-zinc-200 px-11 py-3.5 text-sm font-medium text-gray-900 max-md:rounded-lg max-md:px-10 max-md:py-3 max-md:font-normal max-sm:text-xs"
                                placeholder="Search"
                                v-model="searchQuery"
                                v-debounce:500="searchOptions"
                            />
                        </div>

                        <p
                            class="mt-1 flex flex-row-reverse text-xs text-gray-600"
                            v-text="
                                'Showing currentCount of totalCount options'
                                    .replace('currentCount', options.length)
                                    .replace('totalCount', meta.total)
                            "
                            v-if="meta && meta.total > 0"
                        >
                        </p>
                    </div>

                    <!-- Filter Options -->
                    <ul class="pb-3 text-base text-gray-700">
                        <template v-if="options.length">
                            <li
                                :key="`${filter.id}_${option.id}`"
                                v-for="(option, optionIndex) in options"
                            >
                                <div class="flex select-none items-center gap-x-4 rounded hover:bg-gray-100 max-sm:gap-x-1 max-sm:!p-0 ltr:pl-2 rtl:pr-2">
                                    <input
                                        type="checkbox"
                                        :id="`filter_${filter.id}_option_ ${option.id}`"
                                        class="peer hidden"
                                        :value="option.id"
                                        v-model="appliedValues"
                                        @change="applyValue"
                                    />

                                    <label
                                        class="icon-uncheck peer-checked:icon-check-box cursor-pointer text-2xl text-navyBlue peer-checked:text-navyBlue max-sm:text-xl"
                                        role="checkbox"
                                        aria-checked="false"
                                        :aria-label="option.name"
                                        :aria-labelledby="'label_option_' + option.id"
                                        tabindex="0"
                                        :for="`filter_${filter.id}_option_ ${option.id}`"
                                    >
                                    </label>

                                    <label
                                        class="w-full cursor-pointer p-2 text-base text-gray-900 max-sm:p-1 max-sm:text-sm ltr:pl-0 rtl:pr-0"
                                        :id="'label_option_' + option.id"
                                        :for="`filter_${filter.id}_option_ ${option.id}`"
                                        role="button"
                                        tabindex="0"
                                    >
                                        {{ option.name }}
                                    </label>
                                </div>
                            </li>
                        </template>

                        <template v-else>
                            <li
                                class="flex flex-col items-center justify-center gap-2 py-2"
                                v-if="! isLoadingMore"
                            >
                                No options available.                            </li>

                            <div
                                class="mt-2"
                                v-else
                            >
                                <div class="flex flex-col items-center justify-between">
                                    <div class="shimmer h-5 w-[50%] self-end rounded"></div>
                                </div>

                                <div class="z-10 grid gap-1 rounded-lg bg-white">
                                    <div class="flex items-center gap-x-4 ltr:pl-2 rtl:pr-2">
                                        <div class="shimmer h-5 w-5 rounded"></div>

                                        <div class="p-2 ltr:pl-0 rtl:pr-0">
                                            <div class="shimmer h-5 w-[100px]"></div>
                                        </div>
                                    </div>

                                    <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                                        <div class="shimmer h-5 w-5 rounded"></div>

                                        <div class="p-2 ltr:pl-0 rtl:pr-0">
                                            <div class="shimmer h-5 w-[100px]"></div>
                                        </div>
                                    </div>

                                    <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                                        <div class="shimmer h-5 w-5 rounded"></div>

                                        <div class="p-2 ltr:pl-0 rtl:pr-0">
                                            <div class="shimmer h-5 w-[100px]"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </ul>

                    <!-- Load More Button -->
                    <div class="flex justify-center pb-3" v-if="meta && meta.current_page < meta.last_page">
                        <button
                            type="button"
                            class="rounded border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                            @click="loadMoreOptions"
                            :disabled="isLoadingMore"
                        >
                            <span v-if="isLoadingMore">
                                Loading...                            </span>

                            <span v-else>
                                Load More                            </span>
                        </button>
                    </div>
                </template>
                </div>
            </template>
            </v-accordion>
</div>

    </script>

    <script
        type="text/x-template"
        id="v-price-filter-template"
    >
        <div>
            <!-- Price Range Filter Shimmer -->
            <template v-if="isLoading">
                <div>
    <div class="mt-1 flex items-center gap-4">
        <p class="shimmer h-5 w-12"></p>

        <p class="shimmer h-5 w-28"></p>
    </div>

    <!-- Price range slider effect -->
    <div class="relative mx-auto flex h-20 w-full items-center justify-center p-2">
        <div class="shimmer relative h-1 w-full rounded-2xl bg-gray-200">
            <div class="shimmer absolute -left-1 -top-2.5 h-6 w-6 rounded-full"></div>
            <div class="shimmer absolute -right-1 -top-2.5 h-6 w-6 rounded-full"></div>
        </div>
    </div>
</div>
            </template>

            <template v-else>
                <v-range-slider :key="refreshKey" default-type="price" :default-allowed-max-range="allowedMaxPrice" :default-min-range="minRange" :default-max-range="maxRange" @change-range="setPriceRange($event)"></v-range-slider>

            </template>
        </div>
    </script>

    <script type='module'>
        app.component('v-filters', {
            template: '#v-filters-template',

            data() {
                return {
                    isLoading: true,

                    filters: {
                        available: {},

                        applied: {},
                    },
                };
            },

            mounted() {
                this.getFilters();

                this.setFilters();
            },

            methods: {
                getFilters() {
                    this.$axios.get('http://localhost/onlinestore/bagisto-2.3/public/api/categories/attributes', {
                            params: {
                                category_id: "2",
                            }
                        })
                        .then((response) => {
                            this.isLoading = false;

                            this.filters.available = response.data.data;
                        })
                        .catch((error) => {
                            console.log(error);
                        });
                },

                setFilters() {
                    let queryParams = new URLSearchParams(window.location.search);

                    queryParams.forEach((value, filter) => {
                        /**
                         * Removed all toolbar filters in order to prevent key duplication.
                         */
                        if (! ['sort', 'limit', 'mode'].includes(filter)) {
                            this.filters.applied[filter] = value.split(',');
                        }
                    });

                    this.$emit('filter-applied', this.filters.applied);
                },

                applyFilter(filter, values) {
                    if (values.length) {
                        this.filters.applied[filter.code] = values;
                    } else {
                        delete this.filters.applied[filter.code];
                    }

                    this.$emit('filter-applied', this.filters.applied);
                },

                clear() {
                    /**
                     * Clearing parent component.
                     */
                    this.filters.applied = {};

                    /**
                     * Clearing child components. Improvisation needed here.
                     */
                    this.$refs.filterItemComponent.forEach((filterItem) => {
                        if (filterItem.filter.code === 'price') {
                            filterItem.$data.appliedValues = null;
                        } else {
                            filterItem.$data.appliedValues = [];
                        }
                    });

                    this.$emit('filter-applied', this.filters.applied);
                },
            },
        });

        app.component('v-filter-item', {
            template: '#v-filter-item-template',

            props: ['filter'],

            data() {
                return {
                    options: [],

                    meta: null,

                    appliedValues: null,

                    currentPage: 1,

                    searchQuery: '',

                    isLoadingMore: true,

                    refreshKey: 0,
                }
            },

            watch: {
                appliedValues() {
                    if (this.filter.code === 'price' && ! this.appliedValues) {
                        ++this.refreshKey;
                    }
                },
            },

            mounted() {
                this.fetchFilterOptions();

                if (this.filter.code === 'price') {
                    /**
                     * Improvisation needed here for `this.$parent.$data`.
                     */
                    this.appliedValues = this.$parent.$data.filters.applied[this.filter.code]?.join(',');

                    ++this.refreshKey;

                    return;
                }

                /**
                 * Improvisation needed here for `this.$parent.$data`.
                 */
                this.appliedValues = this.$parent.$data.filters.applied[this.filter.code] ?? [];
            },

            methods: {
                applyValue($event) {
                    if (this.filter.code === 'price') {
                        this.appliedValues = $event;

                        this.$emit('values-applied', this.appliedValues);

                        return;
                    }

                    this.$emit('values-applied', this.appliedValues);
                },

                /**
                 * Search options based on query
                 */
                searchOptions() {
                    this.currentPage = 1;

                    this.fetchFilterOptions(true);
                },

                /**
                 * Load more options when "Load more" button is clicked
                 */
                loadMoreOptions() {
                    this.currentPage++;

                    this.fetchFilterOptions(false);
                },

                fetchFilterOptions(replace = true) {
                    this.isLoadingMore = true;

                    const url = `http://localhost/onlinestore/bagisto-2.3/public/api/categories/attributes/attribute_id/options`.replace('attribute_id', this.filter.id);

                    this.$axios.get(url, {
                        params: {
                            page: this.currentPage,
                            search: this.searchQuery,
                        }
                    })
                    .then(response => {
                        this.isLoadingMore = false;

                        this.options = replace
                            ? response.data.data
                            : [...this.options, ...response.data.data];

                        this.meta = response.data.meta;
                    })
                    .catch(error => {
                        this.isLoadingMore = false;
                    });
                },
            },
        });

        app.component('v-price-filter', {
            template: '#v-price-filter-template',

            props: ['defaultPriceRange'],

            data() {
                return {
                    refreshKey: 0,

                    isLoading: true,

                    allowedMaxPrice: 100,

                    priceRange: this.defaultPriceRange ?? [0, 100].join(','),
                };
            },

            computed: {
                minRange() {
                    let priceRange = this.priceRange.split(',');

                    return priceRange[0];
                },

                maxRange() {
                    let priceRange = this.priceRange.split(',');

                    return priceRange[1];
                }
            },

            mounted() {
                this.getMaxPrice();
            },

            methods: {
                getMaxPrice() {
                    this.$axios.get('http://localhost/onlinestore/bagisto-2.3/public/api/categories/max-price/2')
                        .then((response) => {
                            this.isLoading = false;

                            /**
                             * If data is zero, then default price will be displayed.
                             */
                            if (response.data.data.max_price) {
                                this.allowedMaxPrice = response.data.data.max_price;
                            }

                            if (! this.defaultPriceRange) {
                                this.priceRange = [0, this.allowedMaxPrice].join(',');
                            }

                            ++this.refreshKey;
                        })
                        .catch((error) => {
                            console.log(error);
                        });
                },

                setPriceRange($event) {
                    this.priceRange = [$event.minRange, $event.maxRange].join(',');

                    this.$emit('set-price-range', this.priceRange);
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-product-card-template"
    >
        <!-- Grid Card -->
        <div
            class="1180:transtion-all group w-full rounded-md 1180:relative 1180:grid 1180:content-start 1180:overflow-hidden 1180:duration-300 1180:hover:shadow-[0_5px_10px_rgba(0,0,0,0.1)]"
            v-if="mode != 'list'"
        >
            <div class="relative max-h-[300px] max-w-[291px] overflow-hidden max-md:max-h-60 max-md:max-w-full max-md:rounded-lg max-sm:max-h-[200px] max-sm:max-w-full">
                

                <!-- Product Image -->
                <a
                    :href="`http://localhost/onlinestore/bagisto-2.3/public/${product.url_key}`"
                    :aria-label="product.name + ' '"
                >
                    <v-shimmer-image class="after:content-[' '] relative bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105" :src="product.base_image.medium_image_url" :key="product.id" :index="product.id" width="291" height="300" :alt="product.name">
    <div class="shimmer after:content-[' '] relative bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105" :src="product.base_image.medium_image_url" :key="product.id" :index="product.id" width="291" height="300" :alt="product.name"></div>
</v-shimmer-image>

                </a>

                
                
                <!-- Product Ratings -->
                

                                    <v-product-ratings
    class="flex w-max items-center rounded-md border border-zinc-200 px-4 py-2 absolute bottom-1.5 items-center !border-white bg-white/80 !px-2 !py-1 text-xs max-sm:!px-1.5 max-sm:!py-0.5 ltr:left-1.5 rtl:right-1.5" :average="product.ratings.average" :total="product.reviews.total" :rating="false" v-if="product.reviews.total"
    average="0"
    total="0"
>
</v-product-ratings>

                
                

                <div class="action-items bg-black">
                    <!-- Product Sale Badge -->
                    <p
                        class="absolute top-1.5 inline-block rounded-[44px] bg-red-600 px-2.5 text-sm text-white max-sm:rounded-l-none max-sm:rounded-r-xl max-sm:px-2 max-sm:py-0.5 max-sm:text-xs ltr:left-1.5 max-sm:ltr:left-0 rtl:right-5 max-sm:rtl:right-0"
                        v-if="product.on_sale"
                    >
                        Sale                    </p>

                    <!-- Product New Badge -->
                    <p
                        class="absolute top-1.5 inline-block rounded-[44px] bg-navyBlue px-2.5 text-sm text-white max-sm:rounded-l-none max-sm:rounded-r-xl max-sm:px-2 max-sm:py-0.5 max-sm:text-xs ltr:left-1.5 max-sm:ltr:left-0 rtl:right-1.5 max-sm:rtl:right-0"
                        v-else-if="product.is_new"
                    >
                        New                    </p>

                    <div class="opacity-0 transition-all duration-300 group-hover:bottom-0 group-hover:opacity-100 max-lg:opacity-100 max-sm:opacity-100">

                        

                                                    <span
                                class="absolute top-2.5 flex h-6 w-6 items-center justify-center rounded-full border border-zinc-200 bg-white text-lg md:hidden ltr:right-1.5 rtl:left-1.5"
                                role="button"
                                aria-label="Add To Wishlist"
                                tabindex="0"
                                :class="product.is_wishlist ? 'icon-heart-fill text-red-500' : 'icon-heart'"
                                @click="addToWishlist()"
                            >
                            </span>
                        
                        

                        

                                                    <span
                                class="icon-compare absolute top-10 flex h-6 w-6 items-center justify-center rounded-full border border-zinc-200 bg-white text-lg sm:hidden ltr:right-1.5 rtl:left-1.5"
                                role="button"
                                aria-label="Add To Compare"
                                tabindex="0"
                                @click="addToCompare(product.id)"
                            >
                            </span>
                        
                        

                    </div>
                </div>
            </div>

            <!-- Product Information Section -->
            <div class="-mt-9 grid max-w-[291px] translate-y-9 content-start gap-2.5 bg-white p-2.5 transition-transform duration-300 ease-out group-hover:-translate-y-0 group-hover:rounded-t-lg max-md:relative max-md:mt-0 max-md:translate-y-0 max-md:gap-0 max-md:px-0 max-md:py-1.5 max-sm:min-w-[170px] max-sm:max-w-[192px]">

                

                <p class="break-all text-base font-medium max-md:mb-1.5 max-md:max-w-56 max-md:whitespace-break-spaces max-md:leading-6 max-sm:max-w-[192px] max-sm:text-sm max-sm:leading-4">
                    {{ product.name }}
                </p>

                

                <!-- Pricing -->
                

                <div
                    class="flex items-center gap-2.5 text-lg font-semibold max-sm:text-sm max-sm:leading-6"
                    v-html="product.price_html"
                >
                </div>

                

                <!-- Product Actions Section -->
                <div class="action-items flex items-center justify-between opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 max-md:hidden">
                                            

                        <button
                            class="secondary-button w-full max-w-full p-2.5 text-sm font-medium max-sm:rounded-xl max-sm:p-2"
                            :disabled="! product.is_saleable || isAddingToCart"
                            @click="addToCart()"
                        >
                            Add To Cart                        </button>

                        
                    
                    

                                            <span
                            class="cursor-pointer p-2.5 text-2xl max-sm:hidden"
                            role="button"
                            aria-label="Add To Wishlist"
                            tabindex="0"
                            :class="product.is_wishlist ? 'icon-heart-fill text-red-600' : 'icon-heart'"
                            @click="addToWishlist()"
                        >
                        </span>
                    
                    

                    

                                            <span
                            class="icon-compare cursor-pointer p-2.5 text-2xl max-sm:hidden"
                            role="button"
                            aria-label="Add To Compare"
                            tabindex="0"
                            @click="addToCompare(product.id)"
                        >
                        </span>
                    
                    
                </div>
            </div>
        </div>

        <!-- List Card -->
        <div
            class="relative flex max-w-max grid-cols-2 gap-4 overflow-hidden rounded max-sm:flex-wrap"
            v-else
        >
            <div class="group relative max-h-[258px] max-w-[250px] overflow-hidden"> 

                

                <a :href="`http://localhost/onlinestore/bagisto-2.3/public/${product.url_key}`">
                    <v-shimmer-image class="after:content-[' '] relative min-w-[250px] bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105" :src="product.base_image.medium_image_url" :key="product.id" :index="product.id" width="291" height="300" :alt="product.name">
    <div class="shimmer after:content-[' '] relative min-w-[250px] bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105" :src="product.base_image.medium_image_url" :key="product.id" :index="product.id" width="291" height="300" :alt="product.name"></div>
</v-shimmer-image>

                </a>

                

                <div class="action-items bg-black">
                    <p
                        class="absolute top-5 inline-block rounded-[44px] bg-red-500 px-2.5 text-sm text-white ltr:left-5 max-sm:ltr:left-2 rtl:right-5"
                        v-if="product.on_sale"
                    >
                        Sale                    </p>

                    <p
                        class="absolute top-5 inline-block rounded-[44px] bg-navyBlue px-2.5 text-sm text-white ltr:left-5 max-sm:ltr:left-2 rtl:right-5"
                        v-else-if="product.is_new"
                    >
                        New                    </p>

                    <div class="opacity-0 transition-all duration-300 group-hover:bottom-0 group-hover:opacity-100 max-sm:opacity-100">

                        

                                                    <span 
                                class="absolute top-5 flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-md bg-white text-2xl ltr:right-5 rtl:left-5"
                                role="button"
                                aria-label="Add To Wishlist"
                                tabindex="0"
                                :class="product.is_wishlist ? 'icon-heart-fill text-red-600' : 'icon-heart'"
                                @click="addToWishlist()"
                            >
                            </span>
                        
                        

                        

                                                    <span
                                class="icon-compare absolute top-16 flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-md bg-white text-2xl ltr:right-5 rtl:left-5"
                                role="button"
                                aria-label="Add To Compare"
                                tabindex="0"
                                @click="addToCompare(product.id)"
                            >
                            </span>
                        
                        
                    </div>
                </div>
            </div>

            <div class="grid content-start gap-4">

                

                <p class="text-base">
                    {{ product.name }}
                </p>

                

                

                <div
                    class="flex gap-2.5 text-lg font-semibold"
                    v-html="product.price_html"
                >
                </div>

                

                <!-- Needs to implement that in future -->
                <div class="flex hidden gap-4">
                    <span class="block h-[30px] w-[30px] rounded-full bg-[#B5DCB4]">
                    </span>

                    <span class="block h-[30px] w-[30px] rounded-full bg-zinc-500">
                    </span>
                </div>

                

                <p class="text-sm text-zinc-500">
                    <template  v-if="! product.ratings.total">
                        <p class="text-sm text-zinc-500">
                            Be the first to review this product                        </p>
                    </template>

                    <template v-else>
                                                    <v-product-ratings
    class="flex w-max items-center rounded-md border border-zinc-200 px-4 py-2" :average="product.ratings.average" :total="product.reviews.total" :rating="false"
    average="0"
    total="0"
>
</v-product-ratings>

                                            </template>
                </p>

                

                
                    

                    <v-button class="primary-button whitespace-nowrap px-8 py-2.5" title="Add To Cart" :loading="isAddingToCart" :disabled="! product.is_saleable || isAddingToCart" @click="addToCart()"></v-button>


                    

                            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-product-card', {
            template: '#v-product-card-template',

            props: ['mode', 'product'],

            data() {
                return {
                    isCustomer: '1',

                    isAddingToCart: false,
                }
            },

            methods: {
                addToWishlist() {
                    if (this.isCustomer) {
                        this.$axios.post(`http://localhost/onlinestore/bagisto-2.3/public/api/customer/wishlist`, {
                                product_id: this.product.id
                            })
                            .then(response => {
                                this.product.is_wishlist = ! this.product.is_wishlist;

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                            })
                            .catch(error => {});
                        } else {
                            window.location.href = "http://localhost/onlinestore/bagisto-2.3/public/customer/login";
                        }
                },

                addToCompare(productId) {
                    /**
                     * This will handle for customers.
                     */
                    if (this.isCustomer) {
                        this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/api/compare-items', {
                                'product_id': productId
                            })
                            .then(response => {
                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                            })
                            .catch(error => {
                                if ([400, 422].includes(error.response.status)) {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: error.response.data.data.message });

                                    return;
                                }

                                this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message});
                            });

                        return;
                    }

                    /**
                     * This will handle for guests.
                     */
                    let items = this.getStorageValue() ?? [];

                    if (items.length) {
                        if (! items.includes(productId)) {
                            items.push(productId);

                            localStorage.setItem('compare_items', JSON.stringify(items));

                            this.$emitter.emit('add-flash', { type: 'success', message: "Item added successfully to compare list." });
                        } else {
                            this.$emitter.emit('add-flash', { type: 'warning', message: "Item is already added to compare list." });
                        }
                    } else {
                        localStorage.setItem('compare_items', JSON.stringify([productId]));

                        this.$emitter.emit('add-flash', { type: 'success', message: "Item added successfully to compare list." });

                    }
                },

                getStorageValue(key) {
                    let value = localStorage.getItem('compare_items');

                    if (! value) {
                        return [];
                    }

                    return JSON.parse(value);
                },

                addToCart() {
                    this.isAddingToCart = true;

                    this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/api/checkout/cart', {
                            'quantity': 1,
                            'product_id': this.product.id,
                        })
                        .then(response => {
                            if (response.data.message) {
                                this.$emitter.emit('update-mini-cart', response.data.data );

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });
                            } else {
                                this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                            }

                            this.isAddingToCart = false;
                        })
                        .catch(error => {
                            this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });

                            if (error.response.data.redirect_uri) {
                                window.location.href = error.response.data.redirect_uri;
                            }
                            
                            this.isAddingToCart = false;
                        });
                },
            },
        });
    </script>
        <script
            type="text/x-template"
            id="v-category-template"
        >
            <div class="container px-[60px] max-lg:px-8 max-md:px-4">
                <div class="flex items-start gap-10 max-lg:gap-5 md:mt-10">
                    <!-- Product Listing Filters -->
                    <!-- Desktop Filters Navigation -->
<div v-if="! isMobile">
    <!-- Filters Vue Component -->
    <v-filters
        @filter-applied="setFilters('filter', $event)"
        @filter-clear="clearFilters('filter', $event)"
    >
        <!-- Category Filter Shimmer Effect -->
        <div class="panel-side journal-scroll grid max-h-[1320px] min-w-[342px] max-w-[400px] grid-cols-[1fr] overflow-y-auto overflow-x-hidden max-xl:min-w-[270px] ltr:pr-7 rtl:pl-7">
    <div class="flex h-[50px] items-center justify-between border-b border-zinc-200 py-2.5 max-md:hidden">
        <p class="shimmer h-6 w-[30%]"></p>
        <p class="shimmer h-5 w-1/5"></p>
    </div>

    <!-- Price Range Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-7 w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="z-10 rounded-lg bg-white">
            <div>
    <div class="mt-1 flex items-center gap-4">
        <p class="shimmer h-5 w-12"></p>

        <p class="shimmer h-5 w-28"></p>
    </div>

    <!-- Price range slider effect -->
    <div class="relative mx-auto flex h-20 w-full items-center justify-center p-2">
        <div class="shimmer relative h-1 w-full rounded-2xl bg-gray-200">
            <div class="shimmer absolute -left-1 -top-2.5 h-6 w-6 rounded-full"></div>
            <div class="shimmer absolute -right-1 -top-2.5 h-6 w-6 rounded-full"></div>
        </div>
    </div>
</div>
        </div>
    </div>

    <!-- Checkbox Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-[27px] w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="flex flex-col items-center justify-between gap-2">
            <p class="shimmer h-[52px] w-full rounded-xl"></p>

            <div class="shimmer h-5 w-[50%] self-end rounded"></div>
        </div>

        <div class="z-10 grid gap-1 rounded-lg bg-white pb-3">
            <div class="flex items-center gap-x-4 ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Checkbox Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-[27px] w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="flex flex-col items-center justify-between gap-2">
            <p class="shimmer h-[52px] w-full rounded-xl"></p>

            <div class="shimmer h-5 w-[50%] self-end rounded"></div>
        </div>

        <div class="z-10 grid gap-1 rounded-lg bg-white pb-3">
            <div class="flex items-center gap-x-4 ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>
        </div>
    </div>
</div>
    </v-filters>
</div>

<!-- Mobile Filters Navigation -->
<div
    class="fixed bottom-0 z-10 grid w-full max-w-full grid-cols-[1fr_auto_1fr] items-center justify-items-center border-t border-zinc-200 bg-white px-5 ltr:left-0 rtl:right-0"
    v-if="isMobile"
>
    <!-- Filter Drawer -->
    <v-drawer
    :is-active="isDrawerActive.filter"
    is-active=""
    position="left"
    width="100%"
>
            <template v-slot:toggle>
            <div
                class="flex cursor-pointer items-center gap-x-2.5 px-2.5 py-3.5 text-base font-medium uppercase max-md:py-3"
                @click="isDrawerActive.filter = true"
            >
                <span class="icon-filter-1 text-2xl"></span>

                Filter            </div>
        </template>
    
            <template v-slot:header="{ close }">
            <div class="grid gap-y-2.5 p-6 pb-5 max-md:gap-y-1.5 max-md:border-b max-md:border-zinc-200 max-md:p-4 max-md:gap-y-1 max-md:font-semibold">
                <div class="flex items-center justify-between">
                <p class="text-lg font-semibold">
                    Filters:                </p>

                <p
                    class="cursor-pointer text-sm font-medium ltr:mr-[50px] rtl:ml-[50px]"
                    @click="clearFilters('filter', '')"
                >
                    Clear All                </p>
            </div>

                <div class="absolute top-5 max-sm:top-4 ltr:right-5 rtl:left-5">
                    <span
                        class="icon-cancel cursor-pointer text-3xl max-md:text-2xl"
                        @click="close"
                    >
                    </span>
                </div>
            </div>
        </template>
    
            <template v-slot:content>
            <div class="flex-1 overflow-auto px-6 max-md:px-4">
                <!-- Filters Vue Component -->
            <v-filters
                @filter-applied="setFilters('filter', $event)"
                @filter-clear="clearFilters('filter', $event)"
            >
                <!-- Category Filter Shimmer Effect -->
                <div class="panel-side journal-scroll grid max-h-[1320px] min-w-[342px] max-w-[400px] grid-cols-[1fr] overflow-y-auto overflow-x-hidden max-xl:min-w-[270px] ltr:pr-7 rtl:pl-7">
    <div class="flex h-[50px] items-center justify-between border-b border-zinc-200 py-2.5 max-md:hidden">
        <p class="shimmer h-6 w-[30%]"></p>
        <p class="shimmer h-5 w-1/5"></p>
    </div>

    <!-- Price Range Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-7 w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="z-10 rounded-lg bg-white">
            <div>
    <div class="mt-1 flex items-center gap-4">
        <p class="shimmer h-5 w-12"></p>

        <p class="shimmer h-5 w-28"></p>
    </div>

    <!-- Price range slider effect -->
    <div class="relative mx-auto flex h-20 w-full items-center justify-center p-2">
        <div class="shimmer relative h-1 w-full rounded-2xl bg-gray-200">
            <div class="shimmer absolute -left-1 -top-2.5 h-6 w-6 rounded-full"></div>
            <div class="shimmer absolute -right-1 -top-2.5 h-6 w-6 rounded-full"></div>
        </div>
    </div>
</div>
        </div>
    </div>

    <!-- Checkbox Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-[27px] w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="flex flex-col items-center justify-between gap-2">
            <p class="shimmer h-[52px] w-full rounded-xl"></p>

            <div class="shimmer h-5 w-[50%] self-end rounded"></div>
        </div>

        <div class="z-10 grid gap-1 rounded-lg bg-white pb-3">
            <div class="flex items-center gap-x-4 ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Checkbox Filter Shimmer -->
    <div class="border-b border-zinc-200">
        <div class="flex items-center justify-between py-2.5">
            <p class="shimmer h-[27px] w-2/5"></p>
            <span class="shimmer h-6 w-6"></span>
        </div>

        <div class="flex flex-col items-center justify-between gap-2">
            <p class="shimmer h-[52px] w-full rounded-xl"></p>

            <div class="shimmer h-5 w-[50%] self-end rounded"></div>
        </div>

        <div class="z-10 grid gap-1 rounded-lg bg-white pb-3">
            <div class="flex items-center gap-x-4 ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>

            <div class="flex items-center gap-x-4 rounded ltr:pl-2 rtl:pr-2">
                <div class="shimmer h-5 w-5 rounded"></div>

                <div class="p-2 ltr:pl-0 rtl:pr-0">
                    <div class="shimmer h-5 w-[100px]"></div>
                </div>
            </div>
        </div>
    </div>
</div>
            </v-filters>
            </div>
        </template>
    
    </v-drawer>


    <!-- Separator -->
    <span class="h-5 w-0.5 bg-zinc-200"></span>

    <!-- Sort Drawer -->
    <v-drawer
    :is-active="isDrawerActive.toolbar"
    is-active=""
    position="bottom"
    width="100%"
>
            <template v-slot:toggle>
            <div
                class="flex cursor-pointer items-center gap-x-2.5 px-2.5 py-3.5 text-base font-medium uppercase max-md:py-3"
                @click="isDrawerActive.toolbar = true"
            >
                <span class="icon-sort-1 text-2xl"></span>

                Sort            </div>
        </template>
    
            <template v-slot:header="{ close }">
            <div class="grid gap-y-2.5 p-6 pb-5 max-md:gap-y-1.5 max-md:border-b max-md:border-zinc-200 max-md:p-4 max-md:gap-y-1 max-md:font-semibold">
                <div class="flex items-center justify-between">
                <p class="text-lg font-semibold">
                    Sort                </p>
            </div>

                <div class="absolute top-5 max-sm:top-4 ltr:right-5 rtl:left-5">
                    <span
                        class="icon-cancel cursor-pointer text-3xl max-md:text-2xl"
                        @click="close"
                    >
                    </span>
                </div>
            </div>
        </template>
    
            <template v-slot:content>
            <div class="flex-1 overflow-auto px-6 max-md:px-4 !px-0">
                <v-toolbar @filter-applied='setFilters("toolbar", $event)'></v-toolbar>
            </div>
        </template>
    
    </v-drawer>

</div>




                    <!-- Product Listing Container -->
                    <div class="flex-1">
                        <!-- Desktop Product Listing Toolbar -->
                        <div class="max-md:hidden">
                            <v-toolbar @filter-applied='setFilters("toolbar", $event)'></v-toolbar>




                        </div>

                        <!-- Product List Card Container -->
                        <div
                            class="mt-8 grid grid-cols-1 gap-6"
                            v-if="(filters.toolbar.applied.mode ?? filters.toolbar.default.mode) === 'list'"
                        >
                            <!-- Product Card Shimmer Effect -->
                            <template v-if="isLoading">
                                <div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 gap-6">
    <div class="relative grid max-w-max grid-cols-2 gap-4 max-sm:grid-cols-1">
        <div class="shimmer relative min-h-[258px] min-w-[250px] overflow-hidden rounded"> 
            <img class="rounded-sm bg-zinc-100">
        </div>

        <div class="grid content-start gap-4">
            <p class="shimmer h-6 w-3/4"></p>

            <p class="shimmer h-6 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="flex hidden gap-4"> 
                <span class="shimmer block h-8 w-8 rounded-full"></span> 

                <span class="shimmer block h-8 w-8 rounded-full"></span> 
            </div>

            <p class="shimmer h-6 w-full"></p>

            <div class="shimmer h-12 w-[168px] rounded-xl"></div>
        </div>
    </div>
</div>
                            </template>

                            <!-- Product Card Listing -->
                            

                            <template v-else>
                                <template v-if="products.length">
                                    <v-product-card
    :mode="'list'" v-for="product in products"
    :product="product"
>
</v-product-card>

                                </template>

                                <!-- Empty Products Container -->
                                <template v-else>
                                    <div class="m-auto grid w-full place-content-center items-center justify-items-center py-32 text-center">
                                        <img
                                            class="max-md:h-[100px] max-md:w-[100px]"
                                            src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/thank-you-mhMpuEVL.png"
                                            alt="No products available in this category"
                                        />

                                        <p
                                            class="text-xl max-md:text-sm"
                                            role="heading"
                                        >
                                            No products available in this category                                        </p>
                                    </div>
                                </template>
                            </template>

                            
                        </div>

                        <!-- Product Grid Card Container -->
                        <div v-else class="mt-8 max-md:mt-5">
                            <!-- Product Card Shimmer Effect -->
                            <template v-if="isLoading">
                                <div class="grid grid-cols-3 gap-8 max-1060:grid-cols-2 max-md:justify-items-center max-md:gap-x-4">
                                    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
    <div class="grid gap-2.5 relative w-full max-w-[291px] max-sm:grid-cols-1 ">
        <div class="shimmer relative w-full rounded max-sm:!rounded-lg">
            <div class="after:content-[' '] relative after:block after:pb-[calc(100%+9px)]"></div>
        </div>

        <div class="grid content-start gap-2.5 max-sm:gap-1">
            <p class="shimmer h-4 w-3/4"></p>
            <p class="shimmer h-4 w-[55%]"></p>

            <!-- Needs to implement that in future -->
            <div class="mt-3 flex hidden gap-4">
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
                <span class="shimmer block h-[30px] w-[30px] rounded-full"></span>
            </div>
        </div>
    </div>
                                </div>
                            </template>

                            

                            <!-- Product Card Listing -->
                            <template v-else>
                                <template v-if="products.length">
                                    <div class="grid grid-cols-3 gap-8 max-1060:grid-cols-2 max-md:justify-items-center max-md:gap-x-4">
                                        <v-product-card
    :mode="'grid'" v-for="product in products"
    :product="product"
>
</v-product-card>

                                    </div>
                                </template>

                                <!-- Empty Products Container -->
                                <template v-else>
                                    <div class="m-auto grid w-full place-content-center items-center justify-items-center py-32 text-center">
                                        <img
                                            class="max-md:h-[100px] max-md:w-[100px]"
                                            src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/thank-you-mhMpuEVL.png"
                                            alt="No products available in this category"
                                        />

                                        <p
                                            class="text-xl max-md:text-sm"
                                            role="heading"
                                        >
                                            No products available in this category                                        </p>
                                    </div>
                                </template>
                            </template>

                            
                        </div>

                        

                        <!-- Load More Button -->
                        <button
                            class="secondary-button mx-auto mt-14 block w-max rounded-2xl px-11 py-3 text-center text-base max-md:rounded-lg max-sm:mt-6 max-sm:px-6 max-sm:py-1.5 max-sm:text-sm"
                            @click="loadMoreProducts"
                            v-if="links.next && ! loader"
                        >
                            Load More                        </button>

                        <button
                            v-else-if="links.next"
                            class="secondary-button mx-auto mt-14 block w-max rounded-2xl px-[74.5px] py-3.5 text-center text-base max-md:rounded-lg max-md:py-3 max-sm:mt-6 max-sm:px-[50.8px] max-sm:py-1.5"
                        >
                            <!-- Spinner -->
                            <img
                                class="h-5 w-5 animate-spin text-navyBlue"
                                src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/spinner-CWS6ej98.svg"
                                alt="Loading"
                            />
                        </button>

                        
                    </div>
                </div>
            </div>
        </script>

        <script type="module">
            app.component('v-category', {
                template: '#v-category-template',

                data() {
                    return {
                        isMobile: window.innerWidth <= 767,

                        isLoading: true,

                        isDrawerActive: {
                            toolbar: false,

                            filter: false,
                        },

                        filters: {
                            toolbar: {
                                default: {},

                                applied: {},
                            },

                            filter: {},
                        },

                        products: [],

                        links: {},

                        loader: false,
                    }
                },

                computed: {
                    queryParams() {
                        let queryParams = Object.assign({}, this.filters.filter, this.filters.toolbar.applied);

                        return this.removeJsonEmptyValues(queryParams);
                    },

                    queryString() {
                        return this.jsonToQueryString(this.queryParams);
                    },
                },

                watch: {
                    queryParams() {
                        this.getProducts();
                    },

                    queryString() {
                        window.history.pushState({}, '', '?' + this.queryString);
                    },
                },

                methods: {
                    setFilters(type, filters) {
                        this.filters[type] = filters;
                    },

                    clearFilters(type, filters) {
                        this.filters[type] = {};
                    },

                    getProducts() {
                        this.isDrawerActive = {
                            toolbar: false,

                            filter: false,
                        };

                        document.body.style.overflow ='scroll';

                        this.$axios.get("http://localhost/onlinestore/bagisto-2.3/public/api/products?category_id=2", {
                            params: this.queryParams
                        })
                            .then(response => {
                                this.isLoading = false;

                                this.products = response.data.data;

                                this.links = response.data.links;
                            }).catch(error => {
                                console.log(error);
                            });
                    },

                    loadMoreProducts() {
                        if (! this.links.next) {
                            return;
                        }

                        this.loader = true;

                        this.$axios.get(this.links.next)
                            .then(response => {
                                this.loader = false;

                                this.products = [...this.products, ...response.data.data];

                                this.links = response.data.links;
                            }).catch(error => {
                                console.log(error);
                            });
                    },

                    removeJsonEmptyValues(params) {
                        Object.keys(params).forEach(function (key) {
                            if ((! params[key] && params[key] !== undefined)) {
                                delete params[key];
                            }

                            if (Array.isArray(params[key])) {
                                params[key] = params[key].join(',');
                            }
                        });

                        return params;
                    },

                    jsonToQueryString(params) {
                        let parameters = new URLSearchParams();

                        for (const key in params) {
                            parameters.append(key, params[key]);
                        }

                        return parameters.toString();
                    }
                },
            });
        </script>
        <script
        type="text/x-template"
        id="v-image-search-template"
    >
        <div>
            <label
                class="icon-camera absolute top-3 flex items-center text-xl max-sm:top-2.5 ltr:right-3 ltr:pr-3 max-md:ltr:right-1.5 rtl:left-3 rtl:pl-3 max-md:rtl:left-1.5"
                aria-label="Search"
                :for="'v-image-search-' + $.uid"
                v-if="! isSearching"
            >
            </label>

            <label
                class="absolute top-2.5 flex cursor-pointer items-center text-xl ltr:right-3 ltr:pr-3 max-md:ltr:pr-1 rtl:left-3 rtl:pl-3 max-md:rtl:pl-1"
                v-else
            >
                <!-- Spinner -->
                <svg
                    class="h-5 w-5 animate-spin text-black"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                >
                    <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                    >
                    </circle>

                    <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    >
                    </path>
                </svg>
            </label>

            <input
                type="file"
                class="hidden"
                ref="imageSearchInput"
                :id="'v-image-search-' + $.uid"
                @change="loadLibrary()"
            />

            <img
                id="uploaded-image-url"
                class="hidden"
                :src="uploadedImageUrl"
                alt="uploaded image url"
                width="20"
                height="20"
            />
        </div>
    </script>

    <script type="module">
        app.component('v-image-search', {
            template: '#v-image-search-template',

            data() {
                return {
                    uploadedImageUrl: '',

                    isSearching: false,
                };
            },

            methods: {
                /**
                 * This method will dynamically load the scripts. Because image search library
                 * only used when someone clicks or interact with the image button. This will
                 * reduce some data usage for mobile user.
                 * 
                 * @return {void}
                 */
                loadLibrary() {
                    // External TensorFlow libraries disabled for privacy
                    alert('Image search feature has been disabled for privacy reasons.');
                    return;

                    // this.$shop.loadDynamicScript(
                    //     'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest/dist/tf.min.js', () => {
                    //         this.$shop.loadDynamicScript(
                    //             'https://cdn.jsdelivr.net/npm/tensorflow-models-mobilenet-patch@2.1.1/dist/mobilenet.min.js', () => {
                    //                 this.analyzeImage();
                    //             }
                    //         );
                    //     }
                    // );
                },

                /**
                 * This method will analyze the image and load the sets on the bases of trained model.
                 * 
                 * @return {void}
                 */
                analyzeImage() {
                    this.isSearching = true;

                    let imageInput = this.$refs.imageSearchInput;

                    if (imageInput.files && imageInput.files[0]) {
                        if (imageInput.files[0].type.includes('image/')) {
                            if (imageInput.files[0].size <= 2000000) {
                                let formData = new FormData();

                                formData.append('image', imageInput.files[0]);

                                this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/search/upload', formData, {
                                        headers: {
                                            'Content-Type': 'multipart/form-data'
                                        }
                                    })
                                    .then(response => {
                                        let net;

                                        let self = this;

                                        this.uploadedImageUrl = response.data;

                                        async function app() {
                                            let analysedResult = [];

                                            let queryString = '';

                                            net = await mobilenet.load();

                                            try {
                                                const result = await net.classify(document.getElementById(
                                                    'uploaded-image-url'));

                                                result.forEach(function(value) {
                                                    queryString = value.className.split(',');

                                                    if (queryString.length > 1) {
                                                        analysedResult = analysedResult.concat(
                                                            queryString);
                                                    } else {
                                                        analysedResult.push(queryString[0]);
                                                    }
                                                });
                                            } catch (error) {
                                                this.$emitter.emit('add-flash', {
                                                    type: 'error',
                                                    message: "Something went wrong, please try again later."
                                                });
                                            }

                                            localStorage.searchedImageUrl = self.uploadedImageUrl;

                                            queryString = localStorage.searchedTerms = analysedResult.join(
                                                '_');

                                            queryString = localStorage.searchedTerms.split('_').map(
                                            term => {
                                                return term.split(' ').join('+');
                                            });

                                            window.location.href =
                                                `${'http://localhost/onlinestore/bagisto-2.3/public/search'}?query=${queryString[0]}&image-search=1`;
                                        }

                                        app();
                                    })
                                    .catch((error) => {
                                        this.$emitter.emit('add-flash', {
                                            type: 'error',
                                            message: "Something went wrong, please try again later."
                                        });

                                        this.isSearching = false;
                                    });
                            } else {
                                imageInput.value = '';

                                this.$emitter.emit('add-flash', {
                                    type: 'error',
                                    message: 'Size Limit Error'
                                });

                                this.isSearching = false;
                            }
                        } else {
                            imageInput.value = '';

                            this.$emitter.emit('add-flash', {
                                type: 'error',
                                message: 'Only images (.jpeg, .jpg, .png, ..) are allowed.'
                            });

                            this.isSearching = false;
                        }
                    }
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-mini-cart-template"
    >
        

                    <v-drawer
    
    is-active=""
    position="right"
    width="500px"
>
            <template v-slot:toggle>
            <span class="relative">
                        <span
                            class="icon-cart cursor-pointer text-2xl"
                            role="button"
                            aria-label="Shopping Cart"
                            tabindex="0"
                        ></span>

                                                    <span
                                class="absolute -top-4 rounded-[44px] bg-navyBlue px-2 py-1.5 text-xs font-semibold leading-[9px] text-white ltr:left-5 rtl:right-5 max-md:px-2 max-md:py-1.5 max-md:ltr:left-4 max-md:rtl:right-4"
                                v-if="cart?.items_count"
                            >
                                {{ cart.items_count }}
                            </span>
                                            </span>
        </template>
    
            <template v-slot:header="{ close }">
            <div class="grid gap-y-2.5 p-6 pb-5 max-md:gap-y-1.5 max-md:border-b max-md:border-zinc-200 max-md:p-4 max-md:gap-y-1 max-md:font-semibold">
                <div class="flex items-center justify-between">
                        <p class="text-2xl font-medium max-md:text-xl max-sm:text-xl">
                            Shopping Cart                        </p>
                    </div>

                    <p class="text-base max-md:text-zinc-500 max-sm:text-xs">
                        Get Up To 30% OFF on your 1st order
                    </p>

                <div class="absolute top-5 max-sm:top-4 ltr:right-5 rtl:left-5">
                    <span
                        class="icon-cancel cursor-pointer text-3xl max-md:text-2xl"
                        @click="close"
                    >
                    </span>
                </div>
            </div>
        </template>
    
            <template v-slot:content>
            <div class="flex-1 overflow-auto px-6 max-md:px-4">
                <!-- Cart Item Listing -->
                    <div
                        class="mt-9 grid gap-12 max-md:mt-2.5 max-md:gap-5"
                        v-if="cart?.items?.length"
                    >
                        <div
                            class="flex gap-x-5 max-md:gap-x-4"
                            v-for="item in cart?.items"
                        >
                            <!-- Cart Item Image -->
                            

                            <div class="">
                                <a :href="`http://localhost/onlinestore/bagisto-2.3/public/${item.product_url_key}`">
                                    <img
                                        :src="item.base_image.small_image_url"
                                        class="max-w-28 max-h-28 rounded-xl max-md:max-h-20 max-md:max-w-[76px]"
                                    />
                                </a>
                            </div>

                            

                        <!-- Cart Item Information -->
                        <div class="grid flex-1 place-content-start justify-stretch gap-y-2.5">
                            <div class="flex justify-between gap-2 max-md:gap-0 max-sm:flex-wrap">

                                    

                                    <a
                                    class="max-w-4/5 max-md:w-full"
                                    :href="`http://localhost/onlinestore/bagisto-2.3/public/${item.product_url_key}`"
                                >
                                        <p class="text-base font-medium max-md:font-normal max-sm:text-sm">
                                            {{ item.name }}
                                        </p>
                                    </a>

                                    

                                    

                                    <template v-if="displayTax.prices == 'including_tax'">
                                        <p class="text-lg max-md:font-semibold max-sm:text-sm">
                                            {{ item.formatted_price_incl_tax }}
                                        </p>
                                    </template>

                                    <template v-else-if="displayTax.prices == 'both'">
                                        <p class="flex flex-col text-lg max-md:font-semibold max-sm:text-sm">
                                            {{ item.formatted_price_incl_tax }}

                                            <span class="text-xs font-normal text-zinc-500">
                                                Excl. Tax:
                                                <span class="font-medium text-black">{{ item.formatted_price }}</span>
                                            </span>
                                        </p>
                                    </template>

                                    <template v-else>
                                        <p class="text-lg max-md:font-semibold max-sm:text-sm">
                                            {{ item.formatted_price }}
                                        </p>
                                    </template>

                                    
                                </div>

                                <!-- Cart Item Options Container -->
                                <div
                                    class="grid select-none gap-x-2.5 gap-y-1.5 max-sm:gap-y-0.5"
                                    v-if="item.options.length"
                                >

                                    

                                    <!-- Details Toggler -->
                                    <div class="">
                                        <p
                                            class="flex cursor-pointer items-center gap-x-4 text-base max-md:gap-x-1.5 max-md:text-sm max-sm:text-xs"
                                            @click="item.option_show = ! item.option_show"
                                        >
                                            See Details
                                            <span
                                                class="text-2xl max-md:text-xl max-sm:text-lg"
                                                :class="{'icon-arrow-up': item.option_show, 'icon-arrow-down': ! item.option_show}"
                                            ></span>
                                        </p>
                                    </div>

                                    <!-- Option Details -->
                                    <div
                                        class="grid gap-2"
                                        v-show="item.option_show"
                                    >
                                        <template v-for="attribute in item.options">
                                            <div class="max-md:grid max-md:gap-0.5">
                                                <p class="text-sm font-medium text-zinc-500 max-md:font-normal max-sm:text-xs">
                                                    {{ attribute.attribute_name + ':' }}
                                                </p>

                                                <p class="text-sm max-sm:text-xs">
                                                    <template v-if="attribute?.attribute_type === 'file'">
                                                        <a
                                                            :href="attribute.file_url"
                                                            class="text-blue-700"
                                                            target="_blank"
                                                            :download="attribute.file_name"
                                                        >
                                                            {{ attribute.file_name }}
                                                        </a>
                                                    </template>

                                                    <template v-else>
                                                        {{ attribute.option_label }}
                                                    </template>
                                                </p>
                                            </div>
                                        </template>
                                    </div>

                                    
                                </div>

                                <div class="flex flex-wrap items-center gap-5 max-md:gap-2.5">
                                    

                                <!-- Cart Item Quantity Changer -->
                                <v-quantity-changer
    class="flex items-center border border-navyBlue max-h-9 max-w-[150px] gap-x-2.5 rounded-[54px] px-3.5 py-1.5 max-md:gap-x-2 max-md:px-1 max-md:py-0.5" :value="item?.quantity" @change="updateItem($event, item)"
    name="quantity"
    value="1"
    min-value="1"
>
</v-quantity-changer>


                                    

                                

                                <!-- Cart Item Remove Button -->
                                <button
                                    type="button"
                                    class="text-blue-700 max-md:text-sm"
                                    @click="removeItem(item.id)"
                                >
                                    Remove                                </button>

                                    
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Empty Cart Section -->
                    <div
                        class="mt-32 pb-8 max-md:mt-32"
                        v-else
                    >
                        <div class="b-0 grid place-items-center gap-y-5 max-md:gap-y-0">
                            <img
                                class="max-md:h-[100px] max-md:w-[100px]"
                                src="http://localhost/onlinestore/bagisto-2.3/public/themes/shop/default/build/assets/thank-you-mhMpuEVL.png"
                            >

                            <p
                                class="text-xl max-md:text-sm"
                                role="heading"
                            >
                                Your cart is empty                            </p>
                        </div>
                    </div>
            </div>
        </template>
    
            <template v-slot:footer>
            <div class="pb-8 max-md:pb-2">
                <div
                    v-if="cart?.items?.length"
                    class="grid-col-1 grid gap-5 max-md:gap-2.5"
                >
                    <div
                        class="my-8 flex items-center justify-between border-b border-zinc-200 px-6 pb-2 max-md:my-0 max-md:border-t max-md:px-5 max-md:py-2"
                        :class="{'!justify-end': isLoading}"
                    >
                        

                        <template v-if="! isLoading">
                            <p class="text-sm font-medium text-zinc-500">
                                Subtotal                            </p>

                        <template v-if="displayTax.subtotal == 'including_tax'">
                            <p class="text-3xl font-semibold max-md:text-base">
                                {{ cart.formatted_sub_total_incl_tax }}
                            </p>
                        </template>

                        <template v-else-if="displayTax.subtotal == 'both'">
                            <p class="flex flex-col text-3xl font-semibold max-md:text-sm max-sm:text-right">
                                {{ cart.formatted_sub_total_incl_tax }}

                                <span class="text-sm font-normal text-zinc-500 max-sm:text-xs">
                                    Excl. Tax:
                                    <span class="font-medium text-black">{{ cart.formatted_sub_total }}</span>
                                </span>
                            </p>
                        </template>

                        <template v-else>
                            <p class="text-3xl font-semibold max-md:text-base">
                                {{ cart.formatted_sub_total }}
                            </p>
                        </template>
                    </template>

                        <template v-else>
                            <!-- Spinner -->
                            <svg
                                class="text-blue h-8 w-8 animate-spin text-[5px] font-semibold max-md:h-7 max-md:w-7 max-sm:h-4 max-sm:w-4"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                aria-hidden="true"
                                viewBox="0 0 24 24"
                            >
                                <circle
                                    class="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    stroke-width="4"
                                ></circle>

                                <path
                                    class="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                            </svg>
                        </template>

                            
                        </div>

                        

                        <!-- Cart Action Container -->
                        <div class="grid gap-2.5 px-6 max-md:px-4 max-sm:gap-1.5">
                            

                        <a
                            href="http://localhost/onlinestore/bagisto-2.3/public/checkout/onepage"
                            class="mx-auto block w-full cursor-pointer rounded-2xl bg-navyBlue px-11 py-4 text-center text-base font-medium text-white max-md:rounded-lg max-md:px-5 max-md:py-2"
                        >
                            Continue to Checkout                        </a>

                            

                            <div class="block cursor-pointer text-center text-base font-medium max-md:py-1.5">
                                <a href="http://localhost/onlinestore/bagisto-2.3/public/checkout/cart">
                                    View Cart                                </a>
                            </div>
                        </div>

                        
                    </div>
            </div>
        </template>
    </v-drawer>


        
        
    </script>

    <script type="module">
        app.component("v-mini-cart", {
            template: '#v-mini-cart-template',

            data() {
                return  {
                    cart: null,

                    isLoading:false,

                    displayTax: {
                        prices: "excluding_tax",
                        subtotal: "excluding_tax",
                    },
                }
            },

            mounted() {
                this.getCart();

                /**
                 * To Do: Implement this.
                 *
                 * Action.
                 */
                this.$emitter.on('update-mini-cart', (cart) => {
                    this.cart = cart;
                });
            },

            methods: {
                getCart() {
                    this.$axios.get('http://localhost/onlinestore/bagisto-2.3/public/api/checkout/cart')
                        .then(response => {
                            this.cart = response.data.data;
                        })
                        .catch(error => {});
                },

                updateItem(quantity, item) {
                    this.isLoading = true;

                    let qty = {};

                    qty[item.id] = quantity;

                    this.$axios.put('http://localhost/onlinestore/bagisto-2.3/public/api/checkout/cart', { qty })
                        .then(response => {
                            if (response.data.message) {
                                this.cart = response.data.data;
                            } else {
                                this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                            }

                            this.isLoading = false;
                        }).catch(error => this.isLoading = false);
                },

                removeItem(itemId) {
                    this.$emitter.emit('open-confirm-modal', {
                        agree: () => {
                            this.isLoading = true;

                            this.$axios.post('http://localhost/onlinestore/bagisto-2.3/public/api/checkout/cart', {
                                '_method': 'DELETE',
                                'cart_item_id': itemId,
                            })
                            .then(response => {
                                this.cart = response.data.data;

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });

                                this.isLoading = false;
                            })
                            .catch(error => {
                                this.$emitter.emit('add-flash', { type: 'error', message: response.data.message });

                                this.isLoading = false;
                            });
                        }
                    });
                },
            },
        });
    </script>
    <script
        type="text/x-template"
        id="v-quantity-changer-template"
    >
        <div>
            <span 
                class="icon-minus cursor-pointer text-2xl"
                role="button"
                tabindex="0"
                aria-label="Decrease Quantity"
                @click="decrease"
            >
            </span>

            <p class="w-2.5 select-none text-center max-sm:text-sm">
                {{ quantity }}
            </p>
            
            <span 
                class="icon-plus cursor-pointer text-2xl"
                role="button"
                tabindex="0"
                aria-label="Increase Quantity"
                @click="increase"
            >
            </span>

            <v-field
                type="hidden"
                :name="name"
                v-model="quantity"
            ></v-field>
        </div>
    </script>

    <script type="module">
        app.component("v-quantity-changer", {
            template: '#v-quantity-changer-template',

            props:['name', 'value', 'minValue'],

            data() {
                return  {
                    quantity: this.value,
                }
            },

            watch: {
                value() {
                    this.quantity = this.value;
                },
            },

            methods: {
                increase() {
                    this.$emit('change', ++this.quantity);
                },

                decrease() {
                    if (this.quantity > this.minValue) {
                        this.quantity -= 1;

                        this.$emit('change', this.quantity);
                    }
                },
            }
        });
    </script>
    <script
        type="text/x-template"
        id="v-desktop-category-template"
    >
        <!-- Loading State -->
        <div
            class="flex items-center gap-5"
            v-if="isLoading"
        >
            <span
                class="shimmer h-6 w-20 rounded"
                role="presentation"
            ></span>

            <span
                class="shimmer h-6 w-20 rounded"
                role="presentation"
            ></span>

            <span
                class="shimmer h-6 w-20 rounded"
                role="presentation"
            ></span>
        </div>

        <!-- Default category layout -->
        <div
            class="flex items-center"
            v-else-if="'default' !== 'sidebar'"
        >
            <div
                class="group relative flex h-[77px] items-center border-b-4 border-transparent hover:border-b-4 hover:border-navyBlue"
                v-for="category in categories"
            >
                <span>
                    <a
                        :href="category.url"
                        class="inline-block px-5 uppercase"
                    >
                        {{ category.name }}
                    </a>
                </span>

                <div
                    class="pointer-events-none absolute top-[78px] z-[1] max-h-[580px] w-max max-w-[1260px] translate-y-1 overflow-auto overflow-x-auto border border-b-0 border-l-0 border-r-0 border-t border-[#F3F3F3] bg-white p-9 opacity-0 shadow-[0_6px_6px_1px_rgba(0,0,0,.3)] transition duration-300 ease-out group-hover:pointer-events-auto group-hover:translate-y-0 group-hover:opacity-100 group-hover:duration-200 group-hover:ease-in ltr:-left-9 rtl:-right-9"
                    v-if="category.children && category.children.length"
                >
                    <div class="flex justify-between gap-x-[70px]">
                        <div
                            class="grid w-full min-w-max max-w-[150px] flex-auto grid-cols-[1fr] content-start gap-5"
                            v-for="pairCategoryChildren in pairCategoryChildren(category)"
                        >
                            <template v-for="secondLevelCategory in pairCategoryChildren">
                                <p class="font-medium text-navyBlue">
                                    <a :href="secondLevelCategory.url">
                                        {{ secondLevelCategory.name }}
                                    </a>
                                </p>

                                <ul
                                    class="grid grid-cols-[1fr] gap-3"
                                    v-if="secondLevelCategory.children && secondLevelCategory.children.length"
                                >
                                    <li
                                        class="text-sm font-medium text-zinc-500"
                                        v-for="thirdLevelCategory in secondLevelCategory.children"
                                    >
                                        <a :href="thirdLevelCategory.url">
                                            {{ thirdLevelCategory.name }}
                                        </a>
                                    </li>
                                </ul>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar category layout -->
        <div v-else>
            <!-- Categories Navigation -->
            <div class="flex items-center">
                <!-- "All" button for opening the category drawer -->
                <div
                    class="flex h-[77px] cursor-pointer items-center border-b-4 border-transparent hover:border-b-4 hover:border-navyBlue"
                    @click="toggleCategoryDrawer"
                >
                    <span class="flex items-center gap-1 px-5 uppercase">
                        <span class="icon-hamburger text-xl"></span>

                        All                    </span>
                </div>

                <!-- Show only first 4 categories in main navigation -->
                <div
                    class="group relative flex h-[77px] items-center border-b-4 border-transparent hover:border-b-4 hover:border-navyBlue"
                    v-for="category in categories.slice(0, 4)"
                >
                    <span>
                        <a
                            :href="category.url"
                            class="inline-block px-5 uppercase"
                        >
                            {{ category.name }}
                        </a>
                    </span>

                    <!-- Dropdown for each category -->
                    <div
                        class="pointer-events-none absolute top-[78px] z-[1] max-h-[580px] w-max max-w-[1260px] translate-y-1 overflow-auto overflow-x-auto border border-b-0 border-l-0 border-r-0 border-t border-[#F3F3F3] bg-white p-9 opacity-0 shadow-[0_6px_6px_1px_rgba(0,0,0,.3)] transition duration-300 ease-out group-hover:pointer-events-auto group-hover:translate-y-0 group-hover:opacity-100 group-hover:duration-200 group-hover:ease-in ltr:-left-9 rtl:-right-9"
                        v-if="category.children && category.children.length"
                    >
                        <div class="flex justify-between gap-x-[70px]">
                            <div
                                class="grid w-full min-w-max max-w-[150px] flex-auto grid-cols-[1fr] content-start gap-5"
                                v-for="pairCategoryChildren in pairCategoryChildren(category)"
                            >
                                <template v-for="secondLevelCategory in pairCategoryChildren">
                                    <p class="font-medium text-navyBlue">
                                        <a :href="secondLevelCategory.url">
                                            {{ secondLevelCategory.name }}
                                        </a>
                                    </p>

                                    <ul
                                        class="grid grid-cols-[1fr] gap-3"
                                        v-if="secondLevelCategory.children && secondLevelCategory.children.length"
                                    >
                                        <li
                                            class="text-sm font-medium text-zinc-500"
                                            v-for="thirdLevelCategory in secondLevelCategory.children"
                                        >
                                            <a :href="thirdLevelCategory.url">
                                                {{ thirdLevelCategory.name }}
                                            </a>
                                        </li>
                                    </ul>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bagisto Drawer Integration -->
            <v-drawer
    :is-active="isDrawerActive" @toggle="onDrawerToggle" @close="onDrawerClose"
    is-active=""
    position="left"
    width="400px"
>
            <template v-slot:toggle>
            
        </template>
    
            <template v-slot:header="{ close }">
            <div class="grid gap-y-2.5 p-6 pb-5 max-md:gap-y-1.5 max-md:border-b max-md:border-zinc-200 max-md:p-4 max-md:gap-y-1 max-md:font-semibold border-b border-gray-200">
                <div class="flex w-full items-center justify-between">
                        <p class="text-xl font-medium">
                            Categories                        </p>
                    </div>

                <div class="absolute top-5 max-sm:top-4 ltr:right-5 rtl:left-5">
                    <span
                        class="icon-cancel cursor-pointer text-3xl max-md:text-2xl"
                        @click="close"
                    >
                    </span>
                </div>
            </div>
        </template>
    
            <template v-slot:content>
            <div class="flex-1 overflow-auto px-6 max-md:px-4 !px-0">
                <!-- Wrapper with transition effects -->
                    <div class="relative h-full overflow-hidden">
                        <!-- Sliding container -->
                        <div
                            class="flex h-full transition-transform duration-300"
                            :class="{
                                'ltr:translate-x-0 rtl:translate-x-0': currentViewLevel !== 'third',
                                'ltr:-translate-x-full rtl:translate-x-full': currentViewLevel === 'third'
                            }"
                        >
                            <!-- First level view -->
                            <div class="h-[calc(100vh-74px)] w-full flex-shrink-0 overflow-auto">
                                <div class="py-4">
                                    <div
                                        v-for="category in categories"
                                        :key="category.id"
                                        :class="{'mb-2': category.children && category.children.length}"
                                    >
                                        <div class="flex cursor-pointer items-center justify-between px-6 py-2 transition-colors duration-200 hover:bg-gray-100">
                                            <a
                                                :href="category.url"
                                                class="text-base font-medium text-black"
                                            >
                                                {{ category.name }}
                                            </a>
                                        </div>

                                        <!-- Second Level Categories -->
                                        <div v-if="category.children && category.children.length" >
                                            <div
                                                v-for="secondLevelCategory in category.children"
                                                :key="secondLevelCategory.id"
                                            >
                                                <div
                                                    class="flex cursor-pointer items-center justify-between px-6 py-2 transition-colors duration-200 hover:bg-gray-100"
                                                    @click="showThirdLevel(secondLevelCategory, category, $event)"
                                                >
                                                    <a
                                                        :href="secondLevelCategory.url"
                                                        class="text-sm font-normal"
                                                    >
                                                        {{ secondLevelCategory.name }}
                                                    </a>

                                                    <span
                                                        v-if="secondLevelCategory.children && secondLevelCategory.children.length"
                                                        class="icon-arrow-right rtl:icon-arrow-left"
                                                    ></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Third level view -->
                            <div
                                class="h-full w-full flex-shrink-0"
                                v-if="currentViewLevel === 'third'"
                            >
                                <div class="border-b border-gray-200 px-6 py-4">
                                    <button
                                        @click="goBackToMainView"
                                        class="flex items-center justify-center gap-2 focus:outline-none"
                                        aria-label="Go back"
                                    >
                                        <span class="icon-arrow-left rtl:icon-arrow-right text-lg"></span>

                                        <p class="text-base font-medium text-black">
                                            Back to Main Menu                                        </p>
                                    </button>
                                </div>

                                <!-- Third Level Content -->
                                <div class="py-4">
                                    <div
                                        v-for="thirdLevelCategory in currentSecondLevelCategory?.children"
                                        :key="thirdLevelCategory.id"
                                        class="mb-2"
                                    >
                                        <a
                                            :href="thirdLevelCategory.url"
                                            class="block px-6 py-2 text-sm transition-colors duration-200 hover:bg-gray-100"
                                        >
                                            {{ thirdLevelCategory.name }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </template>
    
    </v-drawer>

        </div>
    </script>

    <script type="module">
        app.component('v-desktop-category', {
            template: '#v-desktop-category-template',

            data() {
                return {
                    isLoading: true,
                    categories: [],
                    isDrawerActive: false,
                    currentViewLevel: 'main',
                    currentSecondLevelCategory: null,
                    currentParentCategory: null
                }
            },

            mounted() {
                this.getCategories();
            },

            methods: {
                getCategories() {
                    this.$axios.get("http://localhost/onlinestore/bagisto-2.3/public/api/categories/tree")
                        .then(response => {
                            this.isLoading = false;
                            this.categories = response.data.data;
                        })
                        .catch(error => {
                            console.log(error);
                        });
                },

                pairCategoryChildren(category) {
                    if (! category.children) return [];

                    return category.children.reduce((result, value, index, array) => {
                        if (index % 2 === 0) {
                            result.push(array.slice(index, index + 2));
                        }
                        return result;
                    }, []);
                },

                toggleCategoryDrawer() {
                    this.isDrawerActive = !this.isDrawerActive;
                    if (this.isDrawerActive) {
                        this.currentViewLevel = 'main';
                    }
                },

                onDrawerToggle(event) {
                    this.isDrawerActive = event.isActive;
                },

                onDrawerClose(event) {
                    this.isDrawerActive = false;
                },

                showThirdLevel(secondLevelCategory, parentCategory, event) {
                    if (secondLevelCategory.children && secondLevelCategory.children.length) {
                        this.currentSecondLevelCategory = secondLevelCategory;
                        this.currentParentCategory = parentCategory;
                        this.currentViewLevel = 'third';

                        if (event) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                    }
                },

                goBackToMainView() {
                    this.currentViewLevel = 'main';
                }
            },
        });
    </script>

        
        <script>
            /**
             * Load event, the purpose of using the event is to mount the application
             * after all of our `Vue` components which is present in blade file have
             * been registered in the app. No matter what `app.mount()` should be
             * called in the last.
             */
            window.addEventListener("load", function (event) {
                app.mount("#app");
            });
        </script>

        

        <script type="text/javascript">
            
        </script>
    </body>
</html>
";s:4:"type";s:6:"normal";}";