<?php

namespace App\Http\Controllers\Admin\Customers;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Product;
use App\Services\CartService;
use App\Repositories\CustomerRepository;
use App\Http\Resources\CartResource;
use App\Http\Resources\CartItemResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;

class CartController extends Controller
{
    public function __construct(
        protected CustomerRepository $customerRepository,
        protected CartService $cartService
    ) {}

    /**
     * Display customer's cart
     */
    public function index(Customer $customer): JsonResponse
    {
        try {
            $cart = $this->cartService->getCustomerCart($customer);
            
            if (!$cart) {
                return response()->json([
                    'success' => true,
                    'message' => 'No active cart found for customer',
                    'data' => null
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => new CartResource($cart)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve cart: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new cart for customer
     */
    public function store(Request $request, Customer $customer): JsonResponse
    {
        try {
            $cart = $this->cartService->createCartForCustomer($customer, [
                'currency_code' => $request->input('currency_code', config('app.currency', 'USD')),
                'channel_id' => $request->input('channel_id', 1),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Cart created successfully',
                'data' => new CartResource($cart)
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create cart: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add product to customer's cart
     */
    public function addItem(Request $request, Customer $customer): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'options' => 'sometimes|array',
        ]);

        try {
            $product = Product::findOrFail($request->product_id);
            
            // Set customer context for cart service
            $this->cartService->setCustomerContext($customer);
            
            $cartItem = $this->cartService->addToCart(
                $product,
                $request->quantity,
                $request->input('options', [])
            );

            return response()->json([
                'success' => true,
                'message' => 'Product added to cart successfully',
                'data' => new CartItemResource($cartItem)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add product to cart: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Update cart item quantity
     */
    public function updateItem(Request $request, Customer $customer, int $cartItemId): JsonResponse
    {
        $request->validate([
            'quantity' => 'required|integer|min:1',
        ]);

        try {
            // Set customer context for cart service
            $this->cartService->setCustomerContext($customer);
            
            $cartItem = $this->cartService->updateCartItem($cartItemId, $request->quantity);

            return response()->json([
                'success' => true,
                'message' => 'Cart item updated successfully',
                'data' => new CartItemResource($cartItem)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update cart item: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove item from cart
     */
    public function removeItem(Customer $customer, int $cartItemId): JsonResponse
    {
        try {
            // Set customer context for cart service
            $this->cartService->setCustomerContext($customer);
            
            $removed = $this->cartService->removeFromCart($cartItemId);

            if (!$removed) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cart item not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Item removed from cart successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove item from cart: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear customer's cart
     */
    public function clear(Customer $customer): JsonResponse
    {
        try {
            // Set customer context for cart service
            $this->cartService->setCustomerContext($customer);
            
            $cleared = $this->cartService->clearCart();

            if (!$cleared) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active cart found to clear'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Cart cleared successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cart: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Apply coupon to customer's cart
     */
    public function applyCoupon(Request $request, Customer $customer): JsonResponse
    {
        $request->validate([
            'coupon_code' => 'required|string|max:50',
        ]);

        try {
            // Set customer context for cart service
            $this->cartService->setCustomerContext($customer);
            
            $applied = $this->cartService->applyCoupon($request->coupon_code);

            if (!$applied) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid or expired coupon code'
                ], 400);
            }

            $cart = $this->cartService->getActiveCart();

            return response()->json([
                'success' => true,
                'message' => 'Coupon applied successfully',
                'data' => new CartResource($cart)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to apply coupon: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove coupon from customer's cart
     */
    public function removeCoupon(Customer $customer): JsonResponse
    {
        try {
            // Set customer context for cart service
            $this->cartService->setCustomerContext($customer);
            
            $removed = $this->cartService->removeCoupon();

            if (!$removed) {
                return response()->json([
                    'success' => false,
                    'message' => 'No coupon found to remove'
                ], 404);
            }

            $cart = $this->cartService->getActiveCart();

            return response()->json([
                'success' => true,
                'message' => 'Coupon removed successfully',
                'data' => new CartResource($cart)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove coupon: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get cart summary for customer
     */
    public function summary(Customer $customer): JsonResponse
    {
        try {
            // Set customer context for cart service
            $this->cartService->setCustomerContext($customer);
            
            $summary = $this->cartService->getCartSummary();

            return response()->json([
                'success' => true,
                'data' => $summary
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get cart summary: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Convert customer's cart to order
     */
    public function convertToOrder(Request $request, Customer $customer): JsonResponse
    {
        $request->validate([
            'shipping_address' => 'required|array',
            'billing_address' => 'required|array',
            'payment_method' => 'required|string',
            'shipping_method' => 'required|string',
        ]);

        try {
            // Set customer context for cart service
            $this->cartService->setCustomerContext($customer);
            
            $cart = $this->cartService->getActiveCart();
            
            if (!$cart || $cart->items->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cart is empty or not found'
                ], 400);
            }

            $orderData = $this->cartService->prepareOrderData(
                $cart,
                $request->shipping_address,
                $request->billing_address,
                $request->payment_method,
                $request->shipping_method
            );

            // Here you would create the order using OrderService
            // $order = $this->orderService->createOrder($orderData);

            return response()->json([
                'success' => true,
                'message' => 'Order data prepared successfully',
                'data' => $orderData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to convert cart to order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get abandoned carts for all customers
     */
    public function abandoned(Request $request): JsonResponse
    {
        try {
            $days = $request->input('days', 7);
            
            $abandonedCarts = $this->cartService->getAbandonedCarts($days);

            return response()->json([
                'success' => true,
                'data' => CartResource::collection($abandonedCarts),
                'meta' => [
                    'total' => $abandonedCarts->count(),
                    'days' => $days
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get abandoned carts: ' . $e->getMessage()
            ], 500);
        }
    }
}
