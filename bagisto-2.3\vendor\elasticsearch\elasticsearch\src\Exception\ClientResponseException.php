<?php
/**
 * Elasticsearch PHP Client
 *
 * @link      https://github.com/elastic/elasticsearch-php
 * @copyright Copyright (c) Elasticsearch B.V (https://www.elastic.co)
 * @license   https://opensource.org/licenses/MIT MIT License
 *
 * Licensed to Elasticsearch B.V under one or more agreements.
 * Elasticsearch B.V licenses this file to you under the MIT License.
 * See the LICENSE file in the project root for more information.
 */
declare(strict_types=1);

namespace Elastic\Elasticsearch\Exception;

use Elastic\Elasticsearch\Traits\ResponseTrait;
use Exception;

/**
 * HTTP client error with 4xx status code
 */
class ClientResponseException extends Exception implements ElasticsearchException
{
    use ResponseTrait;
}