<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'sku',
        'type',
        'status',
        'visibility',
        'price',
        'special_price',
        'special_price_from',
        'special_price_to',
        'cost',
        'weight',
        'dimensions',
        'category_id',
        'brand_id',
        'tax_category_id',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'featured',
        'new',
        'sort_order',
    ];

    protected $casts = [
        'status' => 'boolean',
        'visibility' => 'boolean',
        'featured' => 'boolean',
        'new' => 'boolean',
        'price' => 'decimal:2',
        'special_price' => 'decimal:2',
        'cost' => 'decimal:2',
        'weight' => 'decimal:3',
        'dimensions' => 'array',
        'special_price_from' => 'date',
        'special_price_to' => 'date',
        'sort_order' => 'integer',
    ];

    // Product types
    const TYPE_SIMPLE = 'simple';
    const TYPE_CONFIGURABLE = 'configurable';
    const TYPE_VIRTUAL = 'virtual';
    const TYPE_GROUPED = 'grouped';
    const TYPE_BUNDLE = 'bundle';
    const TYPE_DOWNLOADABLE = 'downloadable';

    // Visibility options
    const VISIBILITY_NOT_VISIBLE = 1;
    const VISIBILITY_CATALOG = 2;
    const VISIBILITY_SEARCH = 3;
    const VISIBILITY_BOTH = 4;

    /**
     * Relationships
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'product_categories');
    }

    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    public function images(): HasMany
    {
        return $this->hasMany(ProductImage::class)->orderBy('sort_order');
    }

    public function inventories(): HasMany
    {
        return $this->hasMany(ProductInventory::class);
    }

    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'product_attribute_values')
                    ->withPivot('text_value', 'integer_value', 'float_value', 'datetime_value', 'date_value', 'boolean_value');
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(ProductReview::class);
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    public function wishlistItems(): HasMany
    {
        return $this->hasMany(WishlistItem::class);
    }

    public function relatedProducts(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_relations', 'product_id', 'related_product_id');
    }

    public function crossSellProducts(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_cross_sells', 'product_id', 'cross_sell_product_id');
    }

    public function upSellProducts(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_up_sells', 'product_id', 'up_sell_product_id');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    public function scopeVisible($query)
    {
        return $query->where('visibility', '!=', self::VISIBILITY_NOT_VISIBLE);
    }

    public function scopeInStock($query)
    {
        return $query->whereHas('inventories', function ($q) {
            $q->where('quantity', '>', 0);
        });
    }

    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    public function scopeNew($query)
    {
        return $query->where('new', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId)
                     ->orWhereHas('categories', function ($q) use ($categoryId) {
                         $q->where('categories.id', $categoryId);
                     });
    }

    /**
     * Accessors & Mutators
     */
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    public function getFormattedSpecialPriceAttribute(): ?string
    {
        return $this->special_price ? '$' . number_format($this->special_price, 2) : null;
    }

    public function getFinalPriceAttribute(): float
    {
        if ($this->hasSpecialPrice()) {
            return $this->special_price;
        }

        return $this->price;
    }

    public function getFormattedFinalPriceAttribute(): string
    {
        return '$' . number_format($this->final_price, 2);
    }

    public function getMainImageAttribute(): ?string
    {
        return $this->images->first()?->image_path;
    }

    public function getStockQuantityAttribute(): int
    {
        return $this->inventories->sum('quantity');
    }

    public function getAverageRatingAttribute(): float
    {
        return $this->reviews()->avg('rating') ?? 0;
    }

    public function getReviewsCountAttribute(): int
    {
        return $this->reviews()->count();
    }

    /**
     * Helper Methods
     */
    public function hasSpecialPrice(): bool
    {
        if (!$this->special_price) {
            return false;
        }

        $now = now();
        
        if ($this->special_price_from && $now->lt($this->special_price_from)) {
            return false;
        }

        if ($this->special_price_to && $now->gt($this->special_price_to)) {
            return false;
        }

        return true;
    }

    public function isInStock(): bool
    {
        return $this->stock_quantity > 0;
    }

    public function canAddToCart(int $quantity = 1): bool
    {
        if ($this->type === self::TYPE_VIRTUAL) {
            return true;
        }

        return $this->stock_quantity >= $quantity;
    }

    public function getDiscountPercentage(): ?float
    {
        if (!$this->hasSpecialPrice()) {
            return null;
        }

        return round((($this->price - $this->special_price) / $this->price) * 100, 2);
    }

    public function getSavingsAmount(): ?float
    {
        if (!$this->hasSpecialPrice()) {
            return null;
        }

        return $this->price - $this->special_price;
    }

    public function isNew(): bool
    {
        return $this->new || $this->created_at->gte(now()->subDays(30));
    }

    public function isFeatured(): bool
    {
        return $this->featured;
    }

    public function isVisible(): bool
    {
        return $this->visibility !== self::VISIBILITY_NOT_VISIBLE;
    }

    public function isVisibleInCatalog(): bool
    {
        return in_array($this->visibility, [self::VISIBILITY_CATALOG, self::VISIBILITY_BOTH]);
    }

    public function isVisibleInSearch(): bool
    {
        return in_array($this->visibility, [self::VISIBILITY_SEARCH, self::VISIBILITY_BOTH]);
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = \Str::slug($product->name);
            }
            
            if (empty($product->sku)) {
                $product->sku = 'PRD-' . strtoupper(\Str::random(8));
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = \Str::slug($product->name);
            }
        });
    }
}
