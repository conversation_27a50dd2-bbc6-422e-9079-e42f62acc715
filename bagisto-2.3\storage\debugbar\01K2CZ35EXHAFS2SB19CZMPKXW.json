{"__meta": {"id": "01K2CZ35EXHAFS2SB19CZMPKXW", "datetime": "2025-08-11 21:53:23", "utime": **********.358764, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/api/products?sort=name-desc&limit=12", "ip": "::1"}, "modules": {"count": 4, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (32)", "Webkul\\Attribute\\Models\\AttributeFamily (7)"], "views": [], "queries": [{"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('sort', 'limit', 'channel_id', 'status', 'visible_individually', 'url_key')", "duration": 1.06, "duration_str": "1.06s", "connection": "Online_store"}, {"sql": "select * from `attributes` where `code` = 'name'", "duration": 1, "duration_str": "1s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 21.76, "duration_str": "21.76s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 1.14, "duration_str": "1.14s", "connection": "Online_store"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 1.95, "duration_str": "1.95s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 3.78, "duration_str": "3.78s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 1.73, "duration_str": "1.73s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 1.04, "duration_str": "1.04s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.78, "duration_str": "780ms", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.78, "duration_str": "780ms", "connection": "Online_store"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (1)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "duration": 22.02, "duration_str": "22.02s", "connection": "Online_store"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.34, "duration_str": "1.34s", "connection": "Online_store"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 1.1, "duration_str": "1.1s", "connection": "Online_store"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "duration": 12.38, "duration_str": "12.38s", "connection": "Online_store"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 1.21, "duration_str": "1.21s", "connection": "Online_store"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (20)", "Webkul\\Product\\Models\\ProductImage (17)", "Webkul\\Product\\Models\\ProductAttributeValue (376)", "Webkul\\Product\\Models\\ProductPriceIndex (45)", "Webkul\\Product\\Models\\ProductInventoryIndex (18)", "Webkul\\Product\\Models\\ProductBundleOption (4)", "Webkul\\Product\\Models\\ProductBundleOptionProduct (4)", "Webkul\\Product\\Models\\ProductGroupedProduct (3)"], "views": [], "queries": [{"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id` order by `sort_product_attribute_values`.`text_value` desc limit 12 offset 0", "duration": 13.24, "duration_str": "13.24s", "connection": "Online_store"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `position` asc", "duration": 3.06, "duration_str": "3.06s", "connection": "Online_store"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `position` asc", "duration": 3.21, "duration_str": "3.21s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "duration": 8.3, "duration_str": "8.3s", "connection": "Online_store"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "duration": 2.01, "duration_str": "2.01s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "duration": 12.6, "duration_str": "12.6s", "connection": "Online_store"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "duration": 5.92, "duration_str": "5.92s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`parent_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "duration": 3.12, "duration_str": "3.12s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (8, 9, 10, 11)", "duration": 2.69, "duration_str": "2.69s", "connection": "Online_store"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (8, 9, 10, 11)", "duration": 1.23, "duration_str": "1.23s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (8, 9, 10, 11)", "duration": 0.78, "duration_str": "780ms", "connection": "Online_store"}, {"sql": "select * from `product_bundle_options` where `product_bundle_options`.`product_id` = 6 and `product_bundle_options`.`product_id` is not null", "duration": 0.9, "duration_str": "900ms", "connection": "Online_store"}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 1 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "duration": 3, "duration_str": "3s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 1 limit 1", "duration": 1.8, "duration_str": "1.8s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 1 and `product_attribute_values`.`product_id` is not null", "duration": 1.17, "duration_str": "1.17s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 1 and `product_inventory_indices`.`product_id` is not null", "duration": 0.89, "duration_str": "890ms", "connection": "Online_store"}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 2 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "duration": 1, "duration_str": "1s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 2 limit 1", "duration": 1.11, "duration_str": "1.11s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 2 and `product_attribute_values`.`product_id` is not null", "duration": 1.65, "duration_str": "1.65s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 2 and `product_inventory_indices`.`product_id` is not null", "duration": 7.1, "duration_str": "7.1s", "connection": "Online_store"}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 3 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "duration": 3.9, "duration_str": "3.9s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 3 limit 1", "duration": 2.2, "duration_str": "2.2s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 3 and `product_attribute_values`.`product_id` is not null", "duration": 1.5, "duration_str": "1.5s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 3 and `product_inventory_indices`.`product_id` is not null", "duration": 0.8, "duration_str": "800ms", "connection": "Online_store"}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 4 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "duration": 0.92, "duration_str": "920ms", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 4 limit 1", "duration": 0.91, "duration_str": "910ms", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 4 and `product_attribute_values`.`product_id` is not null", "duration": 1.33, "duration_str": "1.33s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 4 and `product_inventory_indices`.`product_id` is not null", "duration": 0.83, "duration_str": "830ms", "connection": "Online_store"}, {"sql": "select * from `product_grouped_products` where `product_grouped_products`.`product_id` = 5 and `product_grouped_products`.`product_id` is not null", "duration": 1.11, "duration_str": "1.11s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 1 limit 1", "duration": 0.83, "duration_str": "830ms", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 1 and `product_attribute_values`.`product_id` is not null", "duration": 1.32, "duration_str": "1.32s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 1 and `product_inventory_indices`.`product_id` is not null", "duration": 0.96, "duration_str": "960ms", "connection": "Online_store"}]}]}, "messages": {"count": 22, "messages": [{"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.718825, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.719312, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.719651, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.719947, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.720248, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.720538, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.720827, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.721115, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.721409, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.721715, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.722016, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.825588, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.856401, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.899972, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.946558, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:22] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.996952, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:23] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.031952, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:23] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.062983, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:23] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.241413, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:23] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.302151, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:23] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.32331, "xdebug_link": null, "collector": "log"}, {"message": "[21:53:23] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.347026, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754929401.351085, "end": **********.386692, "duration": 2.035607099533081, "duration_str": "2.04s", "measures": [{"label": "Booting", "start": 1754929401.351085, "relative_start": 0, "end": **********.197406, "relative_end": **********.197406, "duration": 0.****************, "duration_str": "846ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.19743, "relative_start": 0.****************, "end": **********.386695, "relative_end": 2.86102294921875e-06, "duration": 1.****************, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.236668, "relative_start": 0.****************, "end": **********.250374, "relative_end": **********.250374, "duration": 0.013705968856811523, "duration_str": "13.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.722365, "relative_start": 1.****************, "end": **********.354419, "relative_end": **********.354419, "duration": 0.****************, "duration_str": "632ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::products.prices.index", "start": **********.824544, "relative_start": 1.***************, "end": **********.824544, "relative_end": **********.824544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.855781, "relative_start": 1.****************, "end": **********.855781, "relative_end": **********.855781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.899239, "relative_start": 1.5481541156768799, "end": **********.899239, "relative_end": **********.899239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.945736, "relative_start": 1.5946509838104248, "end": **********.945736, "relative_end": **********.945736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.configurable", "start": **********.991613, "relative_start": 1.6405279636383057, "end": **********.991613, "relative_end": **********.991613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.031227, "relative_start": 1.6801421642303467, "end": **********.031227, "relative_end": **********.031227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.062329, "relative_start": 1.7112441062927246, "end": **********.062329, "relative_end": **********.062329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.bundle", "start": **********.240445, "relative_start": 1.889359951019287, "end": **********.240445, "relative_end": **********.240445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.grouped", "start": **********.301421, "relative_start": 1.95033597946167, "end": **********.301421, "relative_end": **********.301421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.322801, "relative_start": 1.9717161655426025, "end": **********.322801, "relative_end": **********.322801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.346521, "relative_start": 1.9954359531402588, "end": **********.346521, "relative_end": **********.346521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 43252992, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 11, "nb_templates": 11, "templates": [{"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.824402, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.85565, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.89908, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.945554, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.configurable", "param_count": null, "params": [], "start": **********.991457, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/configurable.blade.phpshop::products.prices.configurable", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.031073, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.06219, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.bundle", "param_count": null, "params": [], "start": **********.240314, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/bundle.blade.phpshop::products.prices.bundle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fbundle.blade.php&line=1", "ajax": false, "filename": "bundle.blade.php", "line": "?"}}, {"name": "shop::products.prices.grouped", "param_count": null, "params": [], "start": **********.30133, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/grouped.blade.phpshop::products.prices.grouped", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Fgrouped.blade.php&line=1", "ajax": false, "filename": "grouped.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.322677, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.346431, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 48, "nb_statements": 48, "nb_visible_statements": 48, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.18661000000000003, "accumulated_duration_str": "187ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "type": "query", "params": [], "bindings": ["localhost", "http://localhost", "https://localhost"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.319764, "duration": 0.022019999999999998, "duration_str": "22.02ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 11.8}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.3630712, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 11.8, "width_percent": 0.718}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.373682, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 12.518, "width_percent": 0.589}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 744}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.468117, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "Online_store", "explain": null, "start_percent": 13.108, "width_percent": 0.648}, {"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('sort', 'limit', 'channel_id', 'status', 'visible_individually', 'url_key')", "type": "query", "params": [], "bindings": ["sort", "limit", "channel_id", "status", "visible_individually", "url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 198}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 293}, {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 485}], "start": **********.487703, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "Online_store", "explain": null, "start_percent": 13.756, "width_percent": 0.568}, {"sql": "select * from `attributes` where `code` = 'name'", "type": "query", "params": [], "bindings": ["name"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 397}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}], "start": **********.500786, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "Online_store", "explain": null, "start_percent": 14.324, "width_percent": 0.536}, {"sql": "select count(*) as aggregate from (select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": [1, 2, "en", "1", 3, 7, 1, 8, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.5096889, "duration": 0.02215, "duration_str": "22.15ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 14.86, "width_percent": 11.87}, {"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id` order by `sort_product_attribute_values`.`text_value` desc limit 12 offset 0", "type": "query", "params": [], "bindings": [1, 2, "en", "1", 3, 7, 1, 8, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.5389268, "duration": 0.01324, "duration_str": "13.24ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 26.73, "width_percent": 7.095}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.561427, "duration": 0.02176, "duration_str": "21.76ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 33.825, "width_percent": 11.661}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.594864, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 45.485, "width_percent": 1.64}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.603956, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 47.125, "width_percent": 1.72}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.6139688, "duration": 0.0083, "duration_str": "8.3ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 48.845, "width_percent": 4.448}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.6365988, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 53.293, "width_percent": 1.077}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.645792, "duration": 0.0126, "duration_str": "12.6ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 54.37, "width_percent": 6.752}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.665888, "duration": 0.00592, "duration_str": "5.92ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 61.122, "width_percent": 3.172}, {"sql": "select * from `products` where `products`.`parent_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.677005, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 64.295, "width_percent": 1.672}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.685844, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 65.966, "width_percent": 0.611}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.691046, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 66.577, "width_percent": 1.442}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.700817, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 68.019, "width_percent": 0.659}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 27, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 437}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 214}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.7060099, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "Online_store", "explain": null, "start_percent": 68.678, "width_percent": 0.418}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 213}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}], "start": **********.739724, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "Online_store", "explain": null, "start_percent": 69.096, "width_percent": 1.045}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 455}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 477}], "start": **********.800815, "duration": 0.01238, "duration_str": "12.38ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 70.141, "width_percent": 6.634}, {"sql": "select * from `product_bundle_options` where `product_bundle_options`.`product_id` = 6 and `product_bundle_options`.`product_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 528}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.086989, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 76.775, "width_percent": 0.482}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 1 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 530}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.092411, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 77.257, "width_percent": 1.608}, {"sql": "select * from `products` where `products`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.099316, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "Bundle.php:532", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FBundle.php&line=532", "ajax": false, "filename": "Bundle.php", "line": "532"}, "connection": "Online_store", "explain": null, "start_percent": 78.865, "width_percent": 0.965}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.1070132, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 79.83, "width_percent": 2.026}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 1 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.117661, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 81.855, "width_percent": 0.627}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 1 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.122988, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 82.482, "width_percent": 0.477}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 2 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 530}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.1283112, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 82.959, "width_percent": 0.536}, {"sql": "select * from `products` where `products`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.133446, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Bundle.php:532", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FBundle.php&line=532", "ajax": false, "filename": "Bundle.php", "line": "532"}, "connection": "Online_store", "explain": null, "start_percent": 83.495, "width_percent": 0.595}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.1412292, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 84.09, "width_percent": 0.927}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 2 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.149381, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 85.017, "width_percent": 0.884}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 2 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.1567929, "duration": 0.0070999999999999995, "duration_str": "7.1ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 85.901, "width_percent": 3.805}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 3 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 530}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.168766, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 89.706, "width_percent": 2.09}, {"sql": "select * from `products` where `products`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.176911, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "Bundle.php:532", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FBundle.php&line=532", "ajax": false, "filename": "Bundle.php", "line": "532"}, "connection": "Online_store", "explain": null, "start_percent": 91.796, "width_percent": 1.179}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.185429, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 92.975, "width_percent": 0.557}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 3 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.1929781, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 93.532, "width_percent": 0.804}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 3 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.199383, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 94.336, "width_percent": 0.429}, {"sql": "select * from `product_bundle_option_products` where `product_bundle_option_products`.`product_bundle_option_id` = 4 and `product_bundle_option_products`.`product_bundle_option_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 530}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.204408, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 94.764, "width_percent": 0.493}, {"sql": "select * from `products` where `products`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 416}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.209161, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Bundle.php:532", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Bundle.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Bundle.php", "line": 532}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FBundle.php&line=532", "ajax": false, "filename": "Bundle.php", "line": "532"}, "connection": "Online_store", "explain": null, "start_percent": 95.257, "width_percent": 0.488}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.215926, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 95.745, "width_percent": 0.418}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 4 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.222013, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 96.163, "width_percent": 0.713}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 4 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 341}], "start": **********.228398, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 96.876, "width_percent": 0.445}, {"sql": "select * from `product_grouped_products` where `product_grouped_products`.`product_id` = 5 and `product_grouped_products`.`product_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Grouped.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Grouped.php", "line": 151}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.2611399, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 97.321, "width_percent": 0.595}, {"sql": "select * from `products` where `products`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Grouped.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Grouped.php", "line": 152}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/PaginatedResourceResponse.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php", "line": 19}], "start": **********.266784, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Grouped.php:152", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Grouped.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Grouped.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FGrouped.php&line=152", "ajax": false, "filename": "Grouped.php", "line": "152"}, "connection": "Online_store", "explain": null, "start_percent": 97.915, "width_percent": 0.445}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.2715988, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 98.36, "width_percent": 0.418}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 1 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 140}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.277512, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 98.778, "width_percent": 0.707}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 1 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 144}], "start": **********.2865448, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 99.486, "width_percent": 0.514}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 376, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 32, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductBundleOption": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductBundleOption.php&line=1", "ajax": false, "filename": "ProductBundleOption.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductBundleOptionProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductBundleOptionProduct.php&line=1", "ajax": false, "filename": "ProductBundleOptionProduct.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductGroupedProduct": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductGroupedProduct.php&line=1", "ajax": false, "filename": "ProductGroupedProduct.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}}, "count": 530, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/api/products?limit=12&sort=name-desc", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index", "uri": "GET api/products", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php:26-56</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "2.06s", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-607658842 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"9 characters\">name-desc</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-607658842\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1849200745 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1849200745\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InhSVVg0MG00YXQ5R0ZKbUt3OThGQmc9PSIsInZhbHVlIjoiV2U2eGZCZTJMdys2cjlMS28yZm1oVW10V1UyOUNRUUwydllyVHlKS01lczBUY0k3VFVwTHVGQXBYbktQU0NaZkxOR001Y1daYjFYcTU0QkpXS0NlUlgvQVZzS0NJNDRqWlBacUVGdDJrMksySVMxc1dmeWZNVzhXVzdjNTVWSVAiLCJtYWMiOiI3YzNlYWI3N2Q5MzA3YjE4YzE5MzA4ZjM0MzIzNmU1MjQ5MTg2NTQ1NmIyMzIyZjdjNmFiOTE0NGJjYmE4NGVmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://localhost/onlinestore/bagisto-2.3/public/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1090 characters\">bagisto_session=eyJpdiI6ImpDWlNrdnV1KzVRcHJsU1NOQ1RaRHc9PSIsInZhbHVlIjoiNit6V3FHZEdhMzRvcGk4WnFBZUlOK09WTVZxNm5NbElac2JoaE5hV1krZGFOeExFMVpLZVcyMVdJOUFkdk0rRVZZczB3bkF0R0hxRzVjdnMrUm1KU3pkNmlHeGJYT2JuNXVyblByaUg1Qlozc3BmaEFzMkZEZmx4VzBTT3lydnEiLCJtYWMiOiIyYzI4MGJhZTYyZDJkN2I1MDdjMGE4YjIxM2M1ZGJiOTFiYWY3OGYxNGU5ZGIyZTg4ODlkN2U5NTY4ZGIwYTg4IiwidGFnIjoiIn0%3D; dark_mode=0; XSRF-TOKEN=eyJpdiI6InhSVVg0MG00YXQ5R0ZKbUt3OThGQmc9PSIsInZhbHVlIjoiV2U2eGZCZTJMdys2cjlMS28yZm1oVW10V1UyOUNRUUwydllyVHlKS01lczBUY0k3VFVwTHVGQXBYbktQU0NaZkxOR001Y1daYjFYcTU0QkpXS0NlUlgvQVZzS0NJNDRqWlBacUVGdDJrMksySVMxc1dmeWZNVzhXVzdjNTVWSVAiLCJtYWMiOiI3YzNlYWI3N2Q5MzA3YjE4YzE5MzA4ZjM0MzIzNmU1MjQ5MTg2NTQ1NmIyMzIyZjdjNmFiOTE0NGJjYmE4NGVmIiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6IkZ0TlNtU0w3K3QxT09kb29JdTVINFE9PSIsInZhbHVlIjoiTHV1ek1SYU1PQTVVQ0RoVmJIblpiaThhd0dPYzl2dG1MRk1nSDRkUmVHLzlFczcrUUZTdzhtSnFhUUdYd0xBenp2c2hnYU5LVnlyeW9OTzkrYXNVWUhFUWZQa0JqRUxQanJyQjBDVXF2dnJoaGZMTVNtcHpEUnpoSWRNeWhpSFMiLCJtYWMiOiJiZDAyNDRmYWZkOGRmMWE5YzEwMzcxZTRmNDI4Mjc3YjdlYjlhMWEyYmI4ZDIzYjUyZDQwMDg5OTAwNGNjNjE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-582132256 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bagisto_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TMRz3GXEM8sAcFV1fikDiVtK46lGff3YWRNL67O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582132256\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-82688920 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 16:23:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82688920\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-473889334 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://localhost/onlinestore/bagisto-2.3/public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473889334\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/api/products?limit=12&sort=name-desc", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index"}, "badge": null}}