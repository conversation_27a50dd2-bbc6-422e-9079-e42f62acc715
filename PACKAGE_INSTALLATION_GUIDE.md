# Custom Laravel E-commerce - Package Installation Guide

## 🚀 Step-by-Step Installation

### 1. Create New Laravel Project
```bash
# Create new Laravel 11 project
composer create-project laravel/laravel custom-ecommerce
cd custom-ecommerce

# Verify Laravel version
php artisan --version
```

### 2. PHP Extensions Required
Make sure these PHP extensions are enabled in your `php.ini`:

```ini
# Core Extensions (Required)
extension=calendar
extension=curl
extension=intl
extension=mbstring
extension=openssl
extension=pdo
extension=pdo_mysql
extension=tokenizer
extension=gd
extension=zip
extension=fileinfo
extension=json
extension=xml
extension=dom
extension=xmlwriter
extension=xmlreader
```

**Enable in XAMPP:**
1. Open `D:\xampp\php\php.ini`
2. Remove semicolon (`;`) from the extensions above
3. Restart Apache

### 3. Core Composer Packages

#### Essential E-commerce Packages
```bash
# Image Processing
composer require intervention/image:^2.4

# PDF Generation
composer require barryvdh/laravel-dompdf:^2.0.0

# Excel Import/Export
composer require maatwebsite/excel:^3.1.46

# Authentication & Authorization
composer require laravel/sanctum:^4.0
composer require laravel/ui:^4.0

# Social Login (Optional)
composer require laravel/socialite:^5.0

# Nested Categories
composer require kalnoy/nestedset:^6.0

# Repository Pattern
composer require prettus/l5-repository:^2.6

# Breadcrumbs
composer require diglactic/laravel-breadcrumbs:^9.0

# HTTP Client
composer require guzzlehttp/guzzle:^7.0.1

# Caching
composer require spatie/laravel-responsecache:^7.4

# Sitemap Generation
composer require spatie/laravel-sitemap:^7.3

# Visitor Tracking (Optional)
composer require shetabit/visitor:^4.1

# Redis (Optional - for better caching)
composer require predis/predis:^2.2

# Performance (Optional)
composer require laravel/octane:^2.3
```

#### Payment Gateways
```bash
# PayPal Integration
composer require paypal/paypal-checkout-sdk:1.0.1

# Stripe (Alternative)
composer require stripe/stripe-php

# Razorpay (Alternative)
composer require razorpay/razorpay
```

#### Development Packages
```bash
# Debug Bar
composer require barryvdh/laravel-debugbar:^3.8 --dev

# Testing
composer require pestphp/pest:^3.0 --dev
composer require pestphp/pest-plugin-laravel:^3.0 --dev

# Code Quality
composer require laravel/pint:^1.19 --dev

# Faker
composer require fakerphp/faker:^1.23 --dev
```

### 4. Frontend Dependencies

#### Install Node.js Dependencies
```bash
# Core Frontend
npm install

# Vue.js 3 Setup
npm install vue@next @vitejs/plugin-vue

# UI Components
npm install @headlessui/vue @heroicons/vue

# Form Handling
npm install @vuelidate/core @vuelidate/validators

# HTTP Client
npm install axios

# Utilities
npm install lodash
npm install moment

# Development
npm install --save-dev tailwindcss postcss autoprefixer
npm install --save-dev @tailwindcss/forms @tailwindcss/typography
```

#### Initialize TailwindCSS
```bash
npx tailwindcss init -p
```

### 5. Optional Advanced Packages

#### Search & Analytics
```bash
# Elasticsearch (Advanced Search)
composer require elasticsearch/elasticsearch:^8.10

# Laravel Scout (Search)
composer require laravel/scout

# Analytics
composer require spatie/laravel-analytics
```

#### Localization & Translation
```bash
# Multi-language Support
composer require astrotomic/laravel-translatable:^11.0.0

# Arabic Language Support
composer require khaled.alshamaa/ar-php:^6.0.0
```

#### Advanced Features
```bash
# Real-time Features
composer require pusher/pusher-php-server:^7.0

# Queue Management
composer require laravel/horizon

# File Storage
composer require league/flysystem-aws-s3-v3

# Image Optimization
composer require spatie/laravel-image-optimizer

# Backup
composer require spatie/laravel-backup
```

### 6. Complete Installation Script

Create a file `install-packages.sh`:

```bash
#!/bin/bash

echo "🚀 Installing Custom Laravel E-commerce Packages..."

# Core E-commerce packages
composer require intervention/image:^2.4
composer require barryvdh/laravel-dompdf:^2.0.0
composer require maatwebsite/excel:^3.1.46
composer require laravel/sanctum:^4.0
composer require laravel/ui:^4.0
composer require kalnoy/nestedset:^6.0
composer require prettus/l5-repository:^2.6
composer require diglactic/laravel-breadcrumbs:^9.0
composer require guzzlehttp/guzzle:^7.0.1
composer require spatie/laravel-responsecache:^7.4
composer require spatie/laravel-sitemap:^7.3

# Payment
composer require paypal/paypal-checkout-sdk:1.0.1

# Optional packages
composer require laravel/socialite:^5.0
composer require shetabit/visitor:^4.1
composer require predis/predis:^2.2

# Development packages
composer require barryvdh/laravel-debugbar:^3.8 --dev
composer require pestphp/pest:^3.0 --dev
composer require pestphp/pest-plugin-laravel:^3.0 --dev
composer require laravel/pint:^1.19 --dev

echo "✅ Composer packages installed!"

# Frontend packages
npm install vue@next @vitejs/plugin-vue
npm install @headlessui/vue @heroicons/vue
npm install @vuelidate/core @vuelidate/validators
npm install axios lodash moment
npm install --save-dev tailwindcss postcss autoprefixer
npm install --save-dev @tailwindcss/forms @tailwindcss/typography

echo "✅ NPM packages installed!"

# Initialize TailwindCSS
npx tailwindcss init -p

echo "🎉 Installation complete!"
```

Make it executable and run:
```bash
chmod +x install-packages.sh
./install-packages.sh
```

### 7. Configuration Files

#### Update `config/app.php`
```php
'providers' => [
    // ... existing providers
    
    // Custom E-commerce Providers
    Intervention\Image\ImageServiceProvider::class,
    Barryvdh\DomPDF\ServiceProvider::class,
    Maatwebsite\Excel\ExcelServiceProvider::class,
    Diglactic\Breadcrumbs\ServiceProvider::class,
    Spatie\ResponseCache\ResponseCacheServiceProvider::class,
    Spatie\Sitemap\SitemapServiceProvider::class,
],

'aliases' => [
    // ... existing aliases
    
    // Custom E-commerce Aliases
    'Image' => Intervention\Image\Facades\Image::class,
    'PDF' => Barryvdh\DomPDF\Facade::class,
    'Excel' => Maatwebsite\Excel\Facades\Excel::class,
    'Breadcrumbs' => Diglactic\Breadcrumbs\Facades\Breadcrumbs::class,
],
```

#### Publish Configuration Files
```bash
# Publish config files
php artisan vendor:publish --provider="Intervention\Image\ImageServiceProviderLaravelRecent"
php artisan vendor:publish --provider="Barryvdh\DomPDF\ServiceProvider"
php artisan vendor:publish --provider="Maatwebsite\Excel\ExcelServiceProvider"
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
php artisan vendor:publish --provider="Spatie\ResponseCache\ResponseCacheServiceProvider"
php artisan vendor:publish --provider="Spatie\Sitemap\SitemapServiceProvider"

# Create symbolic link for storage
php artisan storage:link
```

### 8. Environment Configuration

Update your `.env` file:
```env
# App Configuration
APP_NAME="Custom E-commerce"
APP_ENV=local
APP_KEY=base64:your-app-key
APP_DEBUG=true
APP_URL=http://localhost

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=custom_ecommerce
DB_USERNAME=root
DB_PASSWORD=

# Cache
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_CONNECTION=sync

# Image Processing
IMAGE_DRIVER=gd

# Mail
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

# Payment Gateways
PAYPAL_CLIENT_ID=
PAYPAL_CLIENT_SECRET=
PAYPAL_MODE=sandbox

# Social Login (Optional)
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=
FACEBOOK_REDIRECT_URL=

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URL=
```

### 9. Verification Commands

After installation, verify everything is working:

```bash
# Check Laravel version
php artisan --version

# Check installed packages
composer show

# Check NPM packages
npm list

# Test image processing
php artisan tinker
>>> Image::make('path/to/image.jpg')->resize(300, 200)->save('path/to/resized.jpg');

# Test PDF generation
>>> PDF::loadHTML('<h1>Test PDF</h1>')->save('test.pdf');

# Clear caches
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear
```

### 10. Package Comparison

| Feature | Bagisto Package | Custom Laravel Package |
|---------|----------------|------------------------|
| **Image Processing** | intervention/image | intervention/image ✅ |
| **PDF Generation** | barryvdh/laravel-dompdf | barryvdh/laravel-dompdf ✅ |
| **Excel Import/Export** | maatwebsite/excel | maatwebsite/excel ✅ |
| **Authentication** | Laravel Sanctum | Laravel Sanctum ✅ |
| **Social Login** | Laravel Socialite | Laravel Socialite ✅ |
| **Nested Categories** | kalnoy/nestedset | kalnoy/nestedset ✅ |
| **Repository Pattern** | prettus/l5-repository | prettus/l5-repository ✅ |
| **HTTP Client** | guzzlehttp/guzzle | guzzlehttp/guzzle ✅ |
| **PayPal Integration** | paypal/paypal-checkout-sdk | paypal/paypal-checkout-sdk ✅ |
| **Caching** | spatie/laravel-responsecache | spatie/laravel-responsecache ✅ |
| **Sitemap** | spatie/laravel-sitemap | spatie/laravel-sitemap ✅ |

### 11. Next Steps

After installing packages:

1. **Create Database Structure**
   ```bash
   php artisan make:migration create_products_table
   php artisan make:migration create_categories_table
   php artisan make:migration create_orders_table
   ```

2. **Generate Models**
   ```bash
   php artisan make:model Product -mcr
   php artisan make:model Category -mcr
   php artisan make:model Order -mcr
   ```

3. **Set up Authentication**
   ```bash
   php artisan ui vue --auth
   php artisan migrate
   ```

4. **Configure Frontend**
   ```bash
   npm run dev
   ```

This setup gives you all the functionality of Bagisto but with a clean, custom Laravel implementation that you have full control over!
