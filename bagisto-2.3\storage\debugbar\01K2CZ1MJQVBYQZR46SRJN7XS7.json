{"__meta": {"id": "01K2CZ1MJQVBYQZR46SRJN7XS7", "datetime": "2025-08-11 21:52:33", "utime": **********.305464, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/admin/cms", "ip": "::1"}, "modules": {"count": 2, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "duration": 1.2, "duration_str": "1.2s", "connection": "Online_store"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 3.99, "duration_str": "3.99s", "connection": "Online_store"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 1.02, "duration_str": "1.02s", "connection": "Online_store"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754929350.658666, "end": **********.348955, "duration": 2.69028902053833, "duration_str": "2.69s", "measures": [{"label": "Booting", "start": 1754929350.658666, "relative_start": 0, "end": **********.573954, "relative_end": **********.573954, "duration": 0.****************, "duration_str": "915ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.573978, "relative_start": 0.****************, "end": **********.348961, "relative_end": 6.198883056640625e-06, "duration": 1.****************, "duration_str": "1.77s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.629816, "relative_start": 0.****************, "end": **********.638435, "relative_end": **********.638435, "duration": 0.008618831634521484, "duration_str": "8.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.715815, "relative_start": 1.****************, "end": **********.298432, "relative_end": **********.298432, "duration": 1.****************, "duration_str": "1.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::cms.index", "start": **********.719963, "relative_start": 1.****************, "end": **********.719963, "relative_end": **********.719963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.datagrid.export.index", "start": **********.670591, "relative_start": 2.***************, "end": **********.670591, "relative_end": **********.670591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.942829, "relative_start": 2.284162998199463, "end": **********.942829, "relative_end": **********.942829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.947059, "relative_start": 2.288393020629883, "end": **********.947059, "relative_end": **********.947059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.948956, "relative_start": 2.290290117263794, "end": **********.948956, "relative_end": **********.948956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.button.index", "start": **********.951771, "relative_start": 2.293105125427246, "end": **********.951771, "relative_end": **********.951771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.modal.index", "start": **********.953612, "relative_start": 2.2949461936950684, "end": **********.953612, "relative_end": **********.953612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.datagrid.index", "start": **********.957104, "relative_start": 2.29843807220459, "end": **********.957104, "relative_end": **********.957104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.datagrid.toolbar", "start": **********.959729, "relative_start": 2.301063060760498, "end": **********.959729, "relative_end": **********.959729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.datagrid.toolbar", "start": **********.962281, "relative_start": 2.303615093231201, "end": **********.962281, "relative_end": **********.962281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.datagrid.toolbar.mass-action", "start": **********.964164, "relative_start": 2.3054981231689453, "end": **********.964164, "relative_end": **********.964164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.966414, "relative_start": 2.3077480792999268, "end": **********.966414, "relative_end": **********.966414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.datagrid.toolbar.search", "start": **********.968621, "relative_start": 2.30995512008667, "end": **********.968621, "relative_end": **********.968621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.datagrid.toolbar.search", "start": **********.970786, "relative_start": 2.312120199203491, "end": **********.970786, "relative_end": **********.970786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.datagrid.toolbar.filter", "start": **********.972643, "relative_start": 2.313977003097534, "end": **********.972643, "relative_end": **********.972643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.datagrid.toolbar.filter", "start": **********.977896, "relative_start": 2.319230079650879, "end": **********.977896, "relative_end": **********.977896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.accordion.index", "start": **********.981646, "relative_start": 2.3229801654815674, "end": **********.981646, "relative_end": **********.981646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.accordion.index", "start": **********.984003, "relative_start": 2.3253371715545654, "end": **********.984003, "relative_end": **********.984003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.menu.item", "start": **********.986523, "relative_start": 2.32785701751709, "end": **********.986523, "relative_end": **********.986523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.988159, "relative_start": 2.3294930458068848, "end": **********.988159, "relative_end": **********.988159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flat-picker.date", "start": **********.990304, "relative_start": 2.3316380977630615, "end": **********.990304, "relative_end": **********.990304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flat-picker.date", "start": **********.991712, "relative_start": 2.3330461978912354, "end": **********.991712, "relative_end": **********.991712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flat-picker.date", "start": **********.992549, "relative_start": 2.333883047103882, "end": **********.992549, "relative_end": **********.992549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flat-picker.datetime", "start": **********.994287, "relative_start": 2.3356211185455322, "end": **********.994287, "relative_end": **********.994287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flat-picker.datetime", "start": **********.995771, "relative_start": 2.3371050357818604, "end": **********.995771, "relative_end": **********.995771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flat-picker.datetime", "start": **********.996657, "relative_start": 2.3379909992218018, "end": **********.996657, "relative_end": **********.996657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.menu.item", "start": **********.997959, "relative_start": 2.3392930030822754, "end": **********.997959, "relative_end": **********.997959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.999192, "relative_start": 2.3405261039733887, "end": **********.999192, "relative_end": **********.999192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.accordion.index", "start": **********.000802, "relative_start": 2.3421361446380615, "end": **********.000802, "relative_end": **********.000802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.accordion.index", "start": **********.00238, "relative_start": 2.3437139987945557, "end": **********.00238, "relative_end": **********.00238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.004201, "relative_start": 2.3455350399017334, "end": **********.004201, "relative_end": **********.004201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.005828, "relative_start": 2.3471620082855225, "end": **********.005828, "relative_end": **********.005828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.008639, "relative_start": 2.349973201751709, "end": **********.008639, "relative_end": **********.008639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.012865, "relative_start": 2.354199171066284, "end": **********.012865, "relative_end": **********.012865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.01543, "relative_start": 2.356764078140259, "end": **********.01543, "relative_end": **********.01543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.024175, "relative_start": 2.365509033203125, "end": **********.024175, "relative_end": **********.024175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.drawer.index", "start": **********.026388, "relative_start": 2.3677220344543457, "end": **********.026388, "relative_end": **********.026388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.datagrid.toolbar.pagination", "start": **********.030487, "relative_start": 2.371821165084839, "end": **********.030487, "relative_end": **********.030487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.datagrid.toolbar.pagination", "start": **********.033724, "relative_start": 2.375058174133301, "end": **********.033724, "relative_end": **********.033724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.menu.item", "start": **********.03567, "relative_start": 2.3770041465759277, "end": **********.03567, "relative_end": **********.03567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.036958, "relative_start": 2.3782920837402344, "end": **********.036958, "relative_end": **********.036958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.datagrid.table", "start": **********.038975, "relative_start": 2.3803091049194336, "end": **********.038975, "relative_end": **********.038975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.datagrid.table.head", "start": **********.042028, "relative_start": 2.383362054824829, "end": **********.042028, "relative_end": **********.042028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.datagrid.table.body", "start": **********.045045, "relative_start": 2.3863790035247803, "end": **********.045045, "relative_end": **********.045045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.index", "start": **********.047504, "relative_start": 2.388838052749634, "end": **********.047504, "relative_end": **********.047504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flash-group.index", "start": **********.151707, "relative_start": 2.4930410385131836, "end": **********.151707, "relative_end": **********.151707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flash-group.item", "start": **********.15378, "relative_start": 2.4951140880584717, "end": **********.15378, "relative_end": **********.15378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.modal.confirm", "start": **********.155781, "relative_start": 2.497115135192871, "end": **********.155781, "relative_end": **********.155781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.header.index", "start": **********.159257, "relative_start": 2.5005910396575928, "end": **********.159257, "relative_end": **********.159257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.173359, "relative_start": 2.514693021774292, "end": **********.173359, "relative_end": **********.173359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.17603, "relative_start": 2.517364025115967, "end": **********.17603, "relative_end": **********.17603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.drawer.index", "start": **********.240678, "relative_start": 2.582012176513672, "end": **********.240678, "relative_end": **********.240678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.products", "start": **********.243859, "relative_start": 2.585193157196045, "end": **********.243859, "relative_end": **********.243859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.orders", "start": **********.246203, "relative_start": 2.5875370502471924, "end": **********.246203, "relative_end": **********.246203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.categories", "start": **********.248146, "relative_start": 2.58948016166687, "end": **********.248146, "relative_end": **********.248146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.customers", "start": **********.250085, "relative_start": 2.591419219970703, "end": **********.250085, "relative_end": **********.250085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.252377, "relative_start": 2.5937111377716064, "end": **********.252377, "relative_end": **********.252377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.sidebar.index", "start": **********.255152, "relative_start": 2.5964860916137695, "end": **********.255152, "relative_end": **********.255152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.tabs", "start": **********.293381, "relative_start": 2.6347150802612305, "end": **********.293381, "relative_end": **********.293381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 41436008, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 59, "nb_templates": 59, "templates": [{"name": "1x admin::cms.index", "param_count": null, "params": [], "start": **********.719868, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/cms/index.blade.phpadmin::cms.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcms%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::cms.index"}, {"name": "1x admin::components.datagrid.export.index", "param_count": null, "params": [], "start": **********.670364, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/datagrid/export/index.blade.phpadmin::components.datagrid.export.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdatagrid%2Fexport%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.datagrid.export.index"}, {"name": "3x admin::components.form.control-group.control", "param_count": null, "params": [], "start": **********.942598, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/control.blade.phpadmin::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::components.form.control-group.control"}, {"name": "2x admin::components.form.control-group.index", "param_count": null, "params": [], "start": **********.946822, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/index.blade.phpadmin::components.form.control-group.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::components.form.control-group.index"}, {"name": "3x admin::components.form.index", "param_count": null, "params": [], "start": **********.948832, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/form/index.blade.phpadmin::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::components.form.index"}, {"name": "1x admin::components.button.index", "param_count": null, "params": [], "start": **********.951645, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/button/index.blade.phpadmin::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.button.index"}, {"name": "1x admin::components.modal.index", "param_count": null, "params": [], "start": **********.953487, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/modal/index.blade.phpadmin::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.modal.index"}, {"name": "1x admin::components.datagrid.index", "param_count": null, "params": [], "start": **********.956924, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/datagrid/index.blade.phpadmin::components.datagrid.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdatagrid%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.datagrid.index"}, {"name": "1x admin::components.datagrid.toolbar", "param_count": null, "params": [], "start": **********.959603, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/datagrid/toolbar.blade.phpadmin::components.datagrid.toolbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdatagrid%2Ftoolbar.blade.php&line=1", "ajax": false, "filename": "toolbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.datagrid.toolbar"}, {"name": "1x admin::components.shimmer.datagrid.toolbar", "param_count": null, "params": [], "start": **********.962152, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/datagrid/toolbar.blade.phpadmin::components.shimmer.datagrid.toolbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdatagrid%2Ftoolbar.blade.php&line=1", "ajax": false, "filename": "toolbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.datagrid.toolbar"}, {"name": "1x admin::components.datagrid.toolbar.mass-action", "param_count": null, "params": [], "start": **********.964032, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/datagrid/toolbar/mass-action.blade.phpadmin::components.datagrid.toolbar.mass-action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdatagrid%2Ftoolbar%2Fmass-action.blade.php&line=1", "ajax": false, "filename": "mass-action.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.datagrid.toolbar.mass-action"}, {"name": "6x admin::components.dropdown.index", "param_count": null, "params": [], "start": **********.966288, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/dropdown/index.blade.phpadmin::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "admin::components.dropdown.index"}, {"name": "1x admin::components.datagrid.toolbar.search", "param_count": null, "params": [], "start": **********.968498, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/datagrid/toolbar/search.blade.phpadmin::components.datagrid.toolbar.search", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdatagrid%2Ftoolbar%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.datagrid.toolbar.search"}, {"name": "1x admin::components.shimmer.datagrid.toolbar.search", "param_count": null, "params": [], "start": **********.970661, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/datagrid/toolbar/search.blade.phpadmin::components.shimmer.datagrid.toolbar.search", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdatagrid%2Ftoolbar%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.datagrid.toolbar.search"}, {"name": "1x admin::components.datagrid.toolbar.filter", "param_count": null, "params": [], "start": **********.972514, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/datagrid/toolbar/filter.blade.phpadmin::components.datagrid.toolbar.filter", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdatagrid%2Ftoolbar%2Ffilter.blade.php&line=1", "ajax": false, "filename": "filter.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.datagrid.toolbar.filter"}, {"name": "1x admin::components.shimmer.datagrid.toolbar.filter", "param_count": null, "params": [], "start": **********.977661, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/datagrid/toolbar/filter.blade.phpadmin::components.shimmer.datagrid.toolbar.filter", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdatagrid%2Ftoolbar%2Ffilter.blade.php&line=1", "ajax": false, "filename": "filter.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.datagrid.toolbar.filter"}, {"name": "2x admin::components.accordion.index", "param_count": null, "params": [], "start": **********.981518, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/accordion/index.blade.phpadmin::components.accordion.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Faccordion%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::components.accordion.index"}, {"name": "2x admin::components.shimmer.accordion.index", "param_count": null, "params": [], "start": **********.983875, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/accordion/index.blade.phpadmin::components.shimmer.accordion.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Faccordion%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::components.shimmer.accordion.index"}, {"name": "3x admin::components.dropdown.menu.item", "param_count": null, "params": [], "start": **********.986378, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/dropdown/menu/item.blade.phpadmin::components.dropdown.menu.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::components.dropdown.menu.item"}, {"name": "3x admin::components.flat-picker.date", "param_count": null, "params": [], "start": **********.990178, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/flat-picker/date.blade.phpadmin::components.flat-picker.date", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflat-picker%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::components.flat-picker.date"}, {"name": "3x admin::components.flat-picker.datetime", "param_count": null, "params": [], "start": **********.994114, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/flat-picker/datetime.blade.phpadmin::components.flat-picker.datetime", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflat-picker%2Fdatetime.blade.php&line=1", "ajax": false, "filename": "datetime.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::components.flat-picker.datetime"}, {"name": "1x admin::components.form.control-group.label", "param_count": null, "params": [], "start": **********.004074, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/label.blade.phpadmin::components.form.control-group.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.form.control-group.label"}, {"name": "1x admin::components.form.control-group.error", "param_count": null, "params": [], "start": **********.012652, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/error.blade.phpadmin::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.form.control-group.error"}, {"name": "2x admin::components.drawer.index", "param_count": null, "params": [], "start": **********.026221, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/drawer/index.blade.phpadmin::components.drawer.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdrawer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::components.drawer.index"}, {"name": "1x admin::components.datagrid.toolbar.pagination", "param_count": null, "params": [], "start": **********.030274, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/datagrid/toolbar/pagination.blade.phpadmin::components.datagrid.toolbar.pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdatagrid%2Ftoolbar%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.datagrid.toolbar.pagination"}, {"name": "1x admin::components.shimmer.datagrid.toolbar.pagination", "param_count": null, "params": [], "start": **********.033507, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/datagrid/toolbar/pagination.blade.phpadmin::components.shimmer.datagrid.toolbar.pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdatagrid%2Ftoolbar%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.datagrid.toolbar.pagination"}, {"name": "1x admin::components.datagrid.table", "param_count": null, "params": [], "start": **********.03885, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/datagrid/table.blade.phpadmin::components.datagrid.table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdatagrid%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.datagrid.table"}, {"name": "1x admin::components.shimmer.datagrid.table.head", "param_count": null, "params": [], "start": **********.041827, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/datagrid/table/head.blade.phpadmin::components.shimmer.datagrid.table.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdatagrid%2Ftable%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.datagrid.table.head"}, {"name": "1x admin::components.shimmer.datagrid.table.body", "param_count": null, "params": [], "start": **********.044844, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/datagrid/table/body.blade.phpadmin::components.shimmer.datagrid.table.body", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdatagrid%2Ftable%2Fbody.blade.php&line=1", "ajax": false, "filename": "body.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.datagrid.table.body"}, {"name": "1x admin::components.layouts.index", "param_count": null, "params": [], "start": **********.04737, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/index.blade.phpadmin::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.layouts.index"}, {"name": "1x admin::components.flash-group.index", "param_count": null, "params": [], "start": **********.151573, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/flash-group/index.blade.phpadmin::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.flash-group.index"}, {"name": "1x admin::components.flash-group.item", "param_count": null, "params": [], "start": **********.153652, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/flash-group/item.blade.phpadmin::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.flash-group.item"}, {"name": "1x admin::components.modal.confirm", "param_count": null, "params": [], "start": **********.155649, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/modal/confirm.blade.phpadmin::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.modal.confirm"}, {"name": "1x admin::components.layouts.header.index", "param_count": null, "params": [], "start": **********.158969, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/header/index.blade.phpadmin::components.layouts.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.layouts.header.index"}, {"name": "1x admin::components.shimmer.header.mega-search.products", "param_count": null, "params": [], "start": **********.243724, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/products.blade.phpadmin::components.shimmer.header.mega-search.products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fproducts.blade.php&line=1", "ajax": false, "filename": "products.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.header.mega-search.products"}, {"name": "1x admin::components.shimmer.header.mega-search.orders", "param_count": null, "params": [], "start": **********.246078, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/orders.blade.phpadmin::components.shimmer.header.mega-search.orders", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Forders.blade.php&line=1", "ajax": false, "filename": "orders.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.header.mega-search.orders"}, {"name": "1x admin::components.shimmer.header.mega-search.categories", "param_count": null, "params": [], "start": **********.248024, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/categories.blade.phpadmin::components.shimmer.header.mega-search.categories", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fcategories.blade.php&line=1", "ajax": false, "filename": "categories.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.header.mega-search.categories"}, {"name": "1x admin::components.shimmer.header.mega-search.customers", "param_count": null, "params": [], "start": **********.249958, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/customers.blade.phpadmin::components.shimmer.header.mega-search.customers", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fcustomers.blade.php&line=1", "ajax": false, "filename": "customers.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.header.mega-search.customers"}, {"name": "1x admin::components.layouts.sidebar.index", "param_count": null, "params": [], "start": **********.255026, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/sidebar/index.blade.phpadmin::components.layouts.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.layouts.sidebar.index"}, {"name": "1x admin::components.layouts.tabs", "param_count": null, "params": [], "start": **********.293249, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/tabs.blade.phpadmin::components.layouts.tabs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Ftabs.blade.php&line=1", "ajax": false, "filename": "tabs.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.layouts.tabs"}]}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00621, "accumulated_duration_str": "6.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.6900132, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 64.251}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 109}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.70554, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "Online_store", "explain": null, "start_percent": 64.251, "width_percent": 16.425}, {"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "type": "query", "params": [], "bindings": ["localhost", "http://localhost", "https://localhost"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 169}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 246}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 218}], "start": **********.115493, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "Online_store", "explain": null, "start_percent": 80.676, "width_percent": 19.324}]}, "models": {"data": {"Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/cms", "action_name": "admin.cms.index", "controller_action": "Webkul\\Admin\\Http\\Controllers\\CMS\\PageController@index", "uri": "GET admin/cms", "controller": "Webkul\\Admin\\Http\\Controllers\\CMS\\PageController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCMS%2FPageController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/cms", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCMS%2FPageController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/CMS/PageController.php:26-33</a>", "middleware": "web, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "2.69s", "peak_memory": "42MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-419419349 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-419419349\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1124687769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1124687769\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-813792369 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/settings/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1090 characters\">bagisto_session=eyJpdiI6ImpDWlNrdnV1KzVRcHJsU1NOQ1RaRHc9PSIsInZhbHVlIjoiNit6V3FHZEdhMzRvcGk4WnFBZUlOK09WTVZxNm5NbElac2JoaE5hV1krZGFOeExFMVpLZVcyMVdJOUFkdk0rRVZZczB3bkF0R0hxRzVjdnMrUm1KU3pkNmlHeGJYT2JuNXVyblByaUg1Qlozc3BmaEFzMkZEZmx4VzBTT3lydnEiLCJtYWMiOiIyYzI4MGJhZTYyZDJkN2I1MDdjMGE4YjIxM2M1ZGJiOTFiYWY3OGYxNGU5ZGIyZTg4ODlkN2U5NTY4ZGIwYTg4IiwidGFnIjoiIn0%3D; dark_mode=0; XSRF-TOKEN=eyJpdiI6InFyRWoxaGVGNmNDOXc4Z3ZnYmFLUEE9PSIsInZhbHVlIjoidFRyVHJLKzdRaVVteERjR1JnaUVUZGJoTkZyQm5JSVpBUzI2UkN3YXhXMU0rL1llanV6M2RDSHVrQ0NDSU9mQ3M2NDNrakRQQ1ZhdkowOFA4TnhEaEY0ejJHY0NJd1g5cDZZN25HSVI2Q3dLTFVPRlkxME5mT0NQNkxkZTBOajUiLCJtYWMiOiJkODUwNGRiNDFlYzU5NWVjNmM4ZjU3YjA1YzY2MGY1YmYwOWE1OWZiMjA5NDJkOGZkYmNmMTA0YmI0ZTQzNjdhIiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6IlA3M1NQWC9PYVJGNFFOV2w2ZmZZeEE9PSIsInZhbHVlIjoiUXFwQ0RhcGU3MXAyNXlibFZXZzFSZXkvaGkybldEQjNwZ2lqenhNZHBHaEJvaWkrTzFwWUpoOGZyRFZJVmhNdkxZK3ZjMWNwcEV4Y0tXbjhyOEp1b0hhZGs1K3JMMmUzbC9qdExzZlNjZDFxVWJGcWdEbWxwT2JtcTJjbjR6ZDQiLCJtYWMiOiIyMDQ5NGNmZDEwMTkwZmVmOTBkN2YxM2RkYjRlYjlhZmYzMWYxYTVlNWY0M2ViMzc5ZTE1ZDBlNWVkYWYzNjYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813792369\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-733993770 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bagisto_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TMRz3GXEM8sAcFV1fikDiVtK46lGff3YWRNL67O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733993770\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-364447691 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 16:22:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364447691\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1827724321 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"68 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/settings/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827724321\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/cms", "action_name": "admin.cms.index", "controller_action": "Webkul\\Admin\\Http\\Controllers\\CMS\\PageController@index"}, "badge": null}}