[[getting-started-php]]
== Getting started

This page guides you through the installation process of the PHP client, shows 
you how to instantiate the client, and how to perform basic Elasticsearch 
operations with it.

[discrete]
=== Requirements

* http://getcomposer.org[composer]

If you don't have composer you can install it by running the following commands:

[source,shell]
--------------------------
curl -s http://getcomposer.org/installer | php
php composer.phar install
--------------------------


[discrete]
=== Installation 

To install the latest version of the client, run the following command:

[source,shell]
--------------------------
composer require elasticsearch/elasticsearch
--------------------------

When you have installed elasticsearch-php you can start using it with the 
`Client` class. You can use the `ClientBuilder` class to create this object:

[source,php]
--------------------------
require 'vendor/autoload.php';

$client = Elastic\Elasticsearch\ClientBuilder::create()->build();
--------------------------

Refer to the <<installation>> page to learn more.


[discrete]
=== Connecting

You can connect to the Elastic Cloud using an API key and the Elasticsearch 
endpoint. 

[source,php]
----
$client = ClientBuilder::create()
   ->setHosts(['<elasticsearch-endpoint>'])
   ->setApiKey('<api-key>')
   ->build();
----

Your Elasticsearch endpoint can be found on the **My deployment** page of your 
deployment:

image::images/es_endpoint.jpg[alt="Finding Elasticsearch endpoint",align="center"]

You can generate an API key on the **Management** page under Security.

image::images/create_api_key.png[alt="Create API key",align="center"]

For other connection options, refer to the <<connecting>> section.


[discrete]
=== Operations

Time to use Elasticsearch! This section walks you through the basic, and most 
important, operations of Elasticsearch. For more operations and more advanced 
examples, refer to the <<operations>> page.


[discrete]
==== Creating an index

This is how you create the `my_index` index:

[source,php]
----
$client = ClientBuilder::create()->build();
$params = [
    'index' => 'my_index'
];

// Create the index
$response = $client->indices()->create($params);
----


[discrete]
==== Indexing documents

This is a simple way of indexing a document:

[source,php]
----
$params = [
    'index' => 'my_index',
    'body'  => [ 'testField' => 'abc']
];

// Document will be indexed to my_index/_doc/<autogenerated ID>
$response = $client->index($params);
----

You can bulk index documents with batches in a slightly more complex way:

.Bulk indexing with batches
[source,php]
----
$params = ['body' => []];

for ($i = 1; $i <= 1234567; $i++) {
    $params['body'][] = [
        'index' => [
            '_index' => 'my_index',
            '_id'    => $i
        ]
    ];

    $params['body'][] = [
        'my_field'     => 'my_value',
        'second_field' => 'some more values'
    ];

    // Every 1000 documents stop and send the bulk request
    if ($i % 1000 == 0) {
        $responses = $client->bulk($params);

        // erase the old bulk request
        $params = ['body' => []];

        // unset the bulk response when you are done to save memory
        unset($responses);
    }
}

// Send the last batch if it exists
if (!empty($params['body'])) {
    $responses = $client->bulk($params);
}
----


[discrete]
==== Getting documents

You can get documents by using the following code:

[source,php]
----
$params = [
    'index' => 'my_index',
    'id'    => 'my_id'
];

// Get doc at /my_index/_doc/my_id
$response = $client->get($params);
----


[discrete]
==== Searching documents

This is how you can create a single match query with the PHP client: 

[source,php]
----
$params = [
    'index' => 'my_index',
    'body'  => [
        'query' => [
            'match' => [
                'testField' => 'abc'
            ]
        ]
    ]
];

$results = $client->search($params);
----


[discrete]
==== Updating documents

This is how you can update a document, for example to add a new field:

[source,php]
----
$params = [
    'index' => 'my_index',
    'id'    => 'my_id',
    'body'  => [
        'doc' => [
            'new_field' => 'abc'
        ]
    ]
];

// Update doc at /my_index/_doc/my_id
$response = $client->update($params);
----


[discrete]
==== Deleting documents

[source,php]
----
$params = [
    'index' => 'my_index',
    'id'    => 'my_id'
];

// Delete doc at /my_index/_doc_/my_id
$response = $client->delete($params);
----


[discrete]
==== Deleting an index

[source,php]
----
$params = ['index' => 'my_index'];
$response = $client->indices()->delete($params);
----


[discrete]
== Further reading

* Use <<client-helpers>> for a more confortable experience with the APIs.