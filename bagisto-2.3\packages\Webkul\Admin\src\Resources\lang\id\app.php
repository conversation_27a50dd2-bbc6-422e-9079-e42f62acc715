<?php

return [
    'users' => [
        'sessions' => [
            'email'                  => '<PERSON><PERSON><PERSON>ail',
            'forget-password-link'   => 'Lupa Kata Sandi?',
            'password'               => 'Kata Sandi',
            'powered-by-description' => 'Dibuat oleh :bagisto, proyek open-source dari :webkul.',
            'submit-btn'             => 'Masuk',
            'title'                  => 'Masuk',
        ],

        'forget-password' => [
            'create' => [
                'email'                  => 'Email Terdaftar',
                'email-not-exist'        => 'Email Tidak Ditemukan',
                'page-title'             => 'Lupa Kata Sandi',
                'powered-by-description' => 'Dibuat oleh :bagisto, proyek open-source dari :webkul.',
                'reset-link-sent'        => 'Tautan Reset Kata Sandi telah dikirim',
                'sign-in-link'           => 'Kemba<PERSON> ke Halaman Masuk?',
                'submit-btn'             => 'Atur Ulang',
                'title'                  => '<PERSON><PERSON>h<PERSON> Kata Sandi',
            ],
        ],

        'reset-password' => [
            'back-link-title'        => '<PERSON><PERSON><PERSON> ke Halaman Masuk?',
            'confirm-password'       => 'Konfirmasi Kata Sandi',
            'email'                  => 'Email Terdaftar',
            'password'               => 'Kata Sandi',
            'powered-by-description' => 'Dibuat oleh :bagisto, proyek open-source dari :webkul.',
            'submit-btn'             => 'Atur Ulang Kata Sandi',
            'title'                  => 'Atur Ulang Kata Sandi',
        ],
    ],

    'notifications' => [
        'description-text'      => 'Daftar semua Notifikasi',
        'marked-success'        => 'Semua notifikasi telah ditandai sebagai dibaca',
        'no-record'             => 'Tidak Ada Data Ditemukan',
        'of'                    => 'dari',
        'per-page'              => 'Per Halaman',
        'read-all'              => 'Tandai sebagai Dibaca',
        'title'                 => 'Notifikasi',
        'view-all'              => 'Lihat Semua',

        'order-status-messages' => [
            'all'             => 'Semua',
            'canceled'        => 'Pesanan Dibatalkan',
            'closed'          => 'Pesanan Ditutup',
            'completed'       => 'Pesanan Selesai',
            'pending'         => 'Pesanan Tertunda',
            'pending-payment' => 'Menunggu Pembayaran',
            'processing'      => 'Pesanan Sedang Diproses',
        ],
    ],

    'account' => [
        'edit' => [
            'back-btn'          => 'Kembali',
            'change-password'   => 'Ubah Kata Sandi',
            'confirm-password'  => 'Konfirmasi Kata Sandi',
            'current-password'  => 'Kata Sandi Saat Ini',
            'email'             => 'Email',
            'general'           => 'Umum',
            'invalid-password'  => 'Kata sandi saat ini yang Anda masukkan tidak benar.',
            'name'              => 'Nama',
            'password'          => 'Kata Sandi',
            'profile-image'     => 'Gambar Profil',
            'save-btn'          => 'Simpan Akun',
            'title'             => 'Akun Saya',
            'update-success'    => 'Akun berhasil diperbarui',
            'upload-image-info' => 'Unggah Gambar Profil (110px X 110px) dalam format PNG atau JPG',
        ],
    ],

    'dashboard' => [
        'index' => [
            'add-customer'                => 'Tambah Pelanggan',
            'add-product'                 => 'Tambah Produk',
            'all-channels'                => 'Semua Saluran',
            'attribute-code'              => 'Kode Atribut',
            'average-sale'                => 'Rata-rata Penjualan Pesanan',
            'color'                       => 'Warna',
            'customer-info'               => 'Tidak Ada Pelanggan dengan Penjualan Terbanyak',
            'customer-with-most-sales'    => 'Pelanggan dengan Penjualan Terbanyak',
            'date-duration'               => ':start - :end',
            'decreased'                   => 'Turun :progress%',
            'empty-threshold'             => 'Batas Kosong',
            'empty-threshold-description' => 'Tidak ada produk yang tersedia',
            'end-date'                    => 'Tanggal Akhir',
            'from'                        => 'Dari',
            'increased'                   => 'Naik :progress%',
            'more-products'               => ':product_count+ Gambar Lainnya',
            'order'                       => ':total_orders Pesanan',
            'order-count'                 => ':count Pesanan',
            'order-id'                    => '#:id',
            'overall-details'             => 'Detail Keseluruhan',
            'pay-by'                      => 'Bayar Dengan - :method',
            'product-count'               => ':count Produk',
            'product-image'               => 'Gambar Produk',
            'product-info'                => 'Tambahkan produk terkait dengan cepat.',
            'product-number'              => 'Produk - :product_number',
            'revenue'                     => 'Pendapatan :total',
            'sale-count'                  => ':count Penjualan',
            'sales'                       => 'Penjualan',
            'sku'                         => 'SKU - :sku',
            'start-date'                  => 'Tanggal Mulai',
            'stock-threshold'             => 'Batas Stok',
            'store-stats'                 => 'Statistik Toko',
            'title'                       => 'Dasbor',
            'to'                          => 'Ke',
            'today-customers'             => 'Pelanggan Hari Ini',
            'today-details'               => 'Detail Hari Ini',
            'today-orders'                => 'Pesanan Hari Ini',
            'today-sales'                 => 'Penjualan Hari Ini',
            'top-performing-categories'   => 'Kategori Terlaris',
            'top-selling-products'        => 'Produk Terlaris',
            'total-customers'             => 'Total Pelanggan',
            'total-orders'                => 'Total Pesanan',
            'total-sales'                 => 'Total Penjualan',
            'total-stock'                 => ':total_stock Stok',
            'total-unpaid-invoices'       => 'Total Faktur Belum Dibayar',
            'unique-visitors'             => ':count Pengunjung Unik',
            'user-info'                   => 'Tinjau dengan cepat apa yang terjadi di toko Anda',
            'user-name'                   => 'Hai! :user_name',
            'visitors'                    => 'Pengunjung',
        ],
    ],

    'sales' => [
        'orders' => [
            'index' => [
                'create-btn' => 'Buat Pesanan',
                'title'      => 'Pesanan',

                'search-customer' => [
                    'create-btn'  => 'Buat Pelanggan',
                    'empty-info'  => 'Tidak ada pelanggan yang tersedia untuk pencarian.',
                    'empty-title' => 'Pelanggan tidak ditemukan',
                    'search-by'   => 'Cari berdasarkan email atau nama',
                    'title'       => 'Pilih Pelanggan',
                ],

                'datagrid' => [
                    'canceled'        => 'Dibatalkan',
                    'channel-name'    => 'Saluran',
                    'closed'          => 'Ditutup',
                    'completed'       => 'Selesai',
                    'customer'        => 'Pelanggan',
                    'date'            => 'Tanggal',
                    'email'           => 'Email',
                    'fraud'           => 'Penipuan',
                    'grand-total'     => 'Total Keseluruhan',
                    'id'              => '#:id',
                    'items'           => 'Item',
                    'location'        => 'Lokasi',
                    'order-id'        => 'ID Pesanan',
                    'pay-by'          => 'Bayar Dengan - :method',
                    'pay-via'         => 'Bayar Melalui',
                    'pending-payment' => 'Pembayaran Tertunda',
                    'pending'         => 'Menunggu',
                    'processing'      => 'Sedang Diproses',
                    'product-count'   => ':count + Produk Lainnya',
                    'status'          => 'Status',
                    'success'         => 'Berhasil',
                    'view'            => 'Lihat',
                ],
            ],

            'create' => [
                'add-to-cart'             => 'Tambah ke Keranjang',
                'back-btn'                => 'Kembali',
                'check-billing-address'   => 'Alamat penagihan belum diisi.',
                'check-shipping-address'  => 'Alamat pengiriman belum diisi.',
                'configuration'           => 'Konfigurasi',
                'coupon-already-applied'  => 'Kode kupon sudah digunakan.',
                'coupon-applied'          => 'Kode kupon berhasil diterapkan.',
                'coupon-error'            => 'Kode kupon tidak dapat diterapkan.',
                'coupon-not-found'        => 'Kode kupon tidak ditemukan',
                'coupon-remove'           => 'Kode kupon berhasil dihapus.',
                'error'                   => 'Terjadi kesalahan',
                'minimum-order-error'     => 'Jumlah minimum pesanan belum terpenuhi.',
                'order-placed-success'    => 'Pesanan berhasil dibuat.',
                'payment-not-supported'   => 'Metode pembayaran ini tidak didukung.',
                'save-btn'                => 'Buat Pesanan',
                'specify-payment-method'  => 'Metode pembayaran belum diisi.',
                'specify-shipping-method' => 'Metode pengiriman belum diisi.',
                'title'                   => 'Buat Pesanan untuk :name',

                'types' => [
                    'simple' => [
                        'none'         => 'Tidak Ada',
                        'total-amount' => 'Jumlah Total',
                    ],

                    'configurable' => [
                        'select-options' => 'Silakan pilih opsi',
                    ],

                    'bundle' => [
                        'none'         => 'Tidak Ada',
                        'total-amount' => 'Jumlah Total',
                    ],

                    'grouped' => [
                        'name' => 'Nama',
                    ],

                    'downloadable' => [
                        'title' => 'Tautan',
                    ],

                    'virtual' => [
                        'none'         => 'Tidak Ada',
                        'total-amount' => 'Jumlah Total',
                    ],
                ],

                'cart' => [
                    'success-add-to-cart' => 'Produk berhasil ditambahkan ke keranjang',
                    'success-remove'      => 'Item berhasil dihapus dari keranjang',
                    'success-update'      => 'Item di keranjang berhasil diperbarui',

                    'items' => [
                        'add-product'       => 'Tambah Produk',
                        'amount-per-unit'   => ':amount Per Unit x :qty Jumlah',
                        'delete'            => 'Hapus',
                        'empty-description' => 'Tidak ada item di keranjang Anda.',
                        'empty-title'       => 'Keranjang Kosong',
                        'excl-tax'          => 'Tidak Termasuk Pajak',
                        'move-to-wishlist'  => 'Pindahkan ke Wishlist',
                        'see-details'       => 'Lihat Detail',
                        'sku'               => 'SKU - :sku',
                        'sub-total'         => 'Sub Total - :sub_total',
                        'title'             => 'Item Keranjang',

                        'search' => [
                            'add-to-cart'   => 'Tambah ke Keranjang',
                            'available-qty' => ':qty Tersedia',
                            'empty-info'    => 'Tidak ada produk yang sesuai dengan pencarian.',
                            'empty-title'   => 'Produk Tidak Ditemukan',
                            'product-image' => 'Gambar Produk',
                            'qty'           => 'Jumlah',
                            'sku'           => 'SKU - :sku',
                            'title'         => 'Cari Produk',
                        ],
                    ],

                    'address' => [
                        'add-btn'          => 'Tambah Alamat',
                        'add-new'          => 'Tambah alamat baru',
                        'add-new-address'  => 'Tambah alamat baru',
                        'addresses'        => 'Daftar Alamat',
                        'back'             => 'Kembali',
                        'billing-address'  => 'Alamat Penagihan',
                        'city'             => 'Kota',
                        'company-name'     => 'Nama Perusahaan',
                        'confirm'          => 'Konfirmasi',
                        'country'          => 'Negara',
                        'edit-btn'         => 'Edit Alamat',
                        'email'            => 'Email',
                        'first-name'       => 'Nama Depan',
                        'last-name'        => 'Nama Belakang',
                        'postcode'         => 'Kode Pos',
                        'proceed'          => 'Lanjutkan',
                        'same-as-billing'  => 'Gunakan alamat yang sama untuk pengiriman?',
                        'save'             => 'Simpan',
                        'save-address'     => 'Simpan ke buku alamat',
                        'select-country'   => 'Pilih Negara',
                        'select-state'     => 'Pilih Provinsi',
                        'shipping-address' => 'Alamat Pengiriman',
                        'state'            => 'Provinsi',
                        'street-address'   => 'Alamat Jalan',
                        'telephone'        => 'Telepon',
                        'title'            => 'Alamat',
                        'vat-id'           => 'ID Pajak',
                    ],

                    'payment' => [
                        'title' => 'Pembayaran',
                    ],

                    'shipping' => [
                        'title' => 'Pengiriman',
                    ],

                    'summary' => [
                        'apply-coupon'             => 'Gunakan Kupon',
                        'discount-amount'          => 'Jumlah Diskon',
                        'enter-your-code'          => 'Masukkan kode Anda',
                        'grand-total'              => 'Total Keseluruhan',
                        'place-order'              => 'Buat Pesanan',
                        'processing'               => 'Sedang Diproses',
                        'shipping-amount-excl-tax' => 'Biaya Pengiriman (Tidak Termasuk Pajak)',
                        'shipping-amount-incl-tax' => 'Biaya Pengiriman (Termasuk Pajak)',
                        'shipping-amount'          => 'Biaya Pengiriman',
                        'sub-total-excl-tax'       => 'Subtotal (Tidak Termasuk Pajak)',
                        'sub-total-incl-tax'       => 'Subtotal (Termasuk Pajak)',
                        'sub-total'                => 'Subtotal',
                        'tax'                      => 'Pajak',
                        'title'                    => 'Ringkasan Pesanan',
                    ],
                ],

                'cart-items' => [
                    'add-to-cart'       => 'Tambah ke Keranjang',
                    'delete'            => 'Hapus',
                    'empty-description' => 'Tidak ada item di keranjang Anda.',
                    'empty-title'       => 'Keranjang Kosong',
                    'excl-tax'          => 'Belum Termasuk Pajak: ',
                    'see-details'       => 'Lihat Detail',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Item di Keranjang',
                ],

                'recent-order-items' => [
                    'add-to-cart'       => 'Tambah ke Keranjang',
                    'empty-description' => 'Tidak ada item dari pesanan terbaru Anda.',
                    'empty-title'       => 'Belum Ada Pesanan',
                    'see-details'       => 'Lihat Detail',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Item dari Pesanan Terbaru',
                    'view'              => 'Lihat',
                ],

                'wishlist-items' => [
                    'add-to-cart'       => 'Tambah ke Keranjang',
                    'delete'            => 'Hapus',
                    'empty-description' => 'Tidak ada item di daftar keinginan Anda.',
                    'empty-title'       => 'Daftar Keinginan Kosong',
                    'see-details'       => 'Lihat Detail',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Item di Wishlist',
                ],

                'compare-items' => [
                    'add-to-cart'       => 'Tambah ke Keranjang',
                    'delete'            => 'Hapus',
                    'empty-description' => 'Tidak ada item di daftar perbandingan Anda.',
                    'empty-title'       => 'Daftar Perbandingan Kosong',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Item untuk Dibandingkan',
                ],
            ],

            'view' => [
                'amount-per-unit'                => ':amount per unit x :qty jumlah',
                'billing-address'                => 'Alamat Penagihan',
                'cancel-msg'                     => 'Apakah Anda yakin ingin membatalkan pesanan ini?',
                'cancel-success'                 => 'Pesanan berhasil dibatalkan',
                'cancel'                         => 'Batalkan',
                'canceled'                       => 'Dibatalkan',
                'channel'                        => 'Saluran',
                'closed'                         => 'Tertutup',
                'comment-success'                => 'Komentar berhasil ditambahkan.',
                'comments'                       => 'Komentar',
                'completed'                      => 'Selesai',
                'contact'                        => 'Kontak',
                'create-success'                 => 'Pesanan berhasil dibuat',
                'currency'                       => 'Mata Uang',
                'customer-group'                 => 'Grup Pelanggan',
                'customer-not-notified'          => ':date | Pelanggan <b>Belum Diberitahu</b>',
                'customer-notified'              => ':date | Pelanggan <b>Sudah Diberitahu</b>',
                'customer'                       => 'Pelanggan',
                'discount'                       => 'Diskon - :discount',
                'download-pdf'                   => 'Unduh PDF',
                'fraud'                          => 'Penipuan',
                'grand-total'                    => 'Total Keseluruhan - :grand_total',
                'invoice-id'                     => 'Faktur #:invoice',
                'invoices'                       => 'Faktur',
                'item-canceled'                  => 'Dibatalkan (:qty_canceled)',
                'item-invoice'                   => 'Ditagihkan (:qty_invoiced)',
                'item-ordered'                   => 'Dipesan (:qty_ordered)',
                'item-refunded'                  => 'Dikembalikan (:qty_refunded)',
                'item-shipped'                   => 'Dikirim (:qty_shipped)',
                'name'                           => 'Nama',
                'no-invoice-found'               => 'Tidak ada faktur ditemukan',
                'no-refund-found'                => 'Tidak ada pengembalian ditemukan',
                'no-shipment-found'              => 'Tidak ada pengiriman ditemukan',
                'notify-customer'                => 'Beritahu Pelanggan',
                'order-date'                     => 'Tanggal Pesanan',
                'order-information'              => 'Informasi Pesanan',
                'order-status'                   => 'Status Pesanan',
                'payment-and-shipping'           => 'Pembayaran dan Pengiriman',
                'payment-method'                 => 'Metode Pembayaran',
                'pending_payment'                => 'Menunggu Pembayaran',
                'pending'                        => 'Menunggu',
                'per-unit'                       => 'Per Unit',
                'price-incl-tax'                 => 'Harga (Termasuk Pajak) - :price',
                'price-excl-tax'                 => 'Harga (Tidak Termasuk Pajak) - :price',
                'price'                          => 'Harga - :price',
                'processing'                     => 'Diproses',
                'quantity'                       => 'Jumlah',
                'refund-id'                      => 'Pengembalian #:refund',
                'refund'                         => 'Pengembalian Dana',
                'refunded'                       => 'Dikembalikan',
                'reorder'                        => 'Pesan Lagi',
                'ship'                           => 'Kirim',
                'shipment'                       => 'Pengiriman #:shipment',
                'shipments'                      => 'Pengiriman',
                'shipping-address'               => 'Alamat Pengiriman',
                'shipping-and-handling-incl-tax' => 'Pengiriman dan Penanganan (Termasuk Pajak)',
                'shipping-and-handling-excl-tax' => 'Pengiriman dan Penanganan (Tidak Termasuk Pajak)',
                'shipping-and-handling'          => 'Pengiriman dan Penanganan',
                'shipping-method'                => 'Metode Pengiriman',
                'shipping-price'                 => 'Biaya Pengiriman',
                'sku'                            => 'SKU - :sku',
                'status'                         => 'Status',
                'sub-total-incl-tax'             => 'Subtotal (Termasuk Pajak) - :sub_total',
                'sub-total-excl-tax'             => 'Subtotal (Tidak Termasuk Pajak) - :sub_total',
                'sub-total'                      => 'Subtotal - :sub_total',
                'submit-comment'                 => 'Kirim Komentar',
                'summary-grand-total'            => 'Total Keseluruhan',
                'summary-sub-total-incl-tax'     => 'Subtotal (Termasuk Pajak)',
                'summary-sub-total-excl-tax'     => 'Subtotal (Tidak Termasuk Pajak)',
                'summary-sub-total'              => 'Subtotal',
                'summary-discount'               => 'Diskon',
                'summary-tax'                    => 'Pajak',
                'tax'                            => 'Pajak (:percent) - :tax',
                'title'                          => 'Pesanan #:order_id',
                'total-due'                      => 'Total yang Harus Dibayar',
                'total-paid'                     => 'Total Dibayar',
                'total-refund'                   => 'Total Pengembalian',
                'view'                           => 'Lihat',
                'write-your-comment'             => 'Tulis komentar Anda',
            ],
        ],

        'shipments' => [
            'index' => [
                'title' => 'Pengiriman',

                'datagrid' => [
                    'id'               => 'ID',
                    'inventory-source' => 'Sumber Stok',
                    'order-date'       => 'Tanggal Pesanan',
                    'order-id'         => 'ID Pesanan',
                    'shipment-date'    => 'Tanggal Pengiriman',
                    'shipment-to'      => 'Dikirim ke',
                    'total-qty'        => 'Total Jumlah',
                    'view'             => 'Lihat',
                ],
            ],

            'create' => [
                'amount-per-unit'  => ':amount per unit x :qty jumlah',
                'cancel-error'     => 'Pesanan tidak dapat dibatalkan',
                'carrier-name'     => 'Nama Kurir',
                'create-btn'       => 'Buat Pengiriman',
                'creation-error'   => 'Terjadi kesalahan saat membuat pengiriman',
                'item-canceled'    => 'Dibatalkan (:qty_canceled)',
                'item-invoice'     => 'Ditagihkan (:qty_invoiced)',
                'item-ordered'     => 'Dipesan (:qty_ordered)',
                'item-refunded'    => 'Dikembalikan (:qty_refunded)',
                'item-shipped'     => 'Dikirim (:qty_shipped)',
                'order-error'      => 'Pengiriman tidak valid',
                'per-unit'         => 'Per Unit',
                'qty-available'    => 'Jumlah Tersedia',
                'qty-to-ship'      => 'Jumlah yang Dikirim',
                'quantity-invalid' => 'Jumlah tidak valid',
                'sku'              => 'SKU - :sku',
                'source'           => 'Sumber',
                'success'          => 'Pengiriman berhasil dibuat',
                'title'            => 'Buat Pengiriman Baru',
                'tracking-number'  => 'Nomor Pelacakan',
            ],

            'view' => [
                'billing-address'      => 'Alamat Tagihan',
                'carrier-title'        => 'Nama Kurir',
                'channel'              => 'Saluran',
                'currency'             => 'Mata Uang',
                'customer'             => 'Pelanggan',
                'email'                => 'Email - :email',
                'inventory-source'     => 'Sumber Stok',
                'order-date'           => 'Tanggal Pesanan',
                'order-id'             => 'ID Pesanan',
                'order-information'    => 'Informasi Pesanan',
                'order-status'         => 'Status Pesanan',
                'ordered-items'        => 'Barang yang Dipesan',
                'payment-and-shipping' => 'Pembayaran dan Pengiriman',
                'payment-method'       => 'Metode Pembayaran',
                'product-image'        => 'Gambar Produk',
                'qty'                  => 'Jumlah - :qty',
                'shipping-address'     => 'Alamat Pengiriman',
                'shipping-method'      => 'Metode Pengiriman',
                'shipping-price'       => 'Biaya Pengiriman',
                'sku'                  => 'SKU - :sku',
                'title'                => 'Pengiriman #:shipment_id',
                'tracking-number'      => 'Nomor Pelacakan',
            ],
        ],

        'refunds' => [
            'index' => [
                'title' => 'Pengembalian Dana',

                'datagrid' => [
                    'billed-to'       => 'Ditagihkan Kepada',
                    'id'              => 'ID',
                    'order-id'        => 'ID Pesanan',
                    'refund-date'     => 'Tanggal Pengembalian Dana',
                    'refunded-amount' => 'Jumlah Dikembalikan',
                    'view'            => 'Lihat',
                ],
            ],

            'view' => [
                'account-information'        => 'Informasi Akun',
                'adjustment-fee'             => 'Biaya Penyesuaian',
                'adjustment-refund'          => 'Pengembalian Dana Penyesuaian',
                'base-discounted-amount'     => 'Jumlah Diskon - :base_discounted_amount',
                'billing-address'            => 'Alamat Tagihan',
                'currency'                   => 'Mata Uang',
                'sub-total-amount-excl-tax'  => 'Subtotal (Tanpa Pajak) - :discounted_amount',
                'sub-total-amount-incl-tax'  => 'Subtotal (Termasuk Pajak) - :discounted_amount',
                'sub-total-amount'           => 'Subtotal - :discounted_amount',
                'grand-total'                => 'Total Keseluruhan',
                'order-channel'              => 'Saluran Pesanan',
                'order-date'                 => 'Tanggal Pesanan',
                'order-id'                   => 'ID Pesanan',
                'order-information'          => 'Informasi Pesanan',
                'order-status'               => 'Status Pesanan',
                'payment-information'        => 'Informasi Pembayaran',
                'payment-method'             => 'Metode Pembayaran',
                'price-excl-tax'             => 'Harga (Tanpa Pajak) - :price',
                'price-incl-tax'             => 'Harga (Termasuk Pajak) - :price',
                'price'                      => 'Harga - :price',
                'product-image'              => 'Gambar Produk',
                'product-ordered'            => 'Produk yang Dipesan',
                'qty'                        => 'Jumlah - :qty',
                'refund'                     => 'Pengembalian Dana',
                'shipping-address'           => 'Alamat Pengiriman',
                'shipping-handling-excl-tax' => 'Pengiriman & Penanganan (Tanpa Pajak)',
                'shipping-handling-incl-tax' => 'Pengiriman & Penanganan (Termasuk Pajak)',
                'shipping-handling'          => 'Pengiriman & Penanganan',
                'shipping-method'            => 'Metode Pengiriman',
                'shipping-price'             => 'Biaya Pengiriman',
                'sku'                        => 'SKU - :sku',
                'sub-total-excl-tax'         => 'Subtotal (Tanpa Pajak)',
                'sub-total-incl-tax'         => 'Subtotal (Termasuk Pajak)',
                'sub-total'                  => 'Subtotal',
                'tax'                        => 'Pajak',
                'tax-amount'                 => 'Jumlah Pajak - :tax_amount',
                'title'                      => 'Pengembalian Dana #:refund_id',
            ],

            'create' => [
                'adjustment-fee'              => 'Biaya Penyesuaian',
                'adjustment-refund'           => 'Pengembalian Dana Penyesuaian',
                'amount-per-unit'             => ':amount Per Unit x :qty Jumlah',
                'create-success'              => 'Pengembalian dana berhasil dibuat',
                'creation-error'              => 'Pembuatan pengembalian dana tidak diizinkan.',
                'discount-amount'             => 'Jumlah Diskon',
                'grand-total'                 => 'Total Keseluruhan',
                'invalid-qty'                 => 'Terdapat jumlah tidak valid untuk faktur item.',
                'invalid-refund-amount-error' => 'Jumlah pengembalian dana harus lebih dari nol.',
                'item-canceled'               => 'Dibatalkan (:qty_canceled)',
                'item-invoice'                => 'Difakturkan (:qty_invoiced)',
                'item-ordered'                => 'Dipesan (:qty_ordered)',
                'item-refunded'               => 'Dikembalikan (:qty_refunded)',
                'item-shipped'                => 'Dikirim (:qty_shipped)',
                'per-unit'                    => 'Per Unit',
                'price'                       => 'Harga',
                'qty-to-refund'               => 'Jumlah yang Dikembalikan',
                'refund-btn'                  => 'Kembalikan Dana',
                'refund-limit-error'          => 'Jumlah pengembalian dana :amount tidak dapat diproses.',
                'refund-shipping'             => 'Pengembalian Biaya Pengiriman',
                'sku'                         => 'SKU - :sku',
                'subtotal'                    => 'Subtotal',
                'tax-amount'                  => 'Jumlah Pajak',
                'title'                       => 'Buat Pengembalian Dana',
                'update-totals-btn'           => 'Perbarui Total',
            ],
        ],

        'invoices' => [
            'index' => [
                'title' => 'Faktur',

                'datagrid' => [
                    'action'              => 'Tindakan',
                    'days-left'           => 'Sisa :count hari',
                    'days-overdue'        => 'Terlambat :count hari',
                    'grand-total'         => 'Total Keseluruhan',
                    'id'                  => 'ID',
                    'invoice-date'        => 'Tanggal Faktur',
                    'mass-update-success' => 'Faktur yang dipilih berhasil diperbarui.',
                    'order-id'            => 'ID Pesanan',
                    'overdue'             => 'Terlambat',
                    'overdue-by'          => 'Terlambat :count hari',
                    'paid'                => 'Lunas',
                    'pending'             => 'Menunggu Pembayaran',
                    'status'              => 'Status',
                    'update-status'       => 'Perbarui Status',
                ],
            ],

            'view' => [
                'amount-per-unit'                => ':amount Per Unit x :qty Jumlah',
                'channel'                        => 'Saluran',
                'customer-email'                 => 'Email - :email',
                'customer'                       => 'Pelanggan',
                'discount'                       => 'Jumlah Diskon - :discount',
                'email'                          => 'Email',
                'grand-total'                    => 'Total Keseluruhan',
                'invoice-items'                  => 'Item Faktur',
                'invoice-sent'                   => 'Faktur berhasil dikirim',
                'invoice-status'                 => 'Status Faktur',
                'order-date'                     => 'Tanggal Pesanan',
                'order-id'                       => 'ID Pesanan',
                'order-information'              => 'Informasi Pesanan',
                'order-status'                   => 'Status Pesanan',
                'price-excl-tax'                 => 'Harga (Tanpa Pajak) - :price',
                'price-incl-tax'                 => 'Harga (Termasuk Pajak) - :price',
                'price'                          => 'Harga - :price',
                'print'                          => 'Cetak',
                'product-image'                  => 'Gambar Produk',
                'qty'                            => 'Jumlah - :qty',
                'send-btn'                       => 'Kirim',
                'send-duplicate-invoice'         => 'Kirim Faktur Duplikat',
                'send'                           => 'Kirim',
                'shipping-and-handling-excl-tax' => 'Pengiriman dan Penanganan (Tanpa Pajak)',
                'shipping-and-handling-incl-tax' => 'Pengiriman dan Penanganan (Termasuk Pajak)',
                'shipping-and-handling'          => 'Pengiriman dan Penanganan',
                'sku'                            => 'SKU - :sku',
                'sub-total-excl-tax'             => 'Subtotal (Tanpa Pajak) - :sub_total',
                'sub-total-incl-tax'             => 'Subtotal (Termasuk Pajak) - :sub_total',
                'sub-total-summary-excl-tax'     => 'Subtotal (Tanpa Pajak)',
                'sub-total-summary-incl-tax'     => 'Subtotal (Termasuk Pajak)',
                'sub-total-summary'              => 'Subtotal',
                'sub-total'                      => 'Subtotal - :sub_total',
                'summary-discount'               => 'Jumlah Diskon',
                'summary-tax'                    => 'Jumlah Pajak',
                'tax'                            => 'Jumlah Pajak - :tax',
                'title'                          => 'Faktur #:invoice_id',
            ],

            'create'   => [
                'amount-per-unit'    => ':amount Per Unit x :qty Jumlah',
                'create-invoice'     => 'Buat Faktur',
                'create-success'     => 'Faktur berhasil dibuat',
                'create-transaction' => 'Buat Transaksi',
                'creation-error'     => 'Pembuatan faktur pesanan tidak diizinkan.',
                'invalid-qty'        => 'Kami menemukan jumlah yang tidak valid untuk item faktur.',
                'invoice'            => 'Faktur',
                'new-invoice'        => 'Faktur Baru',
                'product-error'      => 'Faktur tidak dapat dibuat tanpa produk.',
                'product-image'      => 'Gambar Produk',
                'qty-to-invoiced'    => 'Jumlah untuk Difakturkan',
                'sku'                => 'SKU - :sku',
            ],

            'invoice-pdf' => [
                'bank-details'               => 'Detail Bank',
                'bill-to'                    => 'Tagihan kepada',
                'contact-number'             => 'Nomor Kontak',
                'contact'                    => 'Kontak',
                'date'                       => 'Tanggal Faktur',
                'discount'                   => 'Diskon',
                'excl-tax'                   => 'Tidak Termasuk Pajak:',
                'grand-total'                => 'Total Keseluruhan',
                'invoice-id'                 => 'ID Faktur',
                'invoice'                    => 'Faktur',
                'order-date'                 => 'Tanggal Pesanan',
                'order-id'                   => 'ID Pesanan',
                'payment-method'             => 'Metode Pembayaran',
                'payment-terms'              => 'Syarat Pembayaran',
                'price'                      => 'Harga',
                'product-name'               => 'Nama Produk',
                'qty'                        => 'Jumlah',
                'ship-to'                    => 'Dikirim ke',
                'shipping-handling-excl-tax' => 'Pengiriman & Penanganan (Tidak Termasuk Pajak)',
                'shipping-handling-incl-tax' => 'Pengiriman & Penanganan (Termasuk Pajak)',
                'shipping-handling'          => 'Pengiriman & Penanganan',
                'shipping-method'            => 'Metode Pengiriman',
                'sku'                        => 'SKU',
                'subtotal-excl-tax'          => 'Subtotal (Tidak Termasuk Pajak)',
                'subtotal-incl-tax'          => 'Subtotal (Termasuk Pajak)',
                'subtotal'                   => 'Subtotal',
                'tax-amount'                 => 'Jumlah Pajak',
                'tax'                        => 'Pajak',
                'vat-number'                 => 'Nomor PPN',
            ],
        ],

        'invoice-transaction' => [
            'id'               => 'ID',
            'transaction-date' => 'Tanggal Transaksi',
            'transaction-id'   => 'ID Transaksi',
            'view'             => 'Lihat',
        ],

        'transactions' => [
            'index' => [
                'create-btn' => 'Buat Transaksi',
                'title'      => 'Transaksi',

                'datagrid' => [
                    'completed'          => 'Selesai',
                    'id'                 => 'ID',
                    'invoice-id'         => 'ID Faktur',
                    'order-id'           => 'ID Pesanan',
                    'paid'               => 'Dibayar',
                    'pending'            => 'Tertunda',
                    'status'             => 'Status',
                    'transaction-amount' => 'Jumlah',
                    'transaction-date'   => 'Tanggal',
                    'transaction-id'     => 'ID Transaksi',
                    'view'               => 'Lihat',
                ],

                'create' => [
                    'already-paid'               => 'Sudah Dibayar',
                    'amount'                     => 'Jumlah',
                    'create-transaction'         => 'Buat Transaksi',
                    'invoice-id'                 => 'ID Faktur',
                    'invoice-missing'            => 'Faktur Tidak Ditemukan',
                    'payment-method'             => 'Metode Pembayaran',
                    'save-transaction'           => 'Simpan Transaksi',
                    'transaction-amount-exceeds' => 'Jumlah Transaksi Melebihi Batas',
                    'transaction-amount-zero'    => 'Jumlah Transaksi Nol',
                    'transaction-saved'          => 'Transaksi berhasil disimpan.',
                ],

                'view' => [
                    'amount'           => 'Jumlah',
                    'created-at'       => 'Dibuat Pada',
                    'invoice-id'       => 'ID Faktur',
                    'order-id'         => 'ID Pesanan',
                    'payment-details'  => 'Detail Pembayaran',
                    'payment-method'   => 'Metode Pembayaran',
                    'status'           => 'Status',
                    'title'            => 'Detail Transaksi',
                    'transaction-id'   => 'ID Transaksi',
                ],
            ],
        ],

        'booking' => [
            'index' => [
                'datagrid' => [
                    'created-date' => 'Tanggal Dibuat',
                    'from'         => 'Dari',
                    'id'           => 'ID',
                    'order-id'     => 'ID Pesanan',
                    'qty'          => 'JML',
                    'to'           => 'Hingga',
                    'view'         => 'Lihat',
                ],

                'title'    => 'Pemesanan',
            ],

            'calendar' => [
                'booking-date'     => 'Tanggal Pemesanan',
                'booking-details'  => 'Detail Pemesanan',
                'canceled'         => 'Dibatalkan',
                'closed'           => 'Tutup',
                'done'             => 'Selesai',
                'order-id'         => 'ID Pesanan',
                'pending'          => 'Menunggu',
                'price'            => 'Harga',
                'status'           => 'Status',
                'time-slot'        => 'Slot Waktu:',
                'view-details'     => 'Lihat Detail',
            ],

            'title' => 'Produk Pemesanan',
        ],
    ],

    'catalog' => [
        'products' => [
            'index' => [
                'already-taken' => ':name sudah digunakan.',
                'create-btn'    => 'Buat Produk',
                'title'         => 'Produk',

                'create' => [
                    'back-btn'                => 'Kembali',
                    'configurable-attributes' => 'Atribut yang Dapat Dikonfigurasi',
                    'create-btn'              => 'Buat Produk',
                    'family'                  => 'Keluarga',
                    'save-btn'                => 'Simpan Produk',
                    'sku'                     => 'SKU',
                    'title'                   => 'Buat Produk Baru',
                    'type'                    => 'Tipe',
                ],

                'datagrid' => [
                    'active'                 => 'Aktif',
                    'attribute-family-value' => 'Keluarga Atribut - :attribute_family',
                    'attribute-family'       => 'Keluarga Atribut',
                    'category'               => 'Kategori',
                    'channel'                => 'Saluran',
                    'copy-of-slug'           => 'salinan-dari-:value',
                    'copy-of'                => 'Salinan Dari :value',
                    'delete'                 => 'Hapus',
                    'disable'                => 'Nonaktifkan',
                    'id-value'               => 'ID - :id',
                    'id'                     => 'ID',
                    'image'                  => 'Gambar',
                    'mass-delete-success'    => 'Produk yang dipilih berhasil dihapus',
                    'mass-update-success'    => 'Produk yang dipilih berhasil diperbarui',
                    'name'                   => 'Nama',
                    'out-of-stock'           => 'Stok Habis',
                    'price'                  => 'Harga',
                    'product-image'          => 'Gambar Produk',
                    'qty-value'              => ':qty Tersedia',
                    'qty'                    => 'Kuantitas',
                    'sku-value'              => 'SKU - :sku',
                    'sku'                    => 'SKU',
                    'status'                 => 'Status',
                    'type'                   => 'Tipe',
                    'update-status'          => 'Perbarui Status',
                ],
            ],

            'edit' => [
                'preview'  => 'Pratinjau',
                'remove'   => 'Hapus',
                'save-btn' => 'Simpan Produk',
                'title'    => 'Edit Produk',

                'channels' => [
                    'title' => 'Saluran',
                ],

                'price' => [
                    'group' => [
                        'add-group-price'           => 'Tambahkan Harga Grup',
                        'all-groups'                => 'Semua Grup',
                        'create-btn'                => 'Tambah Baru',
                        'discount-group-price-info' => 'Untuk :qty Qty dengan diskon :price',
                        'edit-btn'                  => 'Edit',
                        'empty-info'                => 'Harga khusus untuk pelanggan dalam grup tertentu.',
                        'fixed-group-price-info'    => 'Untuk :qty Qty dengan harga tetap :price',
                        'title'                     => 'Harga Grup Pelanggan',

                        'create' => [
                            'all-groups'     => 'Semua Grup',
                            'create-title'   => 'Buat Harga Grup Pelanggan',
                            'customer-group' => 'Grup Pelanggan',
                            'delete-btn'     => 'Hapus',
                            'discount'       => 'Diskon',
                            'fixed'          => 'Tetap',
                            'price'          => 'Harga',
                            'price-type'     => 'Jenis Harga',
                            'qty'            => 'Kuantitas Minimum',
                            'save-btn'       => 'Simpan',
                            'update-title'   => 'Perbarui Harga Grup Pelanggan',
                        ],
                    ],
                ],

                'inventories' => [
                    'pending-ordered-qty'      => 'Jumlah Pesanan Tertunda: :qty',
                    'pending-ordered-qty-info' => 'Jumlah pesanan tertunda akan dikurangkan dari sumber inventaris yang sesuai setelah pengiriman. Jika terjadi pembatalan, jumlah yang tertunda akan tersedia untuk dijual.',
                    'title'                    => 'Inventaris',
                ],

                'categories' => [
                    'title' => 'Kategori',
                ],

                'images' => [
                    'info'  => 'Resolusi gambar sebaiknya 560px X 609px',
                    'title' => 'Gambar',
                ],

                'videos' => [
                    'error' => ':attribute tidak boleh lebih besar dari :max kilobyte. Silakan pilih file yang lebih kecil.',
                    'info'  => 'Ukuran maksimum video harus :size',
                    'title' => 'Video',
                ],

                'links' => [
                    'related-products' => [
                        'empty-info' => 'Tambahkan produk terkait dengan cepat.',
                        'info'       => 'Selain produk yang sedang dilihat pelanggan, mereka akan disajikan dengan produk terkait.',
                        'title'      => 'Produk Terkait',
                    ],

                    'up-sells' => [
                        'empty-info' => 'Tambahkan produk up-sell dengan cepat.',
                        'info'       => 'Pelanggan disajikan dengan produk up-sell, yang merupakan alternatif premium atau berkualitas lebih tinggi dari produk yang sedang mereka lihat.',
                        'title'      => 'Produk Up-Sell',
                    ],

                    'cross-sells' => [
                        'empty-info' => 'Tambahkan produk cross-sell dengan cepat.',
                        'info'       => 'Di sebelah keranjang belanja, Anda akan menemukan produk "pembelian impulsif" yang diposisikan sebagai cross-sell untuk melengkapi barang yang sudah ditambahkan ke keranjang.',
                        'title'      => 'Produk Cross-Sell',
                    ],

                    'add-btn'           => 'Tambahkan Produk',
                    'delete'            => 'Hapus',
                    'empty-info'        => 'Untuk menambahkan produk :type dengan cepat.',
                    'empty-title'       => 'Tambahkan Produk',
                    'image-placeholder' => 'Gambar Produk',
                    'sku'               => 'SKU - :sku',
                ],

                'types' => [
                    'simple' => [
                        'customizable-options' => [
                            'add-btn'           => 'Tambah Opsi',
                            'empty-info'        => 'Untuk membuat opsi kustom dengan cepat.',
                            'empty-title'       => 'Tambah Opsi',
                            'info'              => 'Ini akan mengkustomisasi produk sederhana.',
                            'title'             => 'Item yang Dapat Dikustomisasi',

                            'update-create' => [
                                'is-required'               => 'Wajib Diisi',
                                'max-characters'            => 'Karakter Maksimal',
                                'name'                      => 'Judul',
                                'no'                        => 'Tidak',
                                'price'                     => 'Harga',
                                'save-btn'                  => 'Simpan',
                                'supported-file-extensions' => 'Ekstensi File yang Didukung',
                                'title'                     => 'Opsi',
                                'type'                      => 'Tipe',
                                'yes'                       => 'Ya',
                            ],

                            'option' => [
                                'add-btn'     => 'Tambah Opsi',
                                'delete'      => 'Hapus',
                                'delete-btn'  => 'Hapus',
                                'edit-btn'    => 'Edit',
                                'empty-info'  => 'Untuk membuat berbagai kombinasi produk dengan cepat.',
                                'empty-title' => 'Tambah Opsi',

                                'types' => [
                                    'text'          => [
                                        'title' => 'Text',
                                    ],

                                    'textarea'      => [
                                        'title' => 'Textarea',
                                    ],

                                    'checkbox'      => [
                                        'title' => 'Checkbox',
                                    ],

                                    'radio'         => [
                                        'title' => 'Radio',
                                    ],

                                    'select'        => [
                                        'title' => 'Select',
                                    ],

                                    'multiselect'   => [
                                        'title' => 'Multiselect',
                                    ],

                                    'date'          => [
                                        'title' => 'Date',
                                    ],

                                    'datetime'      => [
                                        'title' => 'Datetime',
                                    ],

                                    'time'          => [
                                        'title' => 'Time',
                                    ],

                                    'file'          => [
                                        'title' => 'File',
                                    ],
                                ],

                                'items' => [
                                    'update-create' => [
                                        'label'    => 'Label',
                                        'price'    => 'Harga',
                                        'save-btn' => 'Simpan',
                                        'title'    => 'Opsi',
                                    ],
                                ],
                            ],

                            'validations' => [
                                'associated-product' => 'Produk ini sudah terkait dengan produk bertipe configurable, grouped, atau bundle.',
                            ],
                        ],
                    ],

                    'configurable' => [
                        'add-btn'           => 'Tambah Varian',
                        'delete-btn'        => 'Hapus',
                        'edit-btn'          => 'Edit',
                        'empty-info'        => 'Untuk membuat berbagai kombinasi produk dengan cepat.',
                        'empty-title'       => 'Tambah Varian',
                        'image-placeholder' => 'Gambar Produk',
                        'info'              => 'Produk varian bergantung pada semua kombinasi atribut yang mungkin.',
                        'qty'               => ':qty Jumlah',
                        'sku'               => 'SKU - :sku',
                        'title'             => 'Variasi',

                        'create' => [
                            'description'            => 'Deskripsi',
                            'name'                   => 'Nama',
                            'save-btn'               => 'Tambah',
                            'title'                  => 'Tambah Varian',
                            'variant-already-exists' => 'Varian ini sudah ada',
                        ],

                        'edit' => [
                            'disabled'        => 'Nonaktif',
                            'edit-info'       => 'Jika ingin memperbarui detail produk, buka',
                            'edit-link-title' => 'Halaman Detail Produk',
                            'enabled'         => 'Aktif',
                            'images'          => 'Gambar',
                            'name'            => 'Nama',
                            'price'           => 'Harga',
                            'quantities'      => 'Jumlah',
                            'save-btn'        => 'Simpan',
                            'sku'             => 'SKU',
                            'status'          => 'Status',
                            'title'           => 'Produk',
                            'weight'          => 'Berat',
                        ],

                        'mass-edit' => [
                            'add-images'          => 'Tambah Gambar',
                            'apply-to-all-btn'    => 'Terapkan ke Semua',
                            'apply-to-all-name'   => 'Terapkan nama ke semua varian.',
                            'apply-to-all-sku'    => 'Terapkan harga ke semua SKU.',
                            'apply-to-all-status' => 'Terapkan status ke semua varian.',
                            'apply-to-all-weight' => 'Terapkan berat ke semua varian.',
                            'edit-inventories'    => 'Edit Inventaris',
                            'edit-names'          => 'Edit Nama',
                            'edit-prices'         => 'Edit Harga',
                            'edit-sku'            => 'Edit SKU',
                            'edit-status'         => 'Edit Status',
                            'edit-weight'         => 'Edit Berat',
                            'name'                => 'Nama',
                            'price'               => 'Harga',
                            'remove-images'       => 'Hapus Gambar',
                            'remove-variants'     => 'Hapus Varian',
                            'select-action'       => 'Pilih Aksi',
                            'select-variants'     => 'Pilih Varian',
                            'status'              => 'Status',
                            'variant-name'        => 'Nama Varian',
                            'variant-sku'         => 'SKU Varian',
                            'weight'              => 'Berat',
                        ],
                    ],

                    'grouped' => [
                        'add-btn'           => 'Tambah Produk',
                        'default-qty'       => 'Jumlah Default',
                        'delete'            => 'Hapus',
                        'empty-info'        => 'Untuk membuat berbagai kombinasi produk dengan cepat.',
                        'empty-title'       => 'Tambah Produk',
                        'image-placeholder' => 'Gambar Produk',
                        'info'              => 'Produk grup terdiri dari item individual yang ditampilkan sebagai satu set, bisa bervariasi berdasarkan musim atau tema. Setiap produk bisa dibeli satuan atau sebagai bagian dari grup.',
                        'sku'               => 'SKU - :sku',
                        'title'             => 'Produk Grup',
                    ],

                    'bundle' => [
                        'add-btn'           => 'Tambah Opsi',
                        'empty-info'        => 'Untuk membuat opsi bundel dengan cepat.',
                        'empty-title'       => 'Tambah Opsi',
                        'image-placeholder' => 'Gambar Produk',
                        'info'              => 'Produk bundel adalah paket berisi beberapa item atau layanan yang dijual bersama dengan harga khusus, memberikan nilai lebih dan kemudahan bagi pelanggan.',
                        'title'             => 'Item Bundel',

                        'update-create' => [
                            'checkbox'    => 'Checkbox',
                            'is-required' => 'Wajib Diisi',
                            'multiselect' => 'Multiselect',
                            'name'        => 'Judul',
                            'no'          => 'Tidak',
                            'radio'       => 'Radio',
                            'save-btn'    => 'Simpan',
                            'select'      => 'Select',
                            'title'       => 'Opsi',
                            'type'        => 'Tipe',
                            'yes'         => 'Ya',
                        ],

                        'option' => [
                            'add-btn'     => 'Tambah Produk',
                            'default-qty' => 'Jumlah Default',
                            'delete'      => 'Hapus',
                            'delete-btn'  => 'Hapus',
                            'edit-btn'    => 'Edit',
                            'empty-info'  => 'Untuk membuat berbagai kombinasi produk dengan cepat.',
                            'empty-title' => 'Tambah Produk',
                            'sku'         => 'SKU - :sku',

                            'types' => [
                                'checkbox' => [
                                    'info'  => 'Atur produk default menggunakan checkbox',
                                    'title' => 'Checkbox',
                                ],

                                'multiselect' => [
                                    'info'  => 'Atur produk default menggunakan tombol multiselect',
                                    'title' => 'Multiselect',
                                ],

                                'radio' => [
                                    'info'  => 'Atur produk default menggunakan tombol radio',
                                    'title' => 'Radio',
                                ],

                                'select' => [
                                    'info'  => 'Atur produk default menggunakan radio button',
                                    'title' => 'Select',
                                ],
                            ],
                        ],
                    ],

                    'booking' => [
                        'available-from' => 'Tersedia Dari',
                        'available-to'   => 'Tersedia Sampai',
                        'location'       => 'Lokasi',
                        'qty'            => 'Jumlah',
                        'title'          => 'Tipe Booking',

                        'available-every-week' => [
                            'no'    => 'Tidak',
                            'title' => 'Tersedia Setiap Minggu',
                            'yes'   => 'Ya',
                        ],

                        'appointment' => [
                            'break-duration' => 'Durasi Istirahat antar Slot (Menit)',
                            'slot-duration'  => 'Durasi Slot (Menit)',

                            'same-slot-for-all-days' => [
                                'no'    => 'Tidak',
                                'title' => 'Slot Sama untuk Semua Hari',
                                'yes'   => 'Ya',
                            ],
                        ],

                        'default' => [
                            'add'              => 'Tambah',
                            'break-duration'   => 'Durasi Istirahat antar Slot (Menit)',
                            'close'            => 'Tutup',
                            'description'      => 'Informasi Booking',
                            'description-info' => 'Durasi waktu akan dibuat dan ditampilkan berdasarkan slot yang tersedia. Ini akan unik di semua slot dan terlihat di halaman toko.',
                            'edit'             => 'Edit',
                            'many'             => 'Banyak Booking dalam Satu Hari',
                            'one'              => 'Satu Booking untuk Banyak Hari',
                            'open'             => 'Buka',
                            'slot-add'         => 'Tambah Slot',
                            'slot-duration'    => 'Durasi Slot (Menit)',
                            'slot-title'       => 'Durasi Waktu Slot',
                            'title'            => 'Default',
                            'unavailable'      => 'Tidak Tersedia',

                            'modal' => [
                                'slot' => [
                                    'add-title'  => 'Tambah Slot',
                                    'close'      => 'Tutup',
                                    'day'        => 'Hari',
                                    'edit-title' => 'Edit Slot',
                                    'friday'     => 'Jumat',
                                    'from'       => 'Dari',
                                    'from-day'   => 'Dari Hari',
                                    'from-time'  => 'Dari Waktu',
                                    'monday'     => 'Senin',
                                    'open'       => 'Buka',
                                    'saturday'   => 'Sabtu',
                                    'save'       => 'Simpan',
                                    'select'     => 'Pilih',
                                    'status'     => 'Status',
                                    'sunday'     => 'Minggu',
                                    'thursday'   => 'Kamis',
                                    'to'         => 'Sampai',
                                    'to-day'     => 'Sampai Hari',
                                    'to-time'    => 'Sampai Waktu',
                                    'tuesday'    => 'Selasa',
                                    'wednesday'  => 'Rabu',
                                    'week'       => ':day',
                                ],
                            ],
                        ],

                        'event' => [
                            'add'                => 'Tambah Tiket',
                            'delete'             => 'Hapus',
                            'description'        => 'Deskripsi',
                            'description-info'   => 'Tidak ada tiket yang tersedia.',
                            'edit'               => 'Edit',
                            'name'               => 'Nama',
                            'price'              => 'Harga',
                            'qty'                => 'Jumlah',
                            'special-price'      => 'Harga Spesial',
                            'special-price-from' => 'Harga Spesial Dari',
                            'special-price-to'   => 'Harga Spesial Sampai',
                            'title'              => 'Tiket',
                            'valid-from'         => 'Berlaku Dari',
                            'valid-until'        => 'Berlaku Sampai',

                            'modal' => [
                                'edit' => 'Ubah Tiket',
                                'save' => 'Simpan',
                            ],
                        ],

                        'empty-info' => [
                            'tickets' => [
                                'add' => 'Tambah Tiket',
                            ],

                            'slots' => [
                                'add'         => 'Tambah Slot',
                                'description' => 'Slot tersedia dengan durasi waktu.',
                            ],
                        ],

                        'rental' => [
                            'daily'        => 'Harian',
                            'daily-hourly' => 'Gabungan (Harian dan Per Jam)',
                            'daily-price'  => 'Harga Harian',
                            'hourly'       => 'Per Jam',
                            'hourly-price' => 'Harga Per Jam',
                            'title'        => 'Jenis Penyewaan',

                            'same-slot-for-all-days' => [
                                'no'    => 'Tidak',
                                'title' => 'Slot Sama untuk Semua Hari',
                                'yes'   => 'Ya',
                            ],
                        ],

                        'slots' => [
                            'add'              => 'Tambah Slot',
                            'description-info' => 'Durasi waktu akan dibuat dan ditampilkan berdasarkan slot. Setiap durasi waktu akan bersifat unik di semua slot dan akan terlihat di etalase.',
                            'save'             => 'Simpan',
                            'title'            => 'Durasi Waktu Slot',
                            'unavailable'      => 'Tidak Tersedia',

                            'action' => [
                                'add' => 'Tambah',
                            ],

                            'modal' => [
                                'slot' => [
                                    'friday'    => 'Jumat',
                                    'from'      => 'Dari',
                                    'monday'    => 'Senin',
                                    'saturday'  => 'Sabtu',
                                    'sunday'    => 'Minggu',
                                    'thursday'  => 'Kamis',
                                    'to'        => 'Sampai',
                                    'tuesday'   => 'Selasa',
                                    'wednesday' => 'Rabu',
                                ],
                            ],
                        ],

                        'table' => [
                            'break-duration'            => 'Waktu Istirahat antar Slot (Menit)',
                            'guest-capacity'            => 'Kapasitas Tamu',
                            'guest-limit'               => 'Batas Tamu per Meja',
                            'prevent-scheduling-before' => 'Cegah Penjadwalan Sebelum',
                            'slot-duration'             => 'Durasi Slot (Menit)',

                            'charged-per' => [
                                'guest' => 'Per Tamu',
                                'table' => 'Per Meja',
                                'title' => 'Dikenakan Biaya Berdasarkan',
                            ],

                            'same-slot-for-all-days' => [
                                'no'    => 'Tidak',
                                'title' => 'Slot Sama untuk Semua Hari',
                                'yes'   => 'Ya',
                            ],
                        ],

                        'type' => [
                            'appointment' => 'Pemesanan Janji Temu',
                            'default'     => 'Pemesanan Biasa',
                            'event'       => 'Pemesanan Acara',
                            'many'        => 'Banyak',
                            'one'         => 'Satu',
                            'rental'      => 'Pemesanan Sewa',
                            'table'       => 'Pemesanan Meja',
                            'title'       => 'Jenis',
                        ],

                        'validations' => [
                            'type-mismatch' => 'Jenis pemesanan tidak dapat diubah.',
                        ],
                    ],

                    'downloadable' => [
                        'links' => [
                            'add-btn'     => 'Tambah Tautan',
                            'delete-btn'  => 'Hapus',
                            'edit-btn'    => 'Edit',
                            'empty-info'  => 'Buat tautan langsung saat dibutuhkan.',
                            'empty-title' => 'Tambah Tautan',
                            'file'        => 'Berkas:',
                            'info'        => 'Tipe produk unduhan memungkinkan penjualan produk digital, seperti eBook, aplikasi perangkat lunak, musik, game, dan sebagainya.',
                            'sample-file' => 'Contoh Berkas:',
                            'sample-url'  => 'URL Contoh:',
                            'title'       => 'Tautan Unduhan',
                            'url'         => 'URL:',

                            'update-create' => [
                                'downloads'   => 'Jumlah Unduhan Diizinkan',
                                'file'        => 'Berkas',
                                'file-type'   => 'Tipe Berkas',
                                'name'        => 'Judul',
                                'price'       => 'Harga',
                                'sample'      => 'Contoh',
                                'sample-type' => 'Tipe Contoh',
                                'save-btn'    => 'Simpan',
                                'title'       => 'Tautan',
                                'url'         => 'URL',
                            ],
                        ],

                        'samples' => [
                            'add-btn'     => 'Tambah Contoh',
                            'delete-btn'  => 'Hapus',
                            'edit-btn'    => 'Edit',
                            'empty-info'  => 'Buat contoh langsung saat dibutuhkan.',
                            'empty-title' => 'Tambah Contoh',
                            'file'        => 'Berkas:',
                            'info'        => 'Tipe produk unduhan memungkinkan penjualan produk digital, seperti eBook, aplikasi perangkat lunak, musik, game, dan sebagainya.',
                            'title'       => 'Contoh Unduhan',
                            'url'         => 'URL:',

                            'update-create' => [
                                'file'      => 'Berkas',
                                'file-type' => 'Tipe Berkas',
                                'name'      => 'Judul',
                                'save-btn'  => 'Simpan',
                                'title'     => 'Tautan',
                                'url'       => 'URL',
                            ],
                        ],
                    ],
                ],
            ],

            'create-success'          => 'Produk berhasil dibuat',
            'delete-failed'           => 'Gagal menghapus produk',
            'delete-success'          => 'Produk berhasil dihapus',
            'product-copied'          => 'Produk berhasil disalin',
            'saved-inventory-message' => 'Produk berhasil disimpan',
            'update-success'          => 'Produk berhasil diperbarui',
        ],

        'attributes' => [
            'index' => [
                'create-btn' => 'Buat Atribut',
                'title'      => 'Atribut',

                'datagrid' => [
                    'boolean'             => 'Boolean',
                    'channel-based'       => 'Berdasarkan Saluran',
                    'checkbox'            => 'Kotak Centang',
                    'code'                => 'Kode',
                    'created-at'          => 'Dibuat Pada',
                    'date'                => 'Tanggal',
                    'date-time'           => 'Tanggal & Waktu',
                    'delete'              => 'Hapus',
                    'edit'                => 'Edit',
                    'false'               => 'Salah',
                    'file'                => 'Berkas',
                    'id'                  => 'ID',
                    'image'               => 'Gambar',
                    'locale-based'        => 'Berdasarkan Lokal',
                    'mass-delete-success' => 'Atribut yang Dipilih Berhasil Dihapus',
                    'multiselect'         => 'Pilih Banyak',
                    'name'                => 'Nama',
                    'price'               => 'Harga',
                    'required'            => 'Wajib',
                    'select'              => 'Pilih',
                    'text'                => 'Teks',
                    'textarea'            => 'Teks Panjang',
                    'true'                => 'Benar',
                    'type'                => 'Tipe',
                    'unique'              => 'Unik',
                ],
            ],

            'create' => [
                'add-attribute-options' => 'Tambahkan Opsi Atribut',
                'add-option'            => 'Tambahkan Opsi',
                'add-options-info'      => 'Untuk membuat berbagai kombinasi opsi atribut dengan cepat.',
                'add-row'               => 'Tambahkan Baris',
                'admin'                 => 'Admin',
                'admin-name'            => 'Nama Admin',
                'back-btn'              => 'Kembali',
                'boolean'               => 'Boolean',
                'checkbox'              => 'Kotak Centang',
                'code'                  => 'Kode Atribut',
                'color'                 => 'Warna',
                'configuration'         => 'Konfigurasi',
                'create-empty-option'   => 'Buat opsi kosong secara default',
                'date'                  => 'Tanggal',
                'datetime'              => 'Tanggal & Waktu',
                'decimal'               => 'Desimal',
                'default-value'         => 'Nilai Default',
                'option-deleted'        => 'Opsi Berhasil Dihapus',
                'email'                 => 'Email',
                'enable-wysiwyg'        => 'Aktifkan Editor WYSIWYG',
                'file'                  => 'Berkas',
                'general'               => 'Umum',
                'image'                 => 'Gambar',
                'input-options'         => 'Opsi Input',
                'input-validation'      => 'Validasi Input',
                'is-comparable'         => 'Atribut dapat dibandingkan',
                'is-configurable'       => 'Gunakan untuk Membuat Produk Konfigurabel',
                'is-filterable'         => 'Gunakan dalam Navigasi Bertingkat',
                'is-required'           => 'Wajib Diisi',
                'is-unique'             => 'Harus Unik',
                'is-visible-on-front'   => 'Tampilkan di Halaman Produk di Frontend',
                'label'                 => 'Label',
                'multiselect'           => 'Pilih Banyak',
                'no'                    => 'Tidak',
                'numeric'               => 'Angka',
                'options'               => 'Opsi',
                'position'              => 'Posisi',
                'price'                 => 'Harga',
                'regex'                 => 'Regex',
                'regex-info'            => 'Ekspresi harus berada dalam tanda kutip ganda.',
                'save-btn'              => 'Simpan Atribut',
                'select'                => 'Pilih',
                'select-type'           => 'Pilih Tipe Atribut',
                'swatch'                => 'Swatch',
                'text'                  => 'Teks',
                'textarea'              => 'Bidang Teks',
                'title'                 => 'Tambahkan Atribut',
                'type'                  => 'Tipe Atribut',
                'url'                   => 'URL',
                'use-in-flat'           => 'Buat di Tabel Produk Flat',
                'validations'           => 'Validasi',
                'value-per-channel'     => 'Nilai Per Saluran',
                'value-per-locale'      => 'Nilai Per Lokal',
                'yes'                   => 'Ya',

                'option'                => [
                    'color'    => 'Swatch Warna',
                    'dropdown' => 'Dropdown',
                    'image'    => 'Swatch Gambar',
                    'save-btn' => 'Simpan Opsi',
                    'text'     => 'Swatch Teks',
                ],
            ],

            'edit' => [
                'add-attribute-options' => 'Tambahkan Opsi Atribut',
                'add-option'            => 'Tambahkan Opsi',
                'add-options-info'      => 'Untuk membuat berbagai kombinasi opsi atribut dengan cepat.',
                'add-row'               => 'Tambahkan Baris',
                'admin'                 => 'Admin',
                'admin-name'            => 'Nama Admin',
                'back-btn'              => 'Kembali',
                'boolean'               => 'Boolean',
                'checkbox'              => 'Kotak Centang',
                'code'                  => 'Kode Atribut',
                'color'                 => 'Warna',
                'configuration'         => 'Konfigurasi',
                'create-empty-option'   => 'Buat opsi kosong secara default',
                'date'                  => 'Tanggal',
                'datetime'              => 'Tanggal & Waktu',
                'decimal'               => 'Desimal',
                'default-value'         => 'Nilai Default',
                'option-deleted'        => 'Opsi Berhasil Dihapus',
                'email'                 => 'Email',
                'enable-wysiwyg'        => 'Aktifkan Editor WYSIWYG',
                'file'                  => 'Berkas',
                'general'               => 'Umum',
                'image'                 => 'Gambar',
                'input-options'         => 'Opsi Input',
                'input-validation'      => 'Validasi Input',
                'is-comparable'         => 'Atribut dapat dibandingkan',
                'is-configurable'       => 'Gunakan untuk Membuat Produk Konfigurabel',
                'is-filterable'         => 'Gunakan dalam Navigasi Bertingkat',
                'is-required'           => 'Wajib Diisi',
                'is-unique'             => 'Harus Unik',
                'is-visible-on-front'   => 'Tampilkan di Halaman Produk di Frontend',
                'label'                 => 'Label',
                'multiselect'           => 'Pilih Banyak',
                'no'                    => 'Tidak',
                'numeric'               => 'Angka',
                'options'               => 'Opsi',
                'position'              => 'Posisi',
                'price'                 => 'Harga',
                'regex'                 => 'Regex',
                'regex-info'            => 'Ekspresi harus berada dalam tanda kutip ganda.',
                'save-btn'              => 'Simpan Atribut',
                'select'                => 'Pilih',
                'select-type'           => 'Pilih Tipe Atribut',
                'swatch'                => 'Swatch',
                'text'                  => 'Teks',
                'textarea'              => 'Bidang Teks',
                'title'                 => 'Edit Atribut',
                'type'                  => 'Tipe Atribut',
                'url'                   => 'URL',
                'use-in-flat'           => 'Buat di Tabel Produk Flat',
                'validations'           => 'Validasi',
                'value-per-channel'     => 'Nilai Per Saluran',
                'value-per-locale'      => 'Nilai Per Lokal',
                'yes'                   => 'Ya',

                'option' => [
                    'color'    => 'Swatch Warna',
                    'dropdown' => 'Dropdown',
                    'image'    => 'Swatch Gambar',
                    'save-btn' => 'Simpan Opsi',
                    'text'     => 'Swatch Teks',
                ],
            ],

            'create-success'    => 'Atribut Berhasil Dibuat',
            'delete-failed'     => 'Gagal Menghapus Atribut',
            'delete-success'    => 'Atribut Berhasil Dihapus',
            'update-success'    => 'Atribut Berhasil Diperbarui',
            'user-define-error' => 'Tidak dapat menghapus atribut sistem',
        ],

        'categories' => [
            'index' => [
                'add-btn' => 'Buat Kategori',
                'title'   => 'Kategori',

                'datagrid' => [
                    'active'         => 'Aktif',
                    'delete-success' => ':resource yang dipilih berhasil dihapus',
                    'delete'         => 'Hapus',
                    'edit'           => 'Edit',
                    'id'             => 'ID',
                    'inactive'       => 'Tidak Aktif',
                    'name'           => 'Nama',
                    'no-of-products' => 'Jumlah Produk',
                    'position'       => 'Posisi',
                    'status'         => 'Terlihat di Menu',
                    'update-status'  => 'Perbarui Status',
                ],
            ],

            'create' => [
                'add-banner'               => 'Tambahkan Banner',
                'add-logo'                 => 'Tambahkan Logo',
                'back-btn'                 => 'Kembali',
                'banner'                   => 'Banner',
                'banner-size'              => 'Rasio aspek banner (1320px X 300px)',
                'description'              => 'Deskripsi',
                'description-and-images'   => 'Deskripsi dan Gambar',
                'description-only'         => 'Hanya Deskripsi',
                'display-mode'             => 'Mode Tampilan',
                'enter-position'           => 'Masukkan Posisi',
                'filterable-attributes'    => 'Atribut yang Dapat Difilter',
                'general'                  => 'Umum',
                'logo'                     => 'Logo',
                'logo-size'                => 'Resolusi logo harus (110px X 110px)',
                'meta-description'         => 'Deskripsi Meta',
                'meta-keywords'            => 'Kata Kunci Meta',
                'meta-title'               => 'Judul Meta',
                'name'                     => 'Nama',
                'parent-category'          => 'Kategori Induk',
                'position'                 => 'Posisi',
                'products-and-description' => 'Produk dan Deskripsi',
                'products-only'            => 'Hanya Produk',
                'save-btn'                 => 'Simpan Kategori',
                'select-display-mode'      => 'Pilih Mode Tampilan',
                'seo-details'              => 'Detail SEO',
                'settings'                 => 'Pengaturan',
                'slug'                     => 'Slug',
                'title'                    => 'Tambah Kategori Baru',
                'visible-in-menu'          => 'Terlihat di Menu',
            ],

            'edit' => [
                'add-banner'               => 'Tambahkan Banner',
                'add-logo'                 => 'Tambahkan Logo',
                'back-btn'                 => 'Kembali',
                'banner'                   => 'Banner',
                'banner-size'              => 'Rasio aspek banner (1320px X 300px)',
                'description'              => 'Deskripsi',
                'description-and-images'   => 'Deskripsi dan Gambar',
                'description-only'         => 'Hanya Deskripsi',
                'display-mode'             => 'Mode Tampilan',
                'enter-position'           => 'Masukkan Posisi',
                'filterable-attributes'    => 'Atribut yang Dapat Difilter',
                'general'                  => 'Umum',
                'logo'                     => 'Logo',
                'logo-size'                => 'Resolusi logo harus (110px X 110px)',
                'meta-description'         => 'Deskripsi Meta',
                'meta-keywords'            => 'Kata Kunci Meta',
                'meta-title'               => 'Judul Meta',
                'name'                     => 'Nama',
                'position'                 => 'Posisi',
                'products-and-description' => 'Produk dan Deskripsi',
                'products-only'            => 'Hanya Produk',
                'save-btn'                 => 'Simpan Kategori',
                'select-display-mode'      => 'Pilih Mode Tampilan',
                'select-parent-category'   => 'Pilih Kategori Induk',
                'seo-details'              => 'Detail SEO',
                'settings'                 => 'Pengaturan',
                'slug'                     => 'Slug',
                'title'                    => 'Edit Kategori',
                'visible-in-menu'          => 'Terlihat di Menu',
            ],

            'category'             => 'Kategori',
            'create-success'       => 'Kategori berhasil dibuat.',
            'delete-category-root' => 'Kategori utama tidak dapat dihapus.',
            'delete-failed'        => 'Terjadi kesalahan saat menghapus kategori',
            'delete-success'       => 'Kategori berhasil dihapus.',
            'update-success'       => 'Kategori berhasil diperbarui.',
        ],

        'families' => [
            'index' => [
                'add'   => 'Buat Keluarga Atribut',
                'title' => 'Keluarga',

                'datagrid' => [
                    'code'           => 'Kode',
                    'delete'         => 'Hapus',
                    'delete-success' => ':resource yang dipilih berhasil dihapus',
                    'edit'           => 'Edit',
                    'id'             => 'ID',
                    'method-error'   => 'Kesalahan! Metode yang salah terdeteksi, silakan periksa konfigurasi aksi massal',
                    'name'           => 'Nama',
                    'no-resource'    => 'Sumber daya yang diberikan tidak mencukupi untuk aksi ini',
                    'partial-action' => 'Beberapa aksi tidak dilakukan karena batasan sistem pada :resource',
                    'update-success' => ':resource yang dipilih berhasil diperbarui',
                ],
            ],

            'create' => [
                'add-group-btn'                    => 'Tambahkan Grup',
                'add-group-title'                  => 'Tambah Grup Baru',
                'back-btn'                         => 'Kembali',
                'code'                             => 'Kode',
                'column'                           => 'Kolom',
                'delete-group-btn'                 => 'Hapus Grup',
                'edit-group-info'                  => 'Klik dua kali untuk mengedit Grup',
                'enter-code'                       => 'Masukkan Kode',
                'enter-name'                       => 'Masukkan Nama',
                'general'                          => 'Umum',
                'group-code-already-exists'        => 'Kode grup atribut sudah ada.',
                'group-contains-system-attributes' => 'Grup ini berisi atribut sistem. Pindahkan atribut sistem ke grup lain terlebih dahulu, lalu coba lagi.',
                'group-name-already-exists'        => 'Nama grup atribut sudah ada.',
                'groups'                           => 'Grup',
                'groups-info'                      => 'Kelola grup keluarga atribut',
                'main-column'                      => 'Kolom Utama',
                'name'                             => 'Nama',
                'removal-not-possible'             => 'Anda tidak dapat menghapus atribut sistem dari keluarga atribut.',
                'right-column'                     => 'Kolom Samping Kanan',
                'save-btn'                         => 'Simpan Keluarga Atribut',
                'select-group'                     => 'Silakan pilih grup atribut.',
                'title'                            => 'Buat Keluarga Atribut',
                'unassigned-attributes'            => 'Atribut yang Belum Ditugaskan',
                'unassigned-attributes-info'       => 'Seret atribut ini untuk ditambahkan ke kolom atau grup.',
            ],

            'edit' => [
                'add-group-btn'                    => 'Tambahkan Grup',
                'add-group-title'                  => 'Tambah Grup Baru',
                'back-btn'                         => 'Kembali',
                'code'                             => 'Kode',
                'column'                           => 'Kolom',
                'delete-group-btn'                 => 'Hapus Grup',
                'edit-group-info'                  => 'Klik dua kali untuk mengedit Grup',
                'enter-code'                       => 'Masukkan Kode',
                'enter-name'                       => 'Masukkan Nama',
                'general'                          => 'Umum',
                'group-code-already-exists'        => 'Kode grup atribut sudah ada.',
                'group-contains-system-attributes' => 'Grup ini berisi atribut sistem. Pindahkan atribut sistem ke grup lain terlebih dahulu, lalu coba lagi.',
                'group-name-already-exists'        => 'Nama grup atribut sudah ada.',
                'groups'                           => 'Grup',
                'groups-info'                      => 'Kelola grup keluarga atribut',
                'main-column'                      => 'Kolom Utama',
                'name'                             => 'Nama',
                'removal-not-possible'             => 'Anda tidak dapat menghapus atribut sistem dari keluarga atribut.',
                'right-column'                     => 'Kolom Samping Kanan',
                'save-btn'                         => 'Simpan Keluarga Atribut',
                'select-group'                     => 'Silakan pilih grup atribut.',
                'title'                            => 'Edit Keluarga Atribut',
                'unassigned-attributes'            => 'Atribut yang Belum Ditugaskan',
                'unassigned-attributes-info'       => 'Seret atribut ini untuk ditambahkan ke kolom atau grup.',
            ],

            'attribute-family'        => 'Keluarga Atribut',
            'attribute-product-error' => 'Keluarga ini digunakan dalam produk.',
            'create-success'          => 'Keluarga atribut berhasil dibuat.',
            'delete-failed'           => 'Terjadi kesalahan saat menghapus keluarga atribut.',
            'delete-success'          => 'Keluarga atribut berhasil dihapus.',
            'family'                  => 'Keluarga',
            'last-delete-error'       => 'Setidaknya satu keluarga atribut harus ada.',
            'update-success'          => 'Keluarga atribut berhasil diperbarui.',
            'user-define-error'       => 'Tidak dapat menghapus keluarga atribut sistem.',
        ],
    ],

    'customers' => [
        'customers' => [
            'index' => [
                'title'         => 'Pelanggan',
                'login-message' => 'Anda masuk sebagai :customer_name',

                'datagrid' => [
                    'active'         => 'Aktif',
                    'address'        => ':address Alamat',
                    'address-count'  => 'Jumlah Alamat',
                    'channel'        => 'Saluran',
                    'delete'         => 'Hapus',
                    'delete-success' => 'Data yang dipilih berhasil dihapus',
                    'email'          => 'Email',
                    'gender'         => 'Jenis Kelamin',
                    'group'          => 'Grup',
                    'id'             => 'ID Pelanggan',
                    'id-value'       => 'ID - :id',
                    'inactive'       => 'Tidak Aktif',
                    'method-error'   => 'Kesalahan! Metode yang salah terdeteksi, silakan periksa konfigurasi aksi massal',
                    'name'           => 'Nama Pelanggan',
                    'no-resource'    => 'Sumber daya yang diberikan tidak mencukupi untuk tindakan ini',
                    'order'          => ':order Pesanan',
                    'order-count'    => 'Jumlah Pesanan',
                    'order-pending'  => 'Pelanggan memiliki pesanan yang tertunda',
                    'partial-action' => 'Beberapa tindakan tidak dapat dilakukan karena batasan sistem pada :resource',
                    'phone'          => 'Nomor Kontak',
                    'revenue'        => 'Pendapatan',
                    'status'         => 'Status',
                    'suspended'      => 'Ditangguhkan',
                    'update-status'  => 'Perbarui Status',
                    'update-success' => 'Pelanggan yang dipilih berhasil diperbarui',
                ],

                'create' => [
                    'contact-number'        => 'Nomor Kontak',
                    'create-btn'            => 'Tambah Pelanggan',
                    'create-success'        => 'Pelanggan berhasil ditambahkan',
                    'customer-group'        => 'Grup Pelanggan',
                    'date-of-birth'         => 'Tanggal Lahir',
                    'email'                 => 'Email',
                    'female'                => 'Perempuan',
                    'first-name'            => 'Nama Depan',
                    'gender'                => 'Jenis Kelamin',
                    'last-name'             => 'Nama Belakang',
                    'male'                  => 'Laki-laki',
                    'other'                 => 'Lainnya',
                    'save-btn'              => 'Simpan Pelanggan',
                    'select-customer-group' => 'Pilih Grup Pelanggan',
                    'select-gender'         => 'Pilih Jenis Kelamin',
                    'title'                 => 'Tambah Pelanggan Baru',
                ],
            ],

            'view' => [
                'account-delete-confirmation' => 'Apakah Anda yakin ingin menghapus akun ini?',
                'active'                      => 'Aktif',
                'address-delete-confirmation' => 'Apakah Anda yakin ingin menghapus alamat ini?',
                'back-btn'                    => 'Kembali',
                'create-order'                => 'Buat Pesanan',
                'customer'                    => 'Pelanggan',
                'date-of-birth'               => 'Tanggal Lahir - :dob',
                'default-address'             => 'Alamat Utama',
                'delete-account'              => 'Hapus Akun',
                'delete'                      => 'Hapus',
                'email'                       => 'Email - :email',
                'empty-description'           => 'Tambahkan Alamat Baru untuk Pelanggan',
                'empty-title'                 => 'Tambah Alamat Pelanggan',
                'gender'                      => 'Jenis Kelamin - :gender',
                'group'                       => 'Grup - :group_code',
                'inactive'                    => 'Tidak Aktif',
                'login-as-customer'           => 'Masuk sebagai pelanggan',
                'note-created-success'        => 'Catatan berhasil dibuat',
                'order-create-confirmation'   => 'Apakah Anda yakin ingin membuat pesanan untuk pelanggan ini?',
                'phone'                       => 'Telepon - :phone',
                'set-as-default'              => 'Jadikan Sebagai Utama',
                'suspended'                   => 'Ditangguhkan',
                'title'                       => 'Detail Pelanggan',

                'address' => [
                    'count' => 'Alamat (:count)',

                    'create' => [
                        'city'               => 'Kota',
                        'company-name'       => 'Nama Perusahaan',
                        'country'            => 'Negara',
                        'create-btn'         => 'Tambah',
                        'create-address-btn' => 'Tambah Alamat Baru',
                        'default-address'    => 'Alamat Utama',
                        'email'              => 'Email',
                        'first-name'         => 'Nama Depan',
                        'last-name'          => 'Nama Belakang',
                        'phone'              => 'Telepon',
                        'post-code'          => 'Kode Pos',
                        'save-btn-title'     => 'Simpan Alamat',
                        'select-country'     => 'Pilih Negara',
                        'state'              => 'Provinsi',
                        'street-address'     => 'Alamat Jalan',
                        'title'              => 'Tambah Alamat',
                        'vat-id'             => 'ID Pajak',
                    ],

                    'edit' => [
                        'city'            => 'Kota',
                        'company-name'    => 'Nama Perusahaan',
                        'country'         => 'Negara',
                        'default-address' => 'Alamat Utama',
                        'edit-btn'        => 'Edit',
                        'email'           => 'Email',
                        'first-name'      => 'Nama Depan',
                        'last-name'       => 'Nama Belakang',
                        'phone'           => 'Telepon',
                        'post-code'       => 'Kode Pos',
                        'save-btn-title'  => 'Simpan Alamat',
                        'select-country'  => 'Pilih Negara',
                        'state'           => 'Provinsi',
                        'street-address'  => 'Alamat Jalan',
                        'title'           => 'Edit Alamat',
                        'vat-id'          => 'ID Pajak',
                    ],

                    'address-delete-success' => 'Alamat berhasil dihapus',
                    'create-success'         => 'Alamat berhasil ditambahkan',
                    'set-default-success'    => 'Alamat utama berhasil diperbarui',
                    'success-mass-delete'    => 'Alamat yang dipilih berhasil dihapus',
                    'update-success'         => 'Alamat berhasil diperbarui',
                ],

                'datagrid' => [
                    'invoices' => [
                        'empty-invoice'  => 'Tidak Ada Faktur Tersedia',
                        'increment-id'   => 'ID Faktur',
                        'invoice-amount' => 'Jumlah Faktur',
                        'invoice-date'   => 'Tanggal Faktur',
                        'order-id'       => 'ID Pesanan',
                        'view'           => 'Lihat',
                    ],

                    'orders' => [
                        'canceled'        => 'Dibatalkan',
                        'channel-name'    => 'Nama Kanal',
                        'closed'          => 'Ditutup',
                        'completed'       => 'Selesai',
                        'customer-name'   => 'Nama Pelanggan',
                        'date'            => 'Tanggal',
                        'empty-order'     => 'Tidak Ada Pesanan Tersedia',
                        'email'           => 'Email',
                        'fraud'           => 'Penipuan',
                        'grand-total'     => 'Total Keseluruhan',
                        'location'        => 'Lokasi',
                        'order-id'        => 'ID Pesanan',
                        'pay-via'         => 'Bayar Melalui',
                        'pending'         => 'Tertunda',
                        'pending-payment' => 'Menunggu Pembayaran',
                        'processing'      => 'Sedang Diproses',
                        'status'          => 'Status',
                        'view'            => 'Lihat',
                    ],

                    'reviews' => [
                        'approved'      => 'Disetujui',
                        'comment'       => 'Komentar',
                        'created-at'    => 'Dibuat Pada',
                        'disapproved'   => 'Ditolak',
                        'empty-reviews' => 'Tidak Ada Ulasan Tersedia',
                        'id'            => 'ID',
                        'invoice-date'  => 'Tanggal Faktur',
                        'pending'       => 'Menunggu',
                        'product-id'    => 'ID Produk',
                        'product-name'  => 'Nama Produk',
                        'rating'        => 'Penilaian',
                        'status'        => 'Status',
                        'title'         => 'Judul',
                    ],
                ],

                'edit' => [
                    'contact-number'        => 'Nomor Kontak',
                    'customer-group'        => 'Grup Pelanggan',
                    'date-of-birth'         => 'Tanggal Lahir',
                    'edit-btn'              => 'Edit',
                    'email'                 => 'Email',
                    'female'                => 'Perempuan',
                    'first-name'            => 'Nama Depan',
                    'gender'                => 'Jenis Kelamin',
                    'last-name'             => 'Nama Belakang',
                    'male'                  => 'Laki-laki',
                    'other'                 => 'Lainnya',
                    'save-btn'              => 'Simpan Pelanggan',
                    'select-customer-group' => 'Pilih Grup Pelanggan',
                    'select-gender'         => 'Pilih Jenis Kelamin',
                    'status'                => 'Status',
                    'suspended'             => 'Ditangguhkan',
                    'title'                 => 'Edit Pelanggan',
                ],

                'invoices' => [
                    'count'        => 'Faktur (:count)',
                    'increment-id' => '# :increment_id',
                ],

                'notes' => [
                    'add-note'              => 'Tambah Catatan',
                    'customer-not-notified' => ':date | Pelanggan <b>Tidak Diberi Notifikasi</b>',
                    'customer-notified'     => ':date | Pelanggan <b>Sudah Diberi Notifikasi</b>',
                    'note'                  => 'Catatan',
                    'note-placeholder'      => 'Tulis catatan Anda di sini',
                    'notify-customer'       => 'Beritahu Pelanggan',
                    'submit-btn-title'      => 'Kirim Catatan',
                ],

                'orders' => [
                    'count'         => 'Pesanan (:count)',
                    'increment-id'  => '# :increment_id',
                    'total-revenue' => 'Total Pendapatan - :revenue',
                ],

                'reviews' => [
                    'id'    => 'ID - :id',
                    'count' => 'Ulasan (:count)',
                ],

                'cart' => [
                    'delete-success' => 'Item keranjang berhasil dihapus.',
                ],

                'wishlist' => [
                    'delete-success' => 'Item daftar keinginan berhasil dihapus.',
                ],

                'compare' => [
                    'delete-success' => 'Item perbandingan berhasil dihapus.',
                ],
            ],

            'delete-failed'  => 'Gagal menghapus pelanggan',
            'delete-success' => 'Pelanggan berhasil dihapus',
            'order-pending'  => 'Pesanan masih tertunda',
            'update-success' => 'Pelanggan berhasil diperbarui',
        ],

        'groups' => [
            'index' => [
                'title' => 'Grup',

                'create' => [
                    'code'       => 'Kode',
                    'create-btn' => 'Buat Grup',
                    'name'       => 'Nama',
                    'save-btn'   => 'Simpan Grup',
                    'success'    => 'Grup berhasil dibuat',
                    'title'      => 'Buat Grup Baru',
                ],

                'edit' => [
                    'delete-failed'  => 'Gagal menghapus grup',
                    'delete-success' => 'Grup berhasil dihapus',
                    'group-default'  => 'Grup default tidak dapat dihapus',
                    'success'        => 'Grup berhasil diperbarui',
                    'title'          => 'Edit Grup',
                ],

                'datagrid' => [
                    'code'   => 'Kode',
                    'delete' => 'Hapus',
                    'edit'   => 'Edit',
                    'id'     => 'ID',
                    'name'   => 'Nama',
                ],
            ],
        ],

        'gdpr' => [
            'index' => [
                'title' => 'Permintaan GDPR',

                'datagrid' => [
                    'completed'     => 'Selesai',
                    'created-at'    => 'Dibuat Pada',
                    'customer-name' => 'Nama Pelanggan',
                    'declined'      => 'Ditolak',
                    'delete'        => 'Hapus',
                    'edit'          => 'Edit',
                    'id'            => 'ID',
                    'message'       => 'Pesan',
                    'pending'       => 'Menunggu',
                    'processing'    => 'Diproses',
                    'revoked'       => 'Dibatalkan',
                    'status'        => 'Status',
                    'type'          => 'Tipe',
                ],

                'modal' => [
                    'completed'  => 'Selesai',
                    'declined'   => 'Ditolak',
                    'pending'    => 'Menunggu',
                    'processing' => 'Diproses',
                    'status'     => 'Status',
                    'title'      => 'Edit Permintaan Data GDPR',
                    'type'       => 'Tipe',
                    'message'    => 'Pesan',
                    'save-btn'   => 'Simpan',
                    'revoked'    => 'Dibatalkan',
                ],

                'update-success'              => 'Permintaan data berhasil diperbarui dan email telah dikirim ke pelanggan.',
                'delete-success'              => 'Permintaan data berhasil dihapus.',
                'attribute-reason-error'      => 'Tidak dapat dihapus.',
                'update-success-unsent-email' => 'Permintaan data berhasil diperbarui tetapi email gagal dikirim ke pelanggan.',
            ],
        ],

        'reviews' => [
            'index' => [
                'date'        => 'Tanggal',
                'description' => 'Deskripsi',
                'id'          => 'ID',
                'name'        => 'Nama',
                'product'     => 'Produk',
                'rating'      => 'Peringkat',
                'status'      => 'Status',
                'title'       => 'Ulasan',

                'edit' => [
                    'approved'       => 'Disetujui',
                    'customer'       => 'Pelanggan',
                    'date'           => 'Tanggal',
                    'disapproved'    => 'Ditolak',
                    'id'             => 'ID',
                    'images'         => 'Gambar',
                    'pending'        => 'Tertunda',
                    'product'        => 'Produk',
                    'rating'         => 'Peringkat',
                    'review-comment' => 'Komentar',
                    'review-title'   => 'Judul',
                    'save-btn'       => 'Simpan',
                    'status'         => 'Status',
                    'title'          => 'Edit Ulasan',
                    'update-success' => 'Ulasan berhasil diperbarui',
                ],

                'datagrid' => [
                    'approved'            => 'Disetujui',
                    'comment'             => 'Komentar',
                    'customer-names'      => 'Nama',
                    'date'                => 'Tanggal',
                    'delete'              => 'Hapus',
                    'delete-success'      => 'Ulasan berhasil dihapus',
                    'disapproved'         => 'Ditolak',
                    'edit'                => 'Edit',
                    'id'                  => 'ID',
                    'mass-delete-error'   => 'Terjadi kesalahan',
                    'mass-delete-success' => 'Ulasan yang dipilih berhasil dihapus',
                    'mass-update-success' => 'Ulasan yang dipilih berhasil diperbarui',
                    'pending'             => 'Tertunda',
                    'product'             => 'Produk',
                    'rating'              => 'Peringkat',
                    'review-id'           => 'ID - :review_id',
                    'status'              => 'Status',
                    'title'               => 'Judul',
                    'update-status'       => 'Perbarui Status',
                ],
            ],
        ],
    ],

    'marketing' => [
        'communications' => [
            'templates' => [
                'index' => [
                    'create-btn' => 'Buat Template',
                    'title'      => 'Template Email',

                    'datagrid' => [
                        'active'   => 'Aktif',
                        'draft'    => 'Draf',
                        'id'       => 'ID',
                        'inactive' => 'Tidak Aktif',
                        'name'     => 'Nama',
                        'status'   => 'Status',
                    ],
                ],

                'create' => [
                    'active'         => 'Aktif',
                    'back-btn'       => 'Kembali',
                    'content'        => 'Konten',
                    'create-success' => 'Template email berhasil dibuat.',
                    'draft'          => 'Draf',
                    'general'        => 'Umum',
                    'inactive'       => 'Tidak Aktif',
                    'name'           => 'Nama',
                    'save-btn'       => 'Simpan Template',
                    'select-status'  => 'Pilih Status',
                    'status'         => 'Status',
                    'title'          => 'Buat Template',
                ],

                'edit' => [
                    'active'         => 'Aktif',
                    'back-btn'       => 'Kembali',
                    'content'        => 'Konten',
                    'draft'          => 'Draf',
                    'general'        => 'Umum',
                    'inactive'       => 'Tidak Aktif',
                    'name'           => 'Nama',
                    'save-btn'       => 'Simpan Template',
                    'status'         => 'Status',
                    'title'          => 'Edit Template',
                    'update-success' => 'Berhasil diperbarui.',
                ],

                'delete-failed'  => 'Gagal menghapus :name',
                'delete-success' => 'Template berhasil dihapus',
                'email-template' => 'Template Email',
            ],

            'campaigns' => [
                'index' => [
                    'create-btn' => 'Buat Kampanye',
                    'title'      => 'Kampanye',

                    'datagrid' => [
                        'active'   => 'Aktif',
                        'delete'   => 'Hapus',
                        'edit'     => 'Edit',
                        'id'       => 'ID',
                        'inactive' => 'Tidak Aktif',
                        'name'     => 'Nama',
                        'status'   => 'Status',
                        'subject'  => 'Subjek',
                    ],
                ],

                'create' => [
                    'active'          => 'Aktif',
                    'back-btn'        => 'Kembali',
                    'channel'         => 'Saluran',
                    'customer-group'  => 'Grup Pelanggan',
                    'email-template'  => 'Template Email',
                    'event'           => 'Acara',
                    'general'         => 'Umum',
                    'inactive'        => 'Tidak Aktif',
                    'name'            => 'Nama',
                    'save-btn'        => 'Simpan Kampanye',
                    'select-channel'  => 'Pilih Saluran',
                    'select-event'    => 'Pilih Acara',
                    'select-group'    => 'Pilih Grup',
                    'select-status'   => 'Pilih Status',
                    'select-template' => 'Pilih Template',
                    'setting'         => 'Pengaturan',
                    'status'          => 'Status',
                    'subject'         => 'Subjek',
                    'title'           => 'Buat Kampanye',
                ],

                'edit' => [
                    'active'          => 'Aktif',
                    'audience'        => 'Audiens',
                    'back-btn'        => 'Kembali',
                    'channel'         => 'Saluran',
                    'customer-group'  => 'Grup Pelanggan',
                    'email-template'  => 'Template Email',
                    'event'           => 'Acara',
                    'general'         => 'Umum',
                    'inactive'        => 'Tidak Aktif',
                    'name'            => 'Nama',
                    'save-btn'        => 'Simpan Kampanye',
                    'select-event'    => 'Pilih Acara',
                    'select-status'   => 'Pilih Status',
                    'select-template' => 'Pilih Template',
                    'status'          => 'Status',
                    'subject'         => 'Subjek',
                    'title'           => 'Edit Kampanye',
                ],

                'create-success' => 'Kampanye berhasil dibuat.',
                'delete-failed'  => 'Gagal menghapus :name',
                'delete-success' => 'Kampanye berhasil dihapus',
                'email-campaign' => 'Kampanye Email',
                'update-success' => 'Kampanye berhasil diperbarui.',
            ],

            'events' => [
                'index' => [
                    'create-btn' => 'Buat Acara',
                    'event'      => 'Acara',
                    'title'      => 'Acara',

                    'datagrid' => [
                        'actions' => 'Tindakan',
                        'date'    => 'Tanggal',
                        'delete'  => 'Hapus',
                        'edit'    => 'Edit',
                        'id'      => 'ID',
                        'name'    => 'Nama',
                    ],

                    'create' => [
                        'date'           => 'Tanggal',
                        'delete-warning' => 'Apakah Anda yakin ingin melakukan tindakan ini?',
                        'description'    => 'Deskripsi',
                        'general'        => 'Umum',
                        'name'           => 'Nama',
                        'save-btn'       => 'Simpan Acara',
                        'success'        => 'Acara berhasil dibuat',
                        'title'          => 'Buat Acara',
                    ],

                    'edit' => [
                        'success' => 'Acara berhasil diperbarui',
                        'title'   => 'Edit Acara',
                    ],
                ],

                'delete-failed'  => 'Gagal menghapus :name',
                'delete-success' => 'Acara berhasil dihapus',
                'edit-error'     => 'Acara tidak dapat diedit',
            ],

            'subscribers' => [
                'index' => [
                    'title' => 'Langganan Newsletter',

                    'datagrid' => [
                        'actions'    => 'Tindakan',
                        'delete'     => 'Hapus',
                        'edit'       => 'Edit',
                        'email'      => 'Email',
                        'false'      => 'Tidak',
                        'id'         => 'ID',
                        'subscribed' => 'Berlangganan',
                        'true'       => 'Ya',
                    ],

                    'edit' => [
                        'back-btn'      => 'Kembali',
                        'email'         => 'Email',
                        'false'         => 'Tidak',
                        'save-btn'      => 'Simpan Pelanggan',
                        'subscribed'    => 'Berlangganan',
                        'success'       => 'Langganan newsletter berhasil diperbarui',
                        'title'         => 'Edit Pelanggan Newsletter',
                        'true'          => 'Ya',
                        'update-failed' => 'Gagal memperbarui langganan newsletter',
                    ],
                ],

                'delete-failed'  => 'Gagal menghapus pelanggan',
                'delete-success' => 'Pelanggan berhasil dihapus',
                'delete-warning' => 'Apakah Anda yakin ingin melakukan tindakan ini?',
            ],
        ],

        'promotions' => [
            'index' => [
                'cart-rule-title'    => 'Aturan Keranjang',
                'catalog-rule-title' => 'Aturan Katalog',
            ],

            'cart-rules' => [
                'index' => [
                    'create-btn' => 'Buat Aturan Keranjang',
                    'title'      => 'Aturan Keranjang',

                    'datagrid' => [
                        'active'      => 'Aktif',
                        'copy'        => 'Salin',
                        'copy-of'     => ':value',
                        'coupon-code' => 'Kode Kupon',
                        'delete'      => 'Hapus',
                        'draft'       => 'Draf',
                        'edit'        => 'Ubah',
                        'end'         => 'Berakhir',
                        'id'          => 'ID',
                        'inactive'    => 'Nonaktif',
                        'name'        => 'Nama',
                        'priority'    => 'Prioritas',
                        'start'       => 'Mulai',
                        'status'      => 'Status',
                    ],
                ],

                'create' => [
                    'action-type'                               => 'Jenis Aksi',
                    'actions'                                   => 'Aksi',
                    'add-condition'                             => 'Tambahkan Kondisi',
                    'additional'                                => 'Tambahan',
                    'all-conditions-true'                       => 'Semua Kondisi Terpenuhi',
                    'any-conditions-true'                       => 'Salah Satu Kondisi Terpenuhi',
                    'apply-to-shipping'                         => 'Terapkan ke Pengiriman',
                    'attribute-family'                          => 'Kelompok Atribut',
                    'attribute-name-children-only'              => 'Nama Atribut (Hanya Anak)',
                    'attribute-name-parent-only'                => 'Nama Atribut (Hanya Induk)',
                    'auto-generate-coupon'                      => 'Buat Kupon Secara Otomatis',
                    'back-btn'                                  => 'Kembali',
                    'buy-x-get-y-free'                          => 'Beli X Gratis Y',
                    'buy-x-quantity'                            => 'Jumlah Beli X',
                    'cart-attribute'                            => 'Atribut Keranjang',
                    'cart-item-attribute'                       => 'Atribut Item Keranjang',
                    'categories'                                => 'Kategori',
                    'channels'                                  => 'Saluran',
                    'children-categories'                       => 'Kategori (Hanya Anak)',
                    'choose-condition-to-add'                   => 'Pilih Kondisi untuk Ditambahkan',
                    'condition-type'                            => 'Jenis Kondisi',
                    'conditions'                                => 'Kondisi',
                    'contain'                                   => 'Mengandung',
                    'contains'                                  => 'Berisi',
                    'coupon-code'                               => 'Kode Kupon',
                    'coupon-type'                               => 'Jenis Kupon',
                    'create-success'                            => 'Aturan keranjang berhasil dibuat',
                    'customer-groups'                           => 'Grup Pelanggan',
                    'description'                               => 'Deskripsi',
                    'discount-amount'                           => 'Jumlah Diskon',
                    'does-not-contain'                          => 'Tidak Berisi',
                    'end-of-other-rules'                        => 'Akhir dari Aturan Lain',
                    'equals-or-greater-than'                    => 'Sama Dengan atau Lebih Besar Dari',
                    'equals-or-less-than'                       => 'Sama Dengan atau Lebih Kecil Dari',
                    'fixed-amount'                              => 'Jumlah Tetap',
                    'fixed-amount-whole-cart'                   => 'Jumlah Tetap untuk Seluruh Keranjang',
                    'free-shipping'                             => 'Pengiriman Gratis',
                    'from'                                      => 'Dari',
                    'general'                                   => 'Umum',
                    'greater-than'                              => 'Lebih Besar Dari',
                    'is-equal-to'                               => 'Sama Dengan',
                    'is-not-equal-to'                           => 'Tidak Sama Dengan',
                    'less-than'                                 => 'Lebih Kecil Dari',
                    'marketing-time'                            => 'Waktu Pemasaran',
                    'maximum-quantity-allowed-to-be-discounted' => 'Jumlah Maksimum yang Bisa Didiskon',
                    'name'                                      => 'Nama',
                    'no'                                        => 'Tidak',
                    'no-coupon'                                 => 'Tanpa Kupon',
                    'parent-categories'                         => 'Kategori (Hanya Induk)',
                    'payment-method'                            => 'Metode Pembayaran',
                    'percentage-product-price'                  => 'Persentase Harga Produk',
                    'price-in-cart'                             => 'Harga dalam Keranjang',
                    'priority'                                  => 'Prioritas',
                    'product-attribute'                         => 'Atribut Produk',
                    'qty-in-cart'                               => 'Jumlah dalam Keranjang',
                    'save-btn'                                  => 'Simpan Aturan Keranjang',
                    'settings'                                  => 'Pengaturan',
                    'shipping-country'                          => 'Negara Pengiriman',
                    'shipping-method'                           => 'Metode Pengiriman',
                    'shipping-postcode'                         => 'Kode Pos Pengiriman',
                    'shipping-state'                            => 'Provinsi Pengiriman',
                    'specific-coupon'                           => 'Kupon Spesifik',
                    'status'                                    => 'Status',
                    'subtotal'                                  => 'Subtotal',
                    'title'                                     => 'Buat Aturan Keranjang',
                    'to'                                        => 'Ke',
                    'total-items-qty'                           => 'Total Jumlah Item',
                    'total-weight'                              => 'Total Berat',
                    'uses-per-coupon'                           => 'Penggunaan Per Kupon',
                    'uses-per-customer'                         => 'Penggunaan Per Pelanggan',
                    'uses-per-customer-control-info'            => 'Hanya berlaku untuk pelanggan yang masuk.',
                    'yes'                                       => 'Ya',
                ],

                'edit' => [
                    'action-type'                               => 'Tipe Aksi',
                    'actions'                                   => 'Aksi',
                    'add-condition'                             => 'Tambah Kondisi',
                    'additional'                                => 'Tambahan',
                    'all-conditions-true'                       => 'Semua Kondisi Bernilai Benar',
                    'alphabetical'                              => 'Alfabetikal',
                    'alphanumeric'                              => 'Alfanumerik',
                    'any-conditions-true'                       => 'Salah Satu Kondisi Bernilai Benar',
                    'apply-to-shipping'                         => 'Terapkan pada Pengiriman',
                    'attribute-family'                          => 'Keluarga Atribut',
                    'attribute-name-children-only'              => 'Nama Atribut (hanya turunan)',
                    'attribute-name-parent-only'                => 'Nama Atribut (hanya induk)',
                    'auto-generate-coupon'                      => 'Buat Kupon Otomatis',
                    'back-btn'                                  => 'Kembali',
                    'buy-x-get-y-free'                          => 'Beli X Dapat Y Gratis',
                    'buy-x-quantity'                            => 'Jumlah Pembelian X',
                    'cart-attribute'                            => 'Atribut Keranjang',
                    'cart-item-attribute'                       => 'Atribut Item Keranjang',
                    'categories'                                => 'Kategori',
                    'channels'                                  => 'Saluran',
                    'children-categories'                       => 'Kategori Turunan',
                    'choose-condition-to-add'                   => 'Pilih Kondisi untuk Ditambahkan',
                    'code-format'                               => 'Format Kode',
                    'code-prefix'                               => 'Awalan Kode',
                    'code-suffix'                               => 'Akhiran Kode',
                    'condition-type'                            => 'Tipe Kondisi',
                    'conditions'                                => 'Kondisi',
                    'contain'                                   => 'Mengandung',
                    'contains'                                  => 'Mengandung',
                    'coupon-code'                               => 'Kode Kupon',
                    'coupon-length'                             => 'Panjang Kupon',
                    'coupon-qty'                                => 'Jumlah Kupon',
                    'coupon-type'                               => 'Tipe Kupon',
                    'customer-group'                            => 'Grup Pelanggan',
                    'customer-groups'                           => 'Grup-grup Pelanggan',
                    'description'                               => 'Deskripsi',
                    'discount-amount'                           => 'Jumlah Diskon',
                    'does-not-contain'                          => 'Tidak Mengandung',
                    'end-of-other-rules'                        => 'Akhir dari Aturan Lain',
                    'equals-or-greater-than'                    => 'Sama dengan atau Lebih Besar dari',
                    'equals-or-less-than'                       => 'Sama dengan atau Lebih Kecil dari',
                    'fixed-amount'                              => 'Jumlah Tetap',
                    'fixed-amount-whole-cart'                   => 'Jumlah Tetap untuk Seluruh Keranjang',
                    'free-shipping'                             => 'Pengiriman Gratis',
                    'from'                                      => 'Dari',
                    'general'                                   => 'Umum',
                    'generate'                                  => 'Buat',
                    'greater-than'                              => 'Lebih Besar dari',
                    'is-equal-to'                               => 'Sama dengan',
                    'is-not-equal-to'                           => 'Tidak Sama dengan',
                    'less-than'                                 => 'Lebih Kecil dari',
                    'marketing-time'                            => 'Waktu Pemasaran',
                    'maximum-quantity-allowed-to-be-discounted' => 'Jumlah Maksimum yang Diperbolehkan untuk Didiskon',
                    'name'                                      => 'Nama',
                    'no'                                        => 'Tidak',
                    'no-coupon'                                 => 'Tanpa Kupon',
                    'numeric'                                   => 'Numerik',
                    'parent-categories'                         => 'Kategori Induk',
                    'payment-method'                            => 'Metode Pembayaran',
                    'percentage-product-price'                  => 'Persentase dari Harga Produk',
                    'price-in-cart'                             => 'Harga di Keranjang',
                    'priority'                                  => 'Prioritas',
                    'product-attribute'                         => 'Atribut Produk',
                    'qty-in-cart'                               => 'Jumlah di Keranjang',
                    'save-btn'                                  => 'Simpan Aturan Keranjang',
                    'settings'                                  => 'Pengaturan',
                    'shipping-country'                          => 'Negara Pengiriman',
                    'shipping-method'                           => 'Metode Pengiriman',
                    'shipping-postcode'                         => 'Kode Pos Pengiriman',
                    'shipping-state'                            => 'Provinsi Pengiriman',
                    'specific-coupon'                           => 'Kupon Khusus',
                    'status'                                    => 'Status',
                    'subtotal'                                  => 'Subtotal',
                    'title'                                     => 'Edit Aturan Keranjang',
                    'to'                                        => 'Ke',
                    'total-items-qty'                           => 'Total Jumlah Item',
                    'total-weight'                              => 'Total Berat',
                    'update-success'                            => 'Aturan keranjang berhasil diperbarui',
                    'uses-per-coupon'                           => 'Jumlah Penggunaan per Kupon',
                    'uses-per-customer'                         => 'Jumlah Penggunaan per Pelanggan',
                    'uses-per-customer-control-info'            => 'Hanya berlaku untuk pelanggan yang masuk (login).',
                    'yes'                                       => 'Ya',
                ],

                'delete-failed'  => 'Gagal Menghapus Aturan Keranjang',
                'delete-success' => 'Aturan Keranjang Berhasil Dihapus',
            ],

            'catalog-rules' => [
                'index' => [
                    'create-btn' => 'Buat Aturan Katalog',
                    'title'      => 'Aturan Katalog',

                    'datagrid' => [
                        'active'   => 'Aktif',
                        'delete'   => 'Hapus',
                        'edit'     => 'Ubah',
                        'end'      => 'Berakhir',
                        'id'       => 'ID',
                        'inactive' => 'Nonaktif',
                        'name'     => 'Nama',
                        'priority' => 'Prioritas',
                        'start'    => 'Mulai',
                        'status'   => 'Status',
                    ],
                ],

                'create' => [
                    'action-type'              => 'Jenis Aksi',
                    'actions'                  => 'Aksi',
                    'add-condition'            => 'Tambahkan Kondisi',
                    'all-conditions-true'      => 'Semua Kondisi Terpenuhi',
                    'any-conditions-true'      => 'Salah Satu Kondisi Terpenuhi',
                    'attribute-family'         => 'Kelompok Atribut',
                    'back-btn'                 => 'Kembali',
                    'categories'               => 'Kategori',
                    'channels'                 => 'Saluran',
                    'choose-condition-to-add'  => 'Pilih Kondisi untuk Ditambahkan',
                    'condition-type'           => 'Jenis Kondisi',
                    'conditions'               => 'Kondisi',
                    'contain'                  => 'Mengandung',
                    'contains'                 => 'Berisi',
                    'customer-groups'          => 'Grup Pelanggan',
                    'description'              => 'Deskripsi',
                    'discount-amount'          => 'Jumlah Diskon',
                    'does-not-contain'         => 'Tidak Berisi',
                    'end-other-rules'          => 'Akhiri Aturan Lain',
                    'equals-or-greater-than'   => 'Sama Dengan atau Lebih Besar Dari',
                    'equals-or-less-than'      => 'Sama Dengan atau Lebih Kecil Dari',
                    'fixed-amount'             => 'Jumlah Tetap',
                    'from'                     => 'Dari',
                    'general'                  => 'Umum',
                    'greater-than'             => 'Lebih Besar Dari',
                    'is-equal-to'              => 'Sama Dengan',
                    'is-not-equal-to'          => 'Tidak Sama Dengan',
                    'less-than'                => 'Lebih Kecil Dari',
                    'marketing-time'           => 'Waktu Pemasaran',
                    'name'                     => 'Nama',
                    'no'                       => 'Tidak',
                    'percentage-product-price' => 'Persentase Harga Produk',
                    'priority'                 => 'Prioritas',
                    'product-attribute'        => 'Atribut Produk',
                    'save-btn'                 => 'Simpan Aturan Katalog',
                    'settings'                 => 'Pengaturan',
                    'status'                   => 'Status',
                    'title'                    => 'Buat Aturan Katalog',
                    'to'                       => 'Ke',
                    'yes'                      => 'Ya',
                ],

                'edit' => [
                    'action-type'              => 'Jenis Aksi',
                    'actions'                  => 'Aksi',
                    'add-condition'            => 'Tambahkan Kondisi',
                    'all-conditions-true'      => 'Semua Kondisi Terpenuhi',
                    'any-conditions-true'      => 'Salah Satu Kondisi Terpenuhi',
                    'back-btn'                 => 'Kembali',
                    'categories'               => 'Kategori',
                    'channels'                 => 'Saluran',
                    'choose-condition-to-add'  => 'Pilih Kondisi untuk Ditambahkan',
                    'condition-type'           => 'Jenis Kondisi',
                    'conditions'               => 'Kondisi',
                    'contain'                  => 'Mengandung',
                    'contains'                 => 'Berisi',
                    'customer-groups'          => 'Grup Pelanggan',
                    'description'              => 'Deskripsi',
                    'discount-amount'          => 'Jumlah Diskon',
                    'does-not-contain'         => 'Tidak Berisi',
                    'end-other-rules'          => 'Akhiri Aturan Lain',
                    'equals-or-greater-than'   => 'Sama Dengan atau Lebih Besar Dari',
                    'equals-or-less-than'      => 'Sama Dengan atau Lebih Kecil Dari',
                    'fixed-amount'             => 'Jumlah Tetap',
                    'from'                     => 'Dari',
                    'general'                  => 'Umum',
                    'greater-than'             => 'Lebih Besar Dari',
                    'is-equal-to'              => 'Sama Dengan',
                    'is-not-equal-to'          => 'Tidak Sama Dengan',
                    'less-than'                => 'Lebih Kecil Dari',
                    'marketing-time'           => 'Waktu Pemasaran',
                    'name'                     => 'Nama',
                    'no'                       => 'Tidak',
                    'percentage-product-price' => 'Persentase Harga Produk',
                    'priority'                 => 'Prioritas',
                    'product-attribute'        => 'Atribut Produk',
                    'save-btn'                 => 'Simpan Aturan Katalog',
                    'settings'                 => 'Pengaturan',
                    'status'                   => 'Status',
                    'title'                    => 'Ubah Aturan Katalog',
                    'to'                       => 'Ke',
                    'yes'                      => 'Ya',
                ],

                'create-success' => 'Aturan katalog berhasil dibuat',
                'delete-success' => 'Aturan katalog berhasil dihapus',
                'update-success' => 'Aturan katalog berhasil diperbarui',
            ],

            'cart-rules-coupons' => [
                'cart-rule-not-defined-error' => 'Aturan keranjang tidak dapat dihapus',
                'delete-success'              => 'Kupon Aturan Keranjang Berhasil Dihapus',
                'mass-delete-success'         => 'Item yang dipilih berhasil dihapus',
                'success'                     => ':name Berhasil Dibuat',

                'datagrid' => [
                    'coupon-code'     => 'Kode Kupon',
                    'created-date'    => 'Tanggal Dibuat',
                    'delete'          => 'Hapus',
                    'expiration-date' => 'Tanggal Kedaluwarsa',
                    'id'              => 'ID',
                    'times-used'      => 'Jumlah Penggunaan',
                ],
            ],
        ],

        'search-seo' => [
            'search-terms' => [
                'index' => [
                    'create-btn' => 'Buat Istilah Pencarian',
                    'title'      => 'Istilah Pencarian',

                    'datagrid' => [
                        'uses'                => 'Penggunaan',
                        'search-query'        => 'Kata Kunci Pencarian',
                        'results'             => 'Hasil',
                        'redirect-url'        => 'URL Pengalihan',
                        'mass-delete-success' => 'Istilah Pencarian yang Dipilih Berhasil Dihapus',
                        'locale'              => 'Bahasa',
                        'id'                  => 'ID',
                        'edit'                => 'Edit',
                        'delete'              => 'Hapus',
                        'channel'             => 'Saluran',
                        'actions'             => 'Aksi',
                    ],

                    'create' => [
                        'channel'        => 'Saluran',
                        'delete-warning' => 'Apakah Anda yakin ingin melakukan tindakan ini?',
                        'locale'         => 'Bahasa',
                        'redirect-url'   => 'URL Pengalihan',
                        'results'        => 'Hasil',
                        'save-btn'       => 'Simpan Istilah Pencarian',
                        'search-query'   => 'Kata Kunci Pencarian',
                        'success'        => 'Istilah Pencarian Berhasil Dibuat',
                        'title'          => 'Buat Istilah Pencarian',
                        'uses'           => 'Penggunaan',
                    ],

                    'edit' => [
                        'delete-success' => 'Istilah Pencarian Berhasil Dihapus',
                        'success'        => 'Istilah Pencarian Berhasil Diperbarui',
                        'title'          => 'Edit Istilah Pencarian',
                    ],
                ],
            ],

            'search-synonyms' => [
                'index' => [
                    'create-btn' => 'Buat Sinonim Pencarian',
                    'title'      => 'Sinonim Pencarian',

                    'datagrid' => [
                        'actions'             => 'Aksi',
                        'delete'              => 'Hapus',
                        'edit'                => 'Edit',
                        'id'                  => 'ID',
                        'mass-delete-success' => 'Sinonim Pencarian yang Dipilih Berhasil Dihapus',
                        'name'                => 'Nama',
                        'terms'               => 'Istilah',
                    ],

                    'create' => [
                        'delete-warning' => 'Apakah Anda yakin ingin melakukan tindakan ini?',
                        'name'           => 'Nama',
                        'save-btn'       => 'Simpan Sinonim Pencarian',
                        'success'        => 'Sinonim Pencarian Berhasil Dibuat',
                        'terms'          => 'Istilah',
                        'terms-info'     => 'Masukkan sinonim dalam bentuk daftar yang dipisahkan dengan koma, misalnya, "sepatu, alas kaki." Ini akan memperluas pencarian agar mencakup kedua istilah.',
                        'title'          => 'Buat Sinonim Pencarian',
                    ],

                    'edit' => [
                        'delete-success' => 'Sinonim Pencarian Berhasil Dihapus',
                        'success'        => 'Sinonim Pencarian Berhasil Diperbarui',
                        'title'          => 'Edit Sinonim Pencarian',
                    ],
                ],
            ],

            'sitemaps' => [
                'index' => [
                    'create-btn' => 'Buat Peta Situs',
                    'sitemap'    => 'Peta Situs',
                    'title'      => 'Peta Situs',

                    'datagrid' => [
                        'actions'         => 'Aksi',
                        'delete'          => 'Hapus',
                        'edit'            => 'Edit',
                        'file-name'       => 'Nama File',
                        'id'              => 'ID',
                        'link-for-google' => 'Tautan untuk Google',
                        'path'            => 'Jalur',
                    ],

                    'create' => [
                        'delete-warning' => 'Apakah Anda yakin ingin melakukan tindakan ini?',
                        'file-name'      => 'Nama File',
                        'file-name-info' => 'Contoh: sitemap.xml',
                        'path'           => 'Jalur',
                        'path-info'      => 'Contoh: "/sitemap/" atau "/" untuk jalur dasar',
                        'save-btn'       => 'Simpan Peta Situs',
                        'success'        => 'Peta Situs Berhasil Dibuat',
                        'title'          => 'Buat Peta Situs',
                    ],

                    'edit' => [
                        'delete-success' => 'Peta Situs Berhasil Dihapus',
                        'success'        => 'Peta Situs Berhasil Diperbarui',
                        'title'          => 'Edit Peta Situs',
                    ],
                ],

                'edit' => [
                    'back-btn'       => 'Kembali',
                    'file-name'      => 'Nama File',
                    'file-name-info' => 'Contoh: sitemap.xml',
                    'general'        => 'Umum',
                    'path'           => 'Jalur',
                    'path-info'      => 'Contoh: "/sitemap/" atau "/" untuk jalur dasar',
                    'save-btn'       => 'Simpan Peta Situs',
                ],

                'delete-failed' => 'Gagal menghapus :name',
            ],

            'url-rewrites' => [
                'index' => [
                    'create-btn' => 'Buat Pengalihan URL',
                    'title'      => 'Pengalihan URL',

                    'datagrid' => [
                        'actions'             => 'Tindakan',
                        'category'            => 'Kategori',
                        'cms-page'            => 'Halaman CMS',
                        'delete'              => 'Hapus',
                        'edit'                => 'Edit',
                        'for'                 => 'Untuk',
                        'id'                  => 'ID',
                        'locale'              => 'Lokal',
                        'mass-delete-success' => 'Pengalihan URL yang dipilih berhasil dihapus',
                        'permanent-redirect'  => 'Permanen (301)',
                        'product'             => 'Produk',
                        'redirect-type'       => 'Jenis Pengalihan',
                        'request-path'        => 'Jalur Permintaan',
                        'target-path'         => 'Jalur Tujuan',
                        'temporary-redirect'  => 'Sementara (302)',
                    ],

                    'create' => [
                        'category'           => 'Kategori',
                        'cms-page'           => 'Halaman CMS',
                        'delete-warning'     => 'Apakah Anda yakin ingin melakukan tindakan ini?',
                        'for'                => 'Untuk',
                        'locale'             => 'Lokal',
                        'permanent-redirect' => 'Permanen (301)',
                        'product'            => 'Produk',
                        'redirect-type'      => 'Jenis Pengalihan',
                        'request-path'       => 'Jalur Permintaan',
                        'save-btn'           => 'Simpan Pengalihan URL',
                        'success'            => 'Pengalihan URL berhasil dibuat',
                        'target-path'        => 'Jalur Tujuan',
                        'temporary-redirect' => 'Sementara (302)',
                        'title'              => 'Buat Pengalihan URL',
                    ],

                    'edit' => [
                        'delete-success' => 'Pengalihan URL berhasil dihapus',
                        'success'        => 'Pengalihan URL berhasil diperbarui',
                        'title'          => 'Edit Pengalihan URL',
                    ],
                ],
            ],
        ],
    ],

    'cms' => [
        'index' => [
            'already-taken' => ':name sudah digunakan.',
            'create-btn'    => 'Buat Halaman',
            'channel'       => 'Saluran',
            'language'      => 'Bahasa',
            'title'         => 'Halaman',

            'datagrid' => [
                'channel'             => 'Saluran',
                'delete'              => 'Hapus',
                'edit'                => 'Edit',
                'id'                  => 'ID',
                'mass-delete-success' => 'Data yang dipilih berhasil dihapus',
                'page-title'          => 'Judul Halaman',
                'url-key'             => 'Kunci URL',
                'view'                => 'Lihat',
            ],
        ],

        'create' => [
            'channels'         => 'Saluran',
            'content'          => 'Konten',
            'description'      => 'Deskripsi',
            'general'          => 'Umum',
            'meta-description' => 'Deskripsi Meta',
            'meta-keywords'    => 'Kata Kunci Meta',
            'meta-title'       => 'Judul Meta',
            'page-title'       => 'Judul',
            'save-btn'         => 'Simpan Halaman',
            'seo'              => 'SEO',
            'title'            => 'Buat Halaman',
            'url-key'          => 'Kunci URL',
        ],

        'edit' => [
            'back-btn'         => 'Kembali',
            'channels'         => 'Saluran',
            'content'          => 'Konten',
            'description'      => 'Deskripsi',
            'general'          => 'Umum',
            'meta-description' => 'Deskripsi Meta',
            'meta-keywords'    => 'Kata Kunci Meta',
            'meta-title'       => 'Judul Meta',
            'page-title'       => 'Judul Halaman',
            'preview-btn'      => 'Pratinjau Halaman',
            'save-btn'         => 'Simpan Halaman',
            'seo'              => 'SEO',
            'title'            => 'Edit Halaman',
            'url-key'          => 'Kunci URL',
        ],

        'create-success' => 'CMS berhasil dibuat.',
        'delete-success' => 'CMS berhasil dihapus.',
        'no-resource'    => 'Sumber daya tidak ditemukan.',
        'update-success' => 'CMS berhasil diperbarui.',
    ],

    'settings' => [
        'locales' => [
            'index' => [
                'create-btn' => 'Buat Lokal',
                'locale'     => 'Lokal',
                'logo-size'  => 'Resolusi gambar harus 24px X 16px',
                'title'      => 'Lokal',

                'datagrid' => [
                    'actions'   => 'Tindakan',
                    'code'      => 'Kode',
                    'delete'    => 'Hapus',
                    'direction' => 'Arah',
                    'edit'      => 'Edit',
                    'id'        => 'ID',
                    'ltr'       => 'Kiri ke Kanan (LTR)',
                    'name'      => 'Nama',
                    'rtl'       => 'Kanan ke Kiri (RTL)',
                ],

                'create' => [
                    'code'             => 'Kode',
                    'direction'        => 'Arah',
                    'locale-logo'      => 'Logo Lokal',
                    'name'             => 'Nama',
                    'save-btn'         => 'Simpan Lokal',
                    'select-direction' => 'Pilih Arah',
                    'title'            => 'Buat Lokal',
                ],

                'edit' => [
                    'title' => 'Edit Lokal',
                ],

                'create-success'    => 'Lokal berhasil dibuat.',
                'delete-failed'     => 'Gagal menghapus lokal.',
                'delete-success'    => 'Lokal berhasil dihapus.',
                'delete-warning'    => 'Apakah Anda yakin ingin melakukan tindakan ini?',
                'last-delete-error' => 'Setidaknya satu lokal harus tersedia.',
                'update-success'    => 'Lokal berhasil diperbarui.',
            ],
        ],

        'currencies' => [
            'index' => [
                'create-btn' => 'Buat Mata Uang',
                'currency'   => 'Mata Uang',
                'title'      => 'Mata Uang',

                'datagrid' => [
                    'actions'        => 'Tindakan',
                    'code'           => 'Kode',
                    'delete'         => 'Hapus',
                    'edit'           => 'Edit',
                    'id'             => 'ID',
                    'method-error'   => 'Kesalahan! Metode yang salah terdeteksi, silakan periksa konfigurasi aksi massal',
                    'name'           => 'Nama',
                    'no-resource'    => 'Sumber daya yang diberikan tidak mencukupi untuk tindakan ini',
                    'partial-action' => 'Beberapa tindakan tidak dapat dilakukan karena batasan sistem pada :resource',
                    'update-success' => ':resource yang dipilih berhasil diperbarui',
                ],

                'create' => [
                    'code'                   => 'Kode',
                    'create-btn'             => 'Buat Mata Uang',
                    'currency-position'      => 'Posisi Simbol Mata Uang',
                    'decimal'                => 'Desimal',
                    'decimal-separator'      => 'Pemisah Desimal',
                    'decimal-separator-note' => 'Kolom :attribute hanya boleh menggunakan karakter koma (,) atau titik (.)',
                    'delete-warning'         => 'Apakah Anda yakin ingin melakukan tindakan ini?',
                    'general'                => 'Umum',
                    'group-separator'        => 'Pemisah Kelompok Angka',
                    'group-separator-note'   => 'Kolom :attribute hanya boleh menggunakan karakter koma (,), titik (.), apostrof (\'), atau spasi ( )',
                    'name'                   => 'Nama',
                    'save-btn'               => 'Simpan Mata Uang',
                    'symbol'                 => 'Simbol',
                    'title'                  => 'Buat Mata Uang Baru',
                ],

                'edit' => [
                    'title' => 'Edit Mata Uang',
                ],

                'create-success'    => 'Mata uang berhasil dibuat.',
                'delete-failed'     => 'Gagal menghapus mata uang.',
                'delete-success'    => 'Mata uang berhasil dihapus.',
                'last-delete-error' => 'Setidaknya satu mata uang harus ada.',
                'update-success'    => 'Mata uang berhasil diperbarui.',
            ],
        ],

        'data-transfer' => [
            'imports' => [
                'create' => [
                    'action'              => 'Aksi',
                    'allowed-errors'      => 'Kesalahan yang Diperbolehkan',
                    'back-btn'            => 'Kembali',
                    'create-update'       => 'Buat/Perbarui',
                    'delete'              => 'Hapus',
                    'download-sample'     => 'Unduh Sampel',
                    'field-separator'     => 'Pemisah Kolom',
                    'file'                => 'Berkas',
                    'file-info'           => 'Gunakan path relatif ke /project-root/storage/app/import, misalnya product-images, import-images.',
                    'file-info-example'   => 'Contohnya, untuk product-images, berkas harus ditempatkan di folder /project-root/storage/app/import/product-images.',
                    'general'             => 'Umum',
                    'images-directory'    => 'Path Direktori Gambar',
                    'process-in-queue'    => 'Proses dalam Antrian',
                    'results'             => 'Hasil',
                    'save-btn'            => 'Simpan Impor',
                    'settings'            => 'Pengaturan',
                    'skip-errors'         => 'Lewati Kesalahan',
                    'stop-on-errors'      => 'Berhenti pada Kesalahan',
                    'title'               => 'Buat Impor',
                    'type'                => 'Tipe',
                    'validation-strategy' => 'Strategi Validasi',
                ],

                'edit' => [
                    'action'              => 'Aksi',
                    'allowed-errors'      => 'Kesalahan yang Diperbolehkan',
                    'back-btn'            => 'Kembali',
                    'create-update'       => 'Buat/Perbarui',
                    'current-file'        => 'Berkas yang Diupload Saat Ini',
                    'delete'              => 'Hapus',
                    'download-sample'     => 'Unduh Sampel',
                    'field-separator'     => 'Pemisah Kolom',
                    'file'                => 'Berkas',
                    'file-info'           => 'Gunakan path relatif ke /project-root/storage/app/import, misalnya product-images, import-images.',
                    'file-info-example'   => 'Contohnya, untuk product-images, berkas harus ditempatkan di folder /project-root/storage/app/import/product-images.',
                    'general'             => 'Umum',
                    'images-directory'    => 'Path Direktori Gambar',
                    'process-in-queue'    => 'Proses dalam Antrian',
                    'results'             => 'Hasil',
                    'save-btn'            => 'Simpan Impor',
                    'settings'            => 'Pengaturan',
                    'skip-errors'         => 'Lewati Kesalahan',
                    'stop-on-errors'      => 'Berhenti pada Kesalahan',
                    'title'               => 'Edit Impor',
                    'type'                => 'Tipe',
                    'validation-strategy' => 'Strategi Validasi',
                ],

                'index' => [
                    'button-title' => 'Buat Impor',
                    'title'        => 'Impor',

                    'datagrid' => [
                        'actions'       => 'Aksi',
                        'completed-at'  => 'Selesai Pada',
                        'created'       => 'Dibuat',
                        'delete'        => 'Hapus',
                        'deleted'       => 'Dihapus',
                        'edit'          => 'Edit',
                        'error-file'    => 'Berkas Kesalahan',
                        'id'            => 'ID',
                        'started-at'    => 'Dimulai Pada',
                        'state'         => 'Status',
                        'summary'       => 'Ringkasan',
                        'updated'       => 'Diperbarui',
                        'uploaded-file' => 'Berkas yang Diupload',
                    ],
                ],

                'import' => [
                    'back-btn'                => 'Kembali',
                    'completed-batches'       => 'Total Batch yang Selesai:',
                    'download-error-report'   => 'Unduh Laporan Lengkap',
                    'edit-btn'                => 'Edit',
                    'imported-info'           => 'Selamat! Impor Anda berhasil.',
                    'importing-info'          => 'Proses Impor Sedang Berjalan',
                    'indexing-info'           => 'Indeks Sumber Daya (Harga, Inventaris, dan Elastic Search) Sedang Berlangsung',
                    'linking-info'            => 'Penghubungan Sumber Daya Sedang Berlangsung',
                    'progress'                => 'Progres:',
                    'title'                   => 'Impor',
                    'total-batches'           => 'Total Batch:',
                    'total-created'           => 'Total Rekaman Dibuat:',
                    'total-deleted'           => 'Total Rekaman Dihapus:',
                    'total-errors'            => 'Total Kesalahan:',
                    'total-invalid-rows'      => 'Total Baris Tidak Valid:',
                    'total-rows-processed'    => 'Total Baris Diproses:',
                    'total-updated'           => 'Total Rekaman Diperbarui:',
                    'validate'                => 'Validasi',
                    'validate-info'           => 'Klik Validasi Data untuk memeriksa impor Anda.',
                    'validating-info'         => 'Data mulai dibaca dan divalidasi',
                    'validation-failed-info'  => 'Impor Anda tidak valid. Harap perbaiki kesalahan berikut dan coba lagi.',
                    'validation-success-info' => 'Impor Anda valid. Klik Impor untuk memulai proses impor.',
                ],

                'create-success'    => 'Impor berhasil dibuat.',
                'delete-failed'     => 'Penghapusan impor gagal secara tidak terduga.',
                'delete-success'    => 'Impor berhasil dihapus.',
                'not-valid'         => 'Impor tidak valid',
                'nothing-to-import' => 'Tidak ada sumber daya untuk diimpor.',
                'setup-queue-error' => 'Harap ubah driver antrian Anda ke "database" atau "redis" untuk memulai proses impor.',
                'update-success'    => 'Impor berhasil diperbarui.',
            ],
        ],

        'exchange-rates' => [
            'index' => [
                'create-btn'    => 'Buat Kurs Valuta',
                'exchange-rate' => 'Kurs Valuta',
                'title'         => 'Kurs Valuta',
                'update-rates'  => 'Perbarui Kurs Valuta',

                'create' => [
                    'delete-warning'         => 'Apakah Anda yakin ingin melakukan aksi ini?',
                    'rate'                   => 'Kurs',
                    'save-btn'               => 'Simpan Kurs Valuta',
                    'select-target-currency' => 'Pilih Mata Uang Tujuan',
                    'source-currency'        => 'Mata Uang Sumber',
                    'target-currency'        => 'Mata Uang Tujuan',
                    'title'                  => 'Buat Kurs Valuta',
                ],

                'edit' => [
                    'title' => 'Edit Kurs Valuta',
                ],

                'datagrid' => [
                    'actions'       => 'Aksi',
                    'currency-name' => 'Nama Mata Uang',
                    'delete'        => 'Hapus',
                    'edit'          => 'Edit',
                    'exchange-rate' => 'Kurs Valuta',
                    'id'            => 'ID',
                ],

                'create-success' => 'Kurs Valuta Berhasil Dibuat',
                'delete-error'   => 'Terjadi Kesalahan saat Menghapus Kurs Valuta',
                'delete-success' => 'Kurs Valuta Berhasil Dihapus',
                'update-success' => 'Kurs Valuta Berhasil Diperbarui',
            ],
        ],

        'inventory-sources' => [
            'index' => [
                'create-btn' => 'Buat Sumber Inventaris',
                'title'      => 'Sumber Inventaris',

                'datagrid' => [
                    'active'   => 'Aktif',
                    'code'     => 'Kode',
                    'delete'   => 'Hapus',
                    'edit'     => 'Edit',
                    'id'       => 'ID',
                    'inactive' => 'Tidak Aktif',
                    'name'     => 'Nama',
                    'priority' => 'Prioritas',
                    'status'   => 'Status',
                ],
            ],

            'create' => [
                'add-title'      => 'Tambah Sumber Inventaris',
                'address'        => 'Alamat Sumber',
                'back-btn'       => 'Kembali',
                'city'           => 'Kota',
                'code'           => 'Kode',
                'contact-email'  => 'Email',
                'contact-fax'    => 'Fax',
                'contact-info'   => 'Informasi Kontak',
                'contact-name'   => 'Nama',
                'contact-number' => 'Nomor Kontak',
                'country'        => 'Negara',
                'description'    => 'Deskripsi',
                'general'        => 'Umum',
                'latitude'       => 'Lintang',
                'longitude'      => 'Bujur',
                'name'           => 'Nama',
                'postcode'       => 'Kode Pos',
                'priority'       => 'Prioritas',
                'save-btn'       => 'Simpan Sumber Inventaris',
                'select-country' => 'Pilih Negara',
                'select-state'   => 'Pilih Provinsi',
                'settings'       => 'Pengaturan',
                'state'          => 'Provinsi',
                'status'         => 'Status',
                'street'         => 'Jalan',
                'title'          => 'Sumber Inventaris',
            ],

            'edit' => [
                'back-btn'       => 'Kembali',
                'city'           => 'Kota',
                'code'           => 'Kode',
                'contact-email'  => 'Email',
                'contact-fax'    => 'Fax',
                'contact-info'   => 'Informasi Kontak',
                'contact-name'   => 'Nama',
                'contact-number' => 'Nomor Kontak',
                'country'        => 'Negara',
                'description'    => 'Deskripsi',
                'general'        => 'Umum',
                'latitude'       => 'Lintang',
                'longitude'      => 'Bujur',
                'name'           => 'Nama',
                'postcode'       => 'Kode Pos',
                'priority'       => 'Prioritas',
                'save-btn'       => 'Simpan Sumber Inventaris',
                'select-country' => 'Pilih Negara',
                'select-state'   => 'Pilih Provinsi',
                'settings'       => 'Pengaturan',
                'source-address' => 'Alamat Sumber',
                'state'          => 'Provinsi',
                'status'         => 'Status',
                'street'         => 'Jalan',
                'title'          => 'Edit Sumber Inventaris',
            ],

            'create-success'    => 'Sumber Inventaris Berhasil Dibuat',
            'delete-failed'     => 'Penghapusan Sumber Inventaris Gagal',
            'delete-success'    => 'Sumber Inventaris Berhasil Dihapus',
            'last-delete-error' => 'Sumber Inventaris Terakhir Tidak Bisa Dihapus',
            'update-success'    => 'Sumber Inventaris Berhasil Diperbarui',
        ],

        'taxes' => [
            'categories' => [
                'index' => [
                    'delete-warning' => 'Apakah Anda yakin ingin menghapus?',
                    'tax-category'   => 'Kategori Pajak',
                    'title'          => 'Kategori Pajak',

                    'datagrid' => [
                        'actions' => 'Aksi',
                        'code'    => 'Kode',
                        'delete'  => 'Hapus',
                        'edit'    => 'Edit',
                        'id'      => 'ID',
                        'name'    => 'Nama',
                    ],

                    'create' => [
                        'add-tax-rates' => 'Tambah Tarif Pajak',
                        'code'          => 'Kode',
                        'description'   => 'Deskripsi',
                        'empty-text'    => 'Tarif Pajak tidak tersedia, silakan buat Tarif Pajak baru.',
                        'general'       => 'Kategori Pajak',
                        'name'          => 'Nama',
                        'save-btn'      => 'Simpan Kategori Pajak',
                        'select'        => 'Pilih',
                        'tax-rates'     => 'Tarif Pajak',
                        'title'         => 'Buat Kategori Pajak',
                    ],

                    'edit' => [
                        'title' => 'Edit Kategori Pajak',
                    ],

                    'can-not-delete' => 'Kategori dengan Tarif Pajak yang Terpasang tidak bisa dihapus.',
                    'create-success' => 'Kategori Pajak Baru Berhasil Dibuat.',
                    'delete-failed'  => 'Penghapusan Kategori Pajak Gagal.',
                    'delete-success' => 'Kategori Pajak Berhasil Dihapus.',
                    'update-success' => 'Kategori Pajak Berhasil Diperbarui.',
                ],
            ],

            'rates' => [
                'index' => [
                    'button-title' => 'Buat Tarif Pajak',
                    'tax-rate'     => 'Tarif Pajak',
                    'title'        => 'Tarif Pajak',

                    'datagrid' => [
                        'country'    => 'Negara',
                        'delete'     => 'Hapus',
                        'edit'       => 'Edit',
                        'id'         => 'ID',
                        'identifier' => 'Identifikasi',
                        'state'      => 'Provinsi',
                        'tax-rate'   => 'Tarif Pajak',
                        'zip-code'   => 'Kode Pos',
                        'zip-from'   => 'Kode Pos Dari',
                        'zip-to'     => 'Kode Pos Ke',
                    ],
                ],

                'create' => [
                    'back-btn'       => 'Kembali',
                    'country'        => 'Negara',
                    'general'        => 'Umum',
                    'identifier'     => 'Identifikasi',
                    'is-zip'         => 'Aktifkan Rentang Kode Pos',
                    'save-btn'       => 'Simpan Tarif Pajak',
                    'select-country' => 'Pilih Negara',
                    'select-state'   => 'Pilih Provinsi',
                    'settings'       => 'Pengaturan',
                    'state'          => 'Provinsi',
                    'tax-rate'       => 'Tarif',
                    'title'          => 'Buat Tarif Pajak',
                    'zip-code'       => 'Kode Pos',
                    'zip-from'       => 'Kode Pos Dari',
                    'zip-to'         => 'Kode Pos Ke',
                ],

                'edit' => [
                    'back-btn'       => 'Kembali',
                    'country'        => 'Negara',
                    'identifier'     => 'Identifikasi',
                    'save-btn'       => 'Simpan Tarif Pajak',
                    'select-country' => 'Pilih Negara',
                    'select-state'   => 'Pilih Provinsi',
                    'settings'       => 'Pengaturan',
                    'state'          => 'Provinsi',
                    'tax-rate'       => 'Tarif',
                    'title'          => 'Edit Tarif Pajak',
                    'zip-code'       => 'Kode Pos',
                    'zip-from'       => 'Kode Pos Dari',
                    'zip-to'         => 'Kode Pos Ke',
                ],

                'create-success' => 'Tarif Pajak Berhasil Dibuat.',
                'delete-failed'  => 'Penghapusan Tarif Pajak Gagal',
                'delete-success' => 'Tarif Pajak Berhasil Dihapus',
                'update-success' => 'Tarif Pajak Berhasil Diperbarui',
            ],
        ],

        'channels' => [
            'index' => [
                'create-btn'        => 'Buat Channel',
                'delete-failed'     => 'Penghapusan Channel Gagal',
                'delete-success'    => 'Channel berhasil dihapus.',
                'last-delete-error' => 'Penghapusan Channel terakhir gagal.',
                'title'             => 'Channel',

                'datagrid' => [
                    'code'      => 'Kode',
                    'delete'    => 'Hapus',
                    'edit'      => 'Edit',
                    'host-name' => 'Nama Host',
                    'id'        => 'ID',
                    'name'      => 'Nama',
                ],
            ],

            'create' => [
                'allowed-ips'             => 'IP yang Diizinkan',
                'cancel'                  => 'Kembali',
                'code'                    => 'Kode',
                'create-success'          => 'Channel berhasil dibuat.',
                'currencies'              => 'Mata Uang',
                'currencies-and-locales'  => 'Mata Uang dan Lokasi',
                'default-currency'        => 'Mata Uang Default',
                'default-locale'          => 'Lokasi Default',
                'description'             => 'Deskripsi',
                'design'                  => 'Desain',
                'favicon'                 => 'Favicon',
                'favicon-size'            => 'Resolusi gambar harus seperti 16px X 16px',
                'general'                 => 'Umum',
                'hostname'                => 'Nama Host',
                'hostname-placeholder'    => 'https://www.example.com (Jangan menambahkan garis miring di akhir.)',
                'inventory-sources'       => 'Sumber Inventaris',
                'last-delete-error'       => 'Setidaknya satu Channel diperlukan.',
                'locales'                 => 'Lokasi',
                'logo'                    => 'Logo',
                'logo-size'               => 'Resolusi gambar harus seperti 192px X 50px',
                'maintenance-mode-text'   => 'Pesan',
                'name'                    => 'Nama',
                'root-category'           => 'Kategori Utama',
                'save-btn'                => 'Simpan Channel',
                'select-default-currency' => 'Pilih Mata Uang Default',
                'select-default-locale'   => 'Pilih Lokasi Default',
                'select-root-category'    => 'Pilih Kategori Utama',
                'select-theme'            => 'Pilih Tema',
                'seo'                     => 'SEO halaman utama',
                'seo-description'         => 'Deskripsi meta',
                'seo-keywords'            => 'Kata kunci meta',
                'seo-title'               => 'Judul meta',
                'settings'                => 'Pengaturan',
                'status'                  => 'Status',
                'theme'                   => 'Tema',
                'title'                   => 'Buat Channel',
            ],

            'edit' => [
                'allowed-ips'            => 'IP yang Diizinkan',
                'back-btn'               => 'Kembali',
                'code'                   => 'Kode',
                'currencies'             => 'Mata Uang',
                'currencies-and-locales' => 'Mata Uang dan Lokasi',
                'default-currency'       => 'Mata Uang Default',
                'default-locale'         => 'Lokasi Default',
                'description'            => 'Deskripsi',
                'design'                 => 'Desain',
                'favicon'                => 'Favicon',
                'favicon-size'           => 'Resolusi gambar harus seperti 16px X 16px',
                'general'                => 'Umum',
                'hostname'               => 'Nama Host',
                'hostname-placeholder'   => 'https://www.example.com (Jangan menambahkan garis miring di akhir.)',
                'inventory-sources'      => 'Sumber Inventaris',
                'last-delete-error'      => 'Setidaknya satu Channel diperlukan.',
                'locales'                => 'Lokasi',
                'logo'                   => 'Logo',
                'logo-size'              => 'Resolusi gambar harus seperti 192px X 50px',
                'maintenance-mode'       => 'Mode Pemeliharaan',
                'maintenance-mode-text'  => 'Pesan',
                'name'                   => 'Nama',
                'root-category'          => 'Kategori Utama',
                'save-btn'               => 'Simpan Channel',
                'seo'                    => 'SEO halaman utama',
                'seo-description'        => 'Deskripsi meta',
                'seo-keywords'           => 'Kata kunci meta',
                'seo-title'              => 'Judul meta',
                'status'                 => 'Status',
                'theme'                  => 'Tema',
                'title'                  => 'Edit Channel',
                'update-success'         => 'Channel berhasil diperbarui',
            ],
        ],

        'users' => [
            'index' => [
                'admin' => 'Admin',
                'title' => 'Pengguna',
                'user'  => 'Pengguna',

                'create' => [
                    'confirm-password'  => 'Konfirmasi Kata Sandi',
                    'email'             => 'Email',
                    'name'              => 'Nama',
                    'password'          => 'Kata Sandi',
                    'role'              => 'Peran',
                    'save-btn'          => 'Simpan Pengguna',
                    'status'            => 'Status',
                    'title'             => 'Buat Pengguna',
                    'upload-image-info' => 'Unggah Gambar Profil (110px X 110px) dalam Format PNG atau JPG',
                ],

                'datagrid' => [
                    'actions'  => 'Aksi',
                    'active'   => 'Aktif',
                    'delete'   => 'Hapus',
                    'edit'     => 'Edit',
                    'email'    => 'Email',
                    'id'       => 'ID',
                    'inactive' => 'Tidak Aktif',
                    'name'     => 'Nama',
                    'role'     => 'Peran',
                    'status'   => 'Status',
                ],

                'edit' => [
                    'title' => 'Edit Pengguna',
                ],
            ],

            'edit' => [
                'back-btn'         => 'Kembali',
                'confirm-password' => 'Konfirmasi Kata Sandi',
                'email'            => 'Email',
                'general'          => 'Umum',
                'name'             => 'Nama',
                'password'         => 'Kata Sandi',
                'role'             => 'Peran',
                'save-btn'         => 'Simpan Pengguna',
                'status'           => 'Status',
                'title'            => 'Edit Pengguna',
            ],

            'activate-warning'   => 'Akun Anda belum diaktifkan, harap hubungi administrator.',
            'cannot-change'      => 'Pengguna tidak dapat diubah',
            'create-success'     => 'Pengguna berhasil dibuat.',
            'delete-failed'      => 'Pengguna gagal dihapus.',
            'delete-self-error'  => 'Anda tidak bisa menghapus akun Anda sendiri.',
            'delete-success'     => 'Pengguna berhasil dihapus.',
            'delete-warning'     => 'Apakah Anda yakin ingin melakukan tindakan ini?',
            'incorrect-password' => 'Kata sandi salah',
            'last-delete-error'  => 'Setidaknya satu admin dibutuhkan.',
            'login-error'        => 'Periksa kredensial Anda dan coba lagi.',
            'update-success'     => 'Pengguna berhasil diperbarui.',
        ],

        'roles' => [
            'index' => [
                'create-btn' => 'Buat Peran',
                'title'      => 'Peran',

                'datagrid' => [
                    'all'             => 'Semua',
                    'custom'          => 'Kustom',
                    'delete'          => 'Hapus',
                    'edit'            => 'Edit',
                    'id'              => 'ID',
                    'name'            => 'Nama',
                    'permission-type' => 'Tipe Izin',
                ],
            ],

            'create' => [
                'access-control' => 'Kontrol Akses',
                'all'            => 'Semua',
                'back-btn'       => 'Kembali',
                'custom'         => 'Kustom',
                'description'    => 'Deskripsi',
                'general'        => 'Umum',
                'name'           => 'Nama',
                'permissions'    => 'Izin',
                'save-btn'       => 'Simpan Peran',
                'title'          => 'Buat Peran',
            ],

            'edit' => [
                'access-control' => 'Kontrol Akses',
                'all'            => 'Semua',
                'back-btn'       => 'Kembali',
                'custom'         => 'Kustom',
                'description'    => 'Deskripsi',
                'general'        => 'Umum',
                'name'           => 'Nama',
                'permissions'    => 'Izin',
                'save-btn'       => 'Simpan Peran',
                'title'          => 'Edit Peran',
            ],

            'being-used'        => 'Peran sudah digunakan pada Pengguna Admin',
            'create-success'    => 'Peran Berhasil Dibuat',
            'delete-failed'     => 'Peran Gagal Dihapus',
            'delete-success'    => 'Peran Berhasil Dihapus',
            'last-delete-error' => 'Peran terakhir tidak dapat dihapus',
            'update-success'    => 'Peran Berhasil Diperbarui',
        ],

        'themes' => [
            'index' => [
                'create-btn' => 'Buat Tema',
                'title'      => 'Tema',

                'datagrid' => [
                    'active'        => 'Aktif',
                    'channel_name'  => 'Nama Channel',
                    'change-status' => 'Ubah Status',
                    'delete'        => 'Hapus',
                    'id'            => 'ID',
                    'inactive'      => 'Tidak Aktif',
                    'name'          => 'Nama',
                    'sort-order'    => 'Urutan Penyortiran',
                    'status'        => 'Status',
                    'theme'         => 'Tema',
                    'type'          => 'Tipe',
                    'view'          => 'Lihat',
                ],
            ],

            'create' => [
                'name'       => 'Nama',
                'save-btn'   => 'Simpan Tema',
                'sort-order' => 'Urutan Penyortiran',
                'themes'     => 'Tema',
                'title'      => 'Buat Tema',

                'type' => [
                    'category-carousel' => 'Carousel Kategori',
                    'footer-links'      => 'Tautan Footer',
                    'image-carousel'    => 'Carousel Gambar',
                    'product-carousel'  => 'Carousel Produk',
                    'services-content'  => 'Konten Layanan',
                    'static-content'    => 'Konten Statis',
                    'title'             => 'Tipe',
                ],
            ],

            'edit' => [
                'active'                        => 'Aktif',
                'add-filter-btn'                => 'Tambah Filter',
                'add-footer-link-btn'           => 'Tambah Tautan Footer',
                'add-image-btn'                 => 'Tambah Gambar',
                'add-link'                      => 'Tambah Tautan',
                'asc'                           => 'Naik',
                'back'                          => 'Kembali',
                'category-carousel'             => 'Carousel Kategori',
                'category-carousel-description' => 'Tampilkan kategori dinamis secara menarik menggunakan carousel kategori yang responsif.',
                'channels'                      => 'Channel',
                'column'                        => 'Kolom',
                'create-filter'                 => 'Buat Filter',
                'css'                           => 'CSS',
                'delete'                        => 'Hapus',
                'desc'                          => 'Turun',
                'edit'                          => 'Edit',
                'featured'                      => 'Fitur Unggulan',
                'filter-title'                  => 'Judul Filter',
                'filters'                       => 'Filter',
                'footer-link'                   => 'Tautan Footer',
                'footer-link-description'       => 'Navigasi mudah melalui tautan footer untuk menjelajah dan menemukan informasi di situs Anda.',
                'footer-link-form-title'        => 'Tautan Footer',
                'footer-title'                  => 'Judul Footer',
                'general'                       => 'Umum',
                'html'                          => 'HTML',
                'image'                         => 'Gambar',
                'image-size'                    => 'Resolusi gambar sebaiknya (1920px X 700px)',
                'image-title'                   => 'Judul Gambar',
                'image-upload-message'          => 'Hanya berkas gambar (.jpeg, .jpg, .png, .webp, ..) yang diizinkan.',
                'inactive'                      => 'Tidak Aktif',
                'key'                           => 'Kunci: :key',
                'key-input'                     => 'Kunci',
                'limit'                         => 'Batas',
                'link'                          => 'Tautan',
                'name'                          => 'Nama',
                'new'                           => 'Baru',
                'no'                            => 'Tidak',
                'parent-id'                     => 'ID Induk',
                'parent-id-hint'                => 'Anda dapat memasukkan beberapa ID induk dengan koma sebagai pemisah (misal: 12,15,34)',
                'category-id'                   => 'ID Kategori',
                'preview'                       => 'Pratinjau',
                'product-carousel'              => 'Carousel Produk',
                'product-carousel-description'  => 'Pamerkan produk dengan elegan menggunakan carousel produk yang dinamis dan responsif.',
                'save-btn'                      => 'Simpan',
                'select'                        => 'Pilih',
                'slider'                        => 'Slider',
                'slider-add-btn'                => 'Tambah Slider',
                'slider-description'            => 'Kustomisasi tema terkait slider.',
                'slider-image'                  => 'Gambar Slider',
                'slider-required'               => 'Kolom slider diperlukan.',
                'sort'                          => 'Urutkan',
                'sort-order'                    => 'Urutan Penyortiran',
                'static-content'                => 'Konten Statis',
                'static-content-description'    => 'Tingkatkan keterlibatan dengan konten statis yang singkat dan informatif untuk audiens Anda.',
                'status'                        => 'Status',
                'themes'                        => 'Tema',
                'title'                         => 'Edit Tema',
                'update-slider'                 => 'Perbarui Slider',
                'url'                           => 'URL',
                'value'                         => 'Nilai: :value',
                'value-input'                   => 'Nilai',

                'services-content' => [
                    'add-btn'            => 'Tambah Layanan',
                    'channels'           => 'Channel',
                    'delete'             => 'Hapus',
                    'description'        => 'Deskripsi',
                    'general'            => 'Umum',
                    'name'               => 'Nama',
                    'save-btn'           => 'Simpan',
                    'service-icon'       => 'Ikon Layanan',
                    'service-icon-class' => 'Kelas Ikon Layanan',
                    'service-info'       => 'Kustomisasi tema terkait layanan.',
                    'services'           => 'Layanan',
                    'sort-order'         => 'Urutan Penyortiran',
                    'status'             => 'Status',
                    'title'              => 'Judul',
                    'update-service'     => 'Perbarui Layanan',
                ],
                'yes'                    => 'Ya',
            ],

            'create-success' => 'Tema berhasil dibuat',
            'delete-success' => 'Tema berhasil dihapus',
            'update-success' => 'Tema berhasil diperbarui',
        ],
    ],

    'reporting' => [
        'sales' => [
            'index' => [
                'abandoned-carts'               => 'Keranjang Ditinggalkan',
                'abandoned-products'            => 'Produk yang Ditinggalkan',
                'abandoned-rate'                => 'Tingkat Keranjang Ditinggalkan',
                'abandoned-revenue'             => 'Pendapatan yang Ditinggalkan',
                'added-to-cart-info'            => 'Hanya :progress pengunjung yang menambahkan produk ke keranjang',
                'added-to-cart'                 => 'Ditambahkan ke Keranjang',
                'all-channels'                  => 'Semua Channel',
                'average-order-value-over-time' => 'Rata-Rata Nilai Pesanan Seiring Waktu',
                'average-sales'                 => 'Rata-Rata Nilai Pesanan',
                'count'                         => 'Jumlah',
                'end-date'                      => 'Tanggal Akhir',
                'id'                            => 'ID',
                'interval'                      => 'Interval',
                'name'                          => 'Nama',
                'orders'                        => 'Pesanan',
                'orders-over-time'              => 'Pesanan Seiring Waktu',
                'payment-method'                => 'Metode Pembayaran',
                'product-views'                 => 'Tampilan Produk',
                'product-views-info'            => 'Hanya :progress pengunjung yang melihat produk',
                'purchase-funnel'               => 'Funnel Pembelian',
                'purchased'                     => 'Dibeli',
                'purchased-info'                => 'Hanya :progress pengunjung yang melakukan pembelian',
                'refunds'                       => 'Pengembalian Dana',
                'refunds-over-time'             => 'Pengembalian Dana Seiring Waktu',
                'sales-over-time'               => 'Penjualan Seiring Waktu',
                'shipping-collected'            => 'Pengiriman yang Diperoleh',
                'shipping-collected-over-time'  => 'Pengiriman yang Diperoleh Seiring Waktu',
                'start-date'                    => 'Tanggal Mulai',
                'tax-collected'                 => 'Pajak yang Diperoleh',
                'tax-collected-over-time'       => 'Pajak yang Diperoleh Seiring Waktu',
                'title'                         => 'Penjualan',
                'top-payment-methods'           => 'Metode Pembayaran Teratas',
                'top-shipping-methods'          => 'Metode Pengiriman Teratas',
                'top-tax-categories'            => 'Kategori Pajak Teratas',
                'total'                         => 'Total',
                'total-orders'                  => 'Total Pesanan',
                'total-sales'                   => 'Total Penjualan',
                'total-visits'                  => 'Total Kunjungan',
                'total-visits-info'             => 'Total pengunjung di toko',
                'view-details'                  => 'Lihat Detail',
            ],
        ],

        'customers' => [
            'index' => [
                'all-channels'                => 'Semua Channel',
                'count'                       => 'Jumlah',
                'customers'                   => 'Pelanggan',
                'customers-over-time'         => 'Pelanggan Seiring Waktu',
                'customers-traffic'           => 'Lalu Lintas Pelanggan',
                'customers-with-most-orders'  => 'Pelanggan dengan Pesanan Terbanyak',
                'customers-with-most-reviews' => 'Pelanggan dengan Ulasan Terbanyak',
                'customers-with-most-sales'   => 'Pelanggan dengan Penjualan Terbanyak',
                'email'                       => 'Email',
                'end-date'                    => 'Tanggal Akhir',
                'id'                          => 'ID',
                'interval'                    => 'Interval',
                'name'                        => 'Nama',
                'orders'                      => 'Pesanan',
                'reviews'                     => 'Ulasan',
                'start-date'                  => 'Tanggal Mulai',
                'title'                       => 'Pelanggan',
                'top-customer-groups'         => 'Kelompok Pelanggan Teratas',
                'total'                       => 'Total',
                'total-customers'             => 'Total Pelanggan',
                'total-visitors'              => 'Total Pengunjung',
                'traffic-over-week'           => 'Lalu Lintas Seiring Minggu',
                'unique-visitors'             => 'Pengunjung Unik',
                'view-details'                => 'Lihat Detail',
            ],
        ],

        'products' => [
            'index' => [
                'all-channels'                     => 'Semua Channel',
                'channel'                          => 'Channel',
                'end-date'                         => 'Tanggal Akhir',
                'id'                               => 'ID',
                'interval'                         => 'Interval',
                'last-search-terms'                => 'Istilah Pencarian Terakhir',
                'locale'                           => 'Lokasi',
                'name'                             => 'Nama',
                'orders'                           => 'Pesanan',
                'price'                            => 'Harga',
                'products-added-over-time'         => 'Produk Ditambahkan Seiring Waktu',
                'products-with-most-reviews'       => 'Produk dengan Ulasan Terbanyak',
                'products-with-most-visits'        => 'Produk dengan Kunjungan Terbanyak',
                'quantities'                       => 'Kuantitas',
                'quantities-sold-over-time'        => 'Kuantitas Terjual Seiring Waktu',
                'results'                          => 'Hasil',
                'revenue'                          => 'Pendapatan',
                'reviews'                          => 'Ulasan',
                'search-term'                      => 'Istilah Pencarian',
                'start-date'                       => 'Tanggal Mulai',
                'title'                            => 'Produk',
                'top-search-terms'                 => 'Istilah Pencarian Teratas',
                'top-selling-products-by-quantity' => 'Produk Terlaris Berdasarkan Kuantitas',
                'top-selling-products-by-revenue'  => 'Produk Terlaris Berdasarkan Pendapatan',
                'total'                            => 'Total',
                'total-products-added-to-wishlist' => 'Produk Ditambahkan ke Wishlist',
                'total-sold-quantities'            => 'Kuantitas Produk Terjual',
                'uses'                             => 'Penggunaan',
                'view-details'                     => 'Lihat Detail',
                'visits'                           => 'Kunjungan',
            ],
        ],

        'view' => [
            'all-channels'  => 'Semua Channel',
            'back-btn'      => 'Kembali',
            'day'           => 'Hari',
            'end-date'      => 'Tanggal Akhir',
            'export-csv'    => 'Expor CSV',
            'export-xls'    => 'Expor XLS',
            'month'         => 'Bulan',
            'not-available' => 'Tidak Ada Data Tersedia.',
            'start-date'    => 'Tanggal Mulai',
            'year'          => 'Tahun',
        ],

        'empty' => [
            'info'  => 'Tidak ada data untuk interval yang dipilih',
            'title' => 'Tidak Ada Data Tersedia',
        ],
    ],

    'configuration' => [
        'index' => [
            'back-btn'                     => 'Kembali',
            'delete'                       => 'Hapus',
            'enable-at-least-one-payment'  => 'Aktifkan setidaknya satu metode pembayaran.',
            'enable-at-least-one-shipping' => 'Aktifkan setidaknya satu metode pengiriman.',
            'no-result-found'              => 'Tidak ada hasil yang ditemukan',
            'save-btn'                     => 'Simpan Konfigurasi',
            'save-message'                 => 'Konfigurasi berhasil disimpan',
            'search'                       => 'Cari',
            'select-country'               => 'Pilih Negara',
            'select-state'                 => 'Pilih Provinsi',
            'title'                        => 'Konfigurasi',

            'general' => [
                'info'  => 'Atur opsi unit.',
                'title' => 'Umum',

                'general' => [
                    'info'  => 'Atur opsi unit dan aktifkan atau nonaktifkan breadcrumb.',
                    'title' => 'Umum',

                    'unit-options' => [
                        'info'        => 'Atur opsi unit.',
                        'title'       => 'Opsi Unit',
                        'title-info'  => 'Konfigurasi berat dalam satuan pound (lbs) atau kilogram (kgs).',
                        'weight-unit' => 'Satuan Berat',
                    ],

                    'breadcrumbs' => [
                        'shop'       => 'Breadcrumbs Toko',
                        'title'      => 'Breadcrumbs',
                        'title-info' => 'Aktifkan atau nonaktifkan navigasi breadcrumb di toko.',
                    ],
                ],

                'content' => [
                    'info'  => 'Atur judul penawaran header dan skrip kustom.',
                    'title' => 'Konten',

                    'header-offer' => [
                        'title'             => 'Judul Penawaran Header',
                        'title-info'        => 'Atur judul penawaran header beserta judul pengalihan dan tautannya.',
                        'offer-title'       => 'Judul Penawaran',
                        'redirection-title' => 'Judul Pengalihan',
                        'redirection-link'  => 'Tautan Pengalihan',
                    ],

                    'speculation-rules' => [
                        'enable-speculation' => 'Aktifkan Aturan Spekulasi',
                        'info'               => 'Atur preferensi untuk mengaktifkan atau menonaktifkan logika spekulasi otomatis.',
                        'title'              => 'Aturan Spekulasi',

                        'prerender' => [
                            'conservative'           => 'Konservatif',
                            'eager'                  => 'Agresif',
                            'eagerness'              => 'Tingkat Keagresifan Prerender',
                            'eagerness-info'         => 'Mengatur seberapa agresif aturan spekulasi akan diterapkan. Pilihan: agresif (maksimum), moderat (default), konservatif (rendah).',
                            'enabled'                => 'Aktifkan Aturan Spekulasi Prerender',
                            'ignore-url-params'      => 'Abaikan Parameter URL untuk Prerender',
                            'ignore-url-params-info' => 'Tentukan parameter URL yang diabaikan dalam aturan spekulasi. Gunakan tanda pipa (|) untuk memisahkan beberapa parameter.',
                            'ignore-urls'            => 'Abaikan URL untuk Prerender',
                            'ignore-urls-info'       => 'Masukkan daftar URL yang ingin dikecualikan dari logika spekulasi. Gunakan tanda pipa (|) untuk memisahkan beberapa URL.',
                            'info'                   => 'Atur status untuk aturan spekulasi.',
                            'moderate'               => 'Moderat',
                        ],

                        'prefetch' => [
                            'conservative'           => 'Konservatif',
                            'eager'                  => 'Agresif',
                            'eagerness'              => 'Tingkat Keagresifan Prefetch',
                            'eagerness-info'         => 'Mengatur seberapa agresif aturan spekulasi akan diterapkan. Pilihan: agresif (maksimum), moderat (default), konservatif (rendah).',
                            'enabled'                => 'Aktifkan Aturan Spekulasi Prefetch',
                            'ignore-url-params'      => 'Abaikan Parameter URL untuk Prefetch',
                            'ignore-url-params-info' => 'Tentukan parameter URL yang diabaikan dalam aturan spekulasi. Gunakan tanda pipa (|) untuk memisahkan beberapa parameter.',
                            'ignore-urls'            => 'Abaikan URL untuk Prefetch',
                            'ignore-urls-info'       => 'Masukkan daftar URL yang ingin dikecualikan dari logika spekulasi. Gunakan tanda pipa (|) untuk memisahkan beberapa URL.',
                            'info'                   => 'Atur status untuk aturan spekulasi.',
                            'moderate'               => 'Moderat',
                        ],
                    ],

                    'custom-scripts' => [
                        'custom-css'        => 'CSS Kustom',
                        'custom-javascript' => 'Javascript Kustom',
                        'title'             => 'Skrip Kustom',
                        'title-info'        => 'Skrip kustom adalah potongan kode yang dibuat secara khusus untuk menambahkan fungsi atau fitur tertentu ke dalam perangkat lunak, sehingga meningkatkan kemampuannya secara unik.',
                    ],
                ],

                'design' => [
                    'info'  => 'Atur logo dan ikon favicon untuk panel admin.',
                    'title' => 'Desain',

                    'admin-logo' => [
                        'favicon'    => 'Favicon',
                        'logo-image' => 'Logo Image',
                        'title'      => 'Admin Logo',
                        'title-info' => 'Configure logo and favicon images for your website\'s front end for better branding and recognition.',
                    ],

                    'menu-category' => [
                        'default'         => 'Menu Default',
                        'info'            => 'Pengaturan ini mengontrol visibilitas kategori dalam menu header. Anda dapat memilih untuk menampilkan hanya kategori induk atau seluruh kategori bersarang.',
                        'preview-default' => 'Pratinjau Menu Default',
                        'preview-sidebar' => 'Pratinjau Menu Sidebar',
                        'sidebar'         => 'Menu Sidebar',
                        'title'           => 'Tampilan Kategori Menu',
                    ],
                ],

                'magic-ai' => [
                    'info'  => 'Atur opsi Magic AI dan izinkan beberapa opsi untuk mengotomatisasi pembuatan konten.',
                    'title' => 'Magic AI',

                    'settings' => [
                        'api-key'        => 'Kunci API',
                        'enabled'        => 'Diaktifkan',
                        'llm-api-domain' => 'Domain API LLM',
                        'organization'   => 'Organisasi',
                        'title'          => 'Pengaturan Umum',
                        'title-info'     => 'Tingkatkan pengalaman Anda dengan fitur Magic AI dengan memasukkan Kunci API eksklusif Anda dan menunjukkan Organisasi terkait untuk integrasi yang mudah. Kuasai kredensial OpenAI Anda dan sesuaikan pengaturan sesuai kebutuhan spesifik Anda.',
                    ],

                    'content-generation' => [
                        'category-description-prompt'      => 'Prompt Deskripsi Kategori',
                        'cms-page-content-prompt'          => 'Prompt Konten Halaman CMS',
                        'enabled'                          => 'Diaktifkan',
                        'product-description-prompt'       => 'Prompt Deskripsi Produk',
                        'product-short-description-prompt' => 'Prompt Deskripsi Singkat Produk',
                        'title'                            => 'Pembuatan Konten',
                        'title-info'                       => 'Fitur ini akan mengaktifkan Magic AI untuk setiap editor WYSIWYG, di mana Anda ingin mengelola konten menggunakan AI.<br/><br/>Saat diaktifkan, buka editor apa pun untuk menghasilkan konten.',
                    ],

                    'image-generation' => [
                        'enabled'    => 'Diaktifkan',
                        'title'      => 'Pembuatan Gambar',
                        'title-info' => 'Fitur ini akan mengaktifkan Magic AI untuk setiap unggahan gambar, di mana Anda ingin menghasilkan gambar menggunakan DALL-E.<br/><br/>Saat diaktifkan, buka unggahan gambar apa pun untuk menghasilkan gambar.',
                    ],

                    'review-translation' => [
                        'deepseek-r1-8b'      => 'DeepSeek R1 (8b)',
                        'enabled'             => 'Diaktifkan',
                        'gemini-2-0-flash'    => 'Gemini 2.0 Flash',
                        'gpt-4-turbo'         => 'OpenAI gpt-4 Turbo',
                        'gpt-4o'              => 'OpenAI gpt-4o',
                        'gpt-4o-mini'         => 'OpenAI gpt-4o mini',
                        'llama-groq'          => 'Llama 3.3 (Groq)',
                        'llama3-1-8b'         => 'Llama 3.1 (8B)',
                        'llama3-2-1b'         => 'Llama 3.2 (1B)',
                        'llama3-2-3b'         => 'Llama 3.2 (3B)',
                        'llama3-8b'           => 'Llama 3 (8B)',
                        'llava-7b'            => 'Llava (7b)',
                        'mistral-7b'          => 'Mistral (7b)',
                        'model'               => 'Model',
                        'orca-mini'           => 'Orca Mini',
                        'phi3-5'              => 'Phi 3.5',
                        'qwen2-5-0-5b'        => 'Qwen 2.5 (0.5b)',
                        'qwen2-5-1-5b'        => 'Qwen 2.5 (1.5b)',
                        'qwen2-5-14b'         => 'Qwen 2.5 (14b)',
                        'qwen2-5-3b'          => 'Qwen 2.5 (3b)',
                        'qwen2-5-7b'          => 'Qwen 2.5 (7b)',
                        'starling-lm-7b'      => 'Starling-lm (7b)',
                        'title'               => 'Review Translation',
                        'title-info'          => 'Provide option to customer or visitor to translate customer review into english.<br/><br/>When enable, go to review and you will find the button ‘Translate to English’ if you review other then English.',
                        'vicuna-13b'          => 'Vicuna (13b)',
                        'vicuna-7b'           => 'Vicuna (7b)',
                    ],

                    'checkout-message' => [
                        'deepseek-r1-8b'      => 'DeepSeek R1 (8b)',
                        'enabled'             => 'Diaktifkan',
                        'gemini-2-0-flash'    => 'Gemini 2.0 Flash',
                        'gpt-4-turbo'         => 'OpenAI gpt 4 Turbo',
                        'gpt-4o'              => 'OpenAI gpt-4o',
                        'gpt-4o-mini'         => 'OpenAI gpt-4o mini',
                        'llama-groq'          => 'Llama 3.3 (Groq)',
                        'llama3-1-8b'         => 'Llama 3.1 (8B)',
                        'llama3-2-1b'         => 'Llama 3.2 (1B)',
                        'llama3-2-3b'         => 'Llama 3.2 (3B)',
                        'llama3-8b'           => 'Llama 3 (8B)',
                        'llava-7b'            => 'Llava (7b)',
                        'mistral-7b'          => 'Mistral (7b)',
                        'model'               => 'Model',
                        'orca-mini'           => 'Orca Mini',
                        'phi3-5'              => 'Phi 3.5',
                        'prompt'              => 'Prompt',
                        'qwen2-5-0-5b'        => 'Qwen 2.5 (0.5b)',
                        'qwen2-5-1-5b'        => 'Qwen 2.5 (1.5b)',
                        'qwen2-5-14b'         => 'Qwen 2.5 (14b)',
                        'qwen2-5-3b'          => 'Qwen 2.5 (3b)',
                        'qwen2-5-7b'          => 'Qwen 2.5 (7b)',
                        'starling-lm-7b'      => 'Starling-lm (7b)',
                        'title'               => 'Pesan Checkout Personalisasi',
                        'title-info'          => 'Buat pesan checkout yang dipersonalisasi untuk pelanggan di halaman Terima Kasih, menyesuaikan kontennya dengan preferensi individu dan meningkatkan pengalaman pasca-pembelian secara keseluruhan.',
                        'vicuna'              => 'Vicuna',
                        'vicuna-13b'          => 'Vicuna (13b)',
                        'vicuna-7b'           => 'Vicuna (7b)',
                    ],
                ],

                'gdpr' => [
                    'title' => 'GDPR',
                    'info'  => 'Pengaturan Kepatuhan GDPR',

                    'settings' => [
                        'title'   => 'Pengaturan Kepatuhan GDPR',
                        'info'    => 'Kelola pengaturan kepatuhan GDPR, termasuk perjanjian privasi data. Aktifkan atau nonaktifkan fitur GDPR sesuai kebutuhan.',
                        'enabled' => 'Aktifkan GDPR',
                    ],

                    'agreement' => [
                        'title'          => 'Persetujuan GDPR',
                        'info'           => 'Kelola persetujuan pelanggan sesuai regulasi GDPR. Aktifkan persyaratan persetujuan untuk pengumpulan dan pemrosesan data.',
                        'enable'         => 'Aktifkan Persetujuan Pelanggan',
                        'checkbox-label' => 'Label Kotak Centang Persetujuan',
                        'content'        => 'Konten Persetujuan',
                    ],

                    'cookie' => [
                        'bottom-left'  => 'Kiri Bawah',
                        'bottom-right' => 'Kanan Bawah',
                        'center'       => 'Tengah',
                        'description'  => 'Deskripsi',
                        'enable'       => 'Aktifkan Pemberitahuan Cookie',
                        'identifier'   => 'Identifier Blok Statis',
                        'info'         => 'Atur pengaturan persetujuan cookie untuk memberi tahu pengguna tentang pengumpulan data dan memastikan kepatuhan terhadap regulasi privasi.',
                        'position'     => 'Posisi Tampilan Blok Cookie',
                        'title'        => 'Pengaturan Pemberitahuan Cookie',
                        'top-left'     => 'Kiri Atas',
                        'top-right'    => 'Kanan Atas',
                    ],

                    'cookie-consent' => [
                        'title'                  => 'Kelola Preferensi Cookie Anda',
                        'info'                   => 'Kendalikan penggunaan data Anda dengan memilih pengaturan cookie yang diinginkan. Sesuaikan izin untuk berbagai jenis cookie.',
                        'strictly-necessary'     => 'Sangat Diperlukan',
                        'basic-interaction'      => 'Interaksi & Fungsionalitas Dasar',
                        'experience-enhancement' => 'Peningkatan Pengalaman',
                        'measurement'            => 'Pengukuran',
                        'targeting-advertising'  => 'Penargetan & Periklanan',
                    ],
                ],

                'sitemap' => [
                    'info'  => 'Atur opsi sitemap.',
                    'title' => 'Sitemap',

                    'settings' => [
                        'enabled' => 'Diaktifkan',
                        'info'    => 'Aktifkan atau nonaktifkan sitemap untuk meningkatkan optimasi mesin pencari dan memperbaiki pengalaman pengguna.',
                        'title'   => 'Pengaturan',
                    ],

                    'file-limits' => [
                        'info'             => 'Atur opsi batasan file.',
                        'max-file-size'    => 'Ukuran Maksimum File',
                        'max-url-per-file' => 'Jumlah Maksimum URL per File',
                        'title'            => 'Batasan File',
                    ],
                ],
            ],

            'catalog' => [
                'info'  => 'Katalog',
                'title' => 'Katalog',

                'products' => [
                    'info'  => 'Halaman tampilan produk, halaman tampilan keranjang, toko depan, ulasan, dan berbagi atribut sosial.',
                    'title' => 'Produk',

                    'settings' => [
                        'compare-options'     => 'Opsi Perbandingan',
                        'image-search-option' => 'Opsi Pencarian Gambar',
                        'title'               => 'Pengaturan',
                        'title-info'          => 'Pengaturan mengacu pada pilihan yang dapat dikonfigurasi yang mengontrol bagaimana sistem, aplikasi, atau perangkat berperilaku, disesuaikan dengan preferensi dan kebutuhan pengguna.',
                        'wishlist-options'    => 'Opsi Wishlist',
                    ],

                    'search' => [
                        'admin-mode'            => 'Mode Pencarian Admin',
                        'admin-mode-info'       => 'Mega Pencarian, Datagrid, dan fungsionalitas pencarian lainnya di panel admin akan didasarkan pada mesin pencarian yang dipilih.',
                        'database'              => 'Database',
                        'elastic'               => 'Elastic Search',
                        'max-query-length'      => 'Panjang kueri maksimum',
                        'max-query-length-info' => 'Atur panjang kueri maksimum untuk pencarian.',
                        'min-query-length'      => 'Panjang kueri minimum',
                        'min-query-length-info' => 'Atur panjang kueri minimum untuk pencarian.',
                        'search-engine'         => 'Mesin Pencarian',
                        'storefront-mode'       => 'Mode Pencarian Toko Depan',
                        'storefront-mode-info'  => 'Fungsionalitas pencarian di toko depan akan didasarkan pada mesin pencarian yang dipilih, termasuk halaman kategori, halaman pencarian, dan fungsionalitas pencarian lainnya.',
                        'title'                 => 'Pencarian',
                        'title-info'            => 'Untuk mengatur mesin pencarian untuk pencarian produk, Anda dapat memilih antara database dan Elasticsearch berdasarkan kebutuhan Anda. Jika Anda memiliki banyak produk, Elasticsearch disarankan.',
                    ],

                    'guest-checkout' => [
                        'allow-guest-checkout'      => 'Izinkan Pembayaran Tamu',
                        'allow-guest-checkout-hint' => 'Petunjuk: Jika diaktifkan, opsi ini dapat dikonfigurasi untuk setiap produk secara spesifik.',
                        'title'                     => 'Pembayaran Tamu',
                        'title-info'                => 'Pembayaran tamu memungkinkan pelanggan untuk membeli produk tanpa membuat akun, menyederhanakan proses pembelian untuk kenyamanan dan transaksi yang lebih cepat.',
                    ],

                    'product-view-page' => [
                        'allow-no-of-related-products'  => 'Jumlah Produk Terkait yang Diizinkan',
                        'allow-no-of-up-sells-products' => 'Jumlah Produk Up-Sell yang Diizinkan',
                        'title'                         => 'Konfigurasi Halaman Tampilan Produk',
                        'title-info'                    => 'Konfigurasi halaman tampilan produk melibatkan penyesuaian tata letak dan elemen pada halaman tampilan produk, meningkatkan pengalaman pengguna dan penyajian informasi.',
                    ],

                    'cart-view-page' => [
                        'allow-no-of-cross-sells-products' => 'Jumlah Produk Cross-Sell yang Diizinkan',
                        'title'                            => 'Konfigurasi Halaman Tampilan Keranjang',
                        'title-info'                       => 'Konfigurasi halaman tampilan keranjang melibatkan pengaturan item, detail, dan opsi di halaman keranjang belanja, meningkatkan interaksi pengguna dan alur pembelian.',
                    ],

                    'storefront' => [
                        'buy-now-button-display' => 'Izinkan pelanggan untuk membeli produk langsung',
                        'cheapest-first'         => 'Murah Terlebih Dahulu',
                        'comma-separated'        => 'Dipisahkan dengan koma',
                        'default-list-mode'      => 'Mode Daftar Default',
                        'expensive-first'        => 'Mahal Terlebih Dahulu',
                        'from-a-z'               => 'Dari A-Z',
                        'from-z-a'               => 'Dari Z-A',
                        'grid'                   => 'Grid',
                        'latest-first'           => 'Terbaru Terlebih Dahulu',
                        'list'                   => 'Daftar',
                        'oldest-first'           => 'Tertua Terlebih Dahulu',
                        'products-per-page'      => 'Produk Per Halaman',
                        'sort-by'                => 'Urutkan Berdasarkan',
                        'title'                  => 'Toko Depan',
                        'title-info'             => 'Toko depan adalah antarmuka yang menghadap pelanggan dari toko online, menampilkan produk, kategori, dan navigasi untuk pengalaman belanja yang mulus.',
                    ],

                    'small-image' => [
                        'height'      => 'Tinggi',
                        'placeholder' => 'Placeholder Gambar Kecil',
                        'title'       => 'Gambar Kecil',
                        'title-info'  => 'Toko depan adalah antarmuka yang menghadap pelanggan dari toko online, menampilkan produk, kategori, dan navigasi untuk pengalaman belanja yang mulus.',
                        'width'       => 'Lebar',
                    ],

                    'medium-image' => [
                        'height'      => 'Tinggi',
                        'placeholder' => 'Placeholder Gambar Sedang',
                        'title'       => 'Gambar Sedang',
                        'title-info'  => 'Gambar sedang mengacu pada gambar berukuran sedang yang menawarkan keseimbangan antara detail dan ruang layar, umumnya digunakan untuk visual.',
                        'width'       => 'Lebar',
                    ],

                    'large-image' => [
                        'height'      => 'Tinggi',
                        'placeholder' => 'Placeholder Gambar Besar',
                        'title'       => 'Gambar Besar',
                        'title-info'  => 'Gambar besar mewakili gambar beresolusi tinggi yang memberikan detail yang lebih baik dan dampak visual, sering digunakan untuk menampilkan produk atau grafik.',
                        'width'       => 'Lebar',
                    ],

                    'review' => [
                        'allow-customer-review'   => 'Izinkan Ulasan dari Pelanggan',
                        'allow-guest-review'      => 'Izinkan Ulasan dari Tamu',
                        'censoring-reviewer-name' => 'Sensor Nama Pengulas',
                        'display-review-count'    => 'Tampilkan jumlah ulasan dalam penilaian.',
                        'display-star-count'      => 'Tampilkan jumlah bintang dalam penilaian.',
                        'summary'                 => 'Ringkasan',
                        'title'                   => 'Ulasan',
                        'title-info'              => 'Evaluasi atau penilaian terhadap sesuatu, biasanya melibatkan opini dan masukan.',
                    ],

                    'attribute' => [
                        'file-upload-size'  => 'Ukuran Unggahan File yang Diizinkan (dalam Kb)',
                        'image-upload-size' => 'Ukuran Unggahan Gambar yang Diizinkan (dalam Kb)',
                        'title'             => 'Atribut',
                        'title-info'        => 'Karakteristik atau properti yang mendefinisikan objek, mempengaruhi perilaku, penampilan, atau fungsi.',
                    ],

                    'social-share' => [
                        'title-info'                  => 'Konfigurasi pengaturan berbagi sosial untuk memungkinkan berbagi produk di Instagram, Twitter, WhatsApp, Facebook, Pinterest, LinkedIn, dan melalui email.',
                        'title'                       => 'Berbagi Sosial',
                        'share-message'               => 'Pesan Berbagi',
                        'share'                       => 'Bagikan',
                        'enable-social-share'         => 'Aktifkan Berbagi Sosial?',
                        'enable-share-whatsapp-info'  => 'Tautan berbagi WhatsApp hanya akan muncul di perangkat seluler.',
                        'enable-share-whatsapp'       => 'Aktifkan Berbagi di WhatsApp?',
                        'enable-share-twitter'        => 'Aktifkan Berbagi di Twitter?',
                        'enable-share-pinterest'      => 'Aktifkan Berbagi di Pinterest?',
                        'enable-share-linkedin'       => 'Aktifkan Berbagi di LinkedIn?',
                        'enable-share-facebook'       => 'Aktifkan Berbagi di Facebook?',
                        'enable-share-email'          => 'Aktifkan Berbagi di Email?',
                    ],
                ],

                'rich-snippets' => [
                    'info'  => 'Atur produk dan kategori.',
                    'title' => 'Rich Snippets',

                    'products' => [
                        'enable'          => 'Aktifkan',
                        'show-categories' => 'Tampilkan Kategori',
                        'show-images'     => 'Tampilkan Gambar',
                        'show-offers'     => 'Tampilkan Penawaran',
                        'show-ratings'    => 'Tampilkan Peringkat',
                        'show-reviews'    => 'Tampilkan Ulasan',
                        'show-sku'        => 'Tampilkan SKU',
                        'show-weight'     => 'Tampilkan Berat',
                        'title'           => 'Produk',
                        'title-info'      => 'Konfigurasi pengaturan produk termasuk dengan SKU, berat, Kategori, Gambar, Ulasan, Peringkat, Penawaran, dll.',
                    ],

                    'categories' => [
                        'enable'                  => 'Aktifkan',
                        'show-search-input-field' => 'Tampilkan Kolom Pencarian',
                        'title'                   => 'Kategori',
                        'title-info'              => '"Kategori" mengacu pada kelompok atau klasifikasi yang membantu mengorganisir dan mengelompokkan produk atau item serupa untuk mempermudah pencarian dan navigasi.',
                    ],
                ],

                'inventory' => [
                    'title'      => 'Inventaris',
                    'title-info' => 'Konfigurasikan pengaturan inventaris untuk memungkinkan pemesanan kembali dan tentukan ambang batas stok habis.',

                    'product-stock-options' => [
                        'allow-back-orders'       => 'Izinkan Pemesanan Kembali',
                        'max-qty-allowed-in-cart' => 'Jumlah Maksimum yang Diizinkan di Keranjang Belanja',
                        'min-qty-allowed-in-cart' => 'Jumlah Minimum yang Diizinkan di Keranjang Belanja',
                        'out-of-stock-threshold'  => 'Ambang Batas Stok Habis',
                        'title'                   => 'Opsi Stok Produk',
                        'info'                    => 'Konfigurasikan opsi stok produk untuk memungkinkan pemesanan kembali, menetapkan jumlah minimum dan maksimum di keranjang, dan tentukan ambang batas stok habis.',
                    ],
                ],
            ],

            'customer' => [
                'info'  => 'Pelanggan',
                'title' => 'Pelanggan',

                'address' => [
                    'info'  => 'Setel negara, negara bagian, kode pos, dan baris di alamat jalan.',
                    'title' => 'Alamat',

                    'requirements' => [
                        'city'       => 'Kota',
                        'country'    => 'Negara',
                        'state'      => 'Negara Bagian',
                        'title'      => 'Persyaratan',
                        'title-info' => 'Persyaratan adalah kondisi, fitur, atau spesifikasi yang diperlukan agar sesuatu dapat dipenuhi, tercapai, atau berhasil dipenuhi.',
                        'zip'        => 'Kode Pos',
                    ],

                    'information' => [
                        'street-lines' => 'Baris di Alamat Jalan',
                        'title'        => 'Informasi',
                        'title-info'   => '"Baris di alamat jalan" mengacu pada segmen individual dari alamat, sering dipisahkan dengan koma, memberikan informasi lokasi seperti nomor rumah, jalan, kota, dan lainnya.',
                    ],
                ],

                'captcha' => [
                    'info'  => 'Setel kunci situs, kunci rahasia, dan status.',
                    'title' => 'Google Captcha',

                    'credentials' => [
                        'secret-key' => 'Kunci Rahasia',
                        'site-key'   => 'Kunci Situs',
                        'status'     => 'Status',
                        'title'      => 'Kredensial',
                        'title-info' => '"Peta Situs: Peta tata letak situs untuk mesin pencari. Kunci rahasia: Kode aman untuk enkripsi data, otentikasi, atau perlindungan akses API."',
                    ],

                    'validations' => [
                        'captcha'  => 'Ada yang salah! Silakan coba lagi.',
                        'required' => 'Silakan pilih CAPTCHA',
                    ],
                ],

                'settings' => [
                    'settings-info' => 'Setel wishlist, pengalihan login, langganan newsletter, opsi grup default, verifikasi email, dan login sosial.',
                    'title'         => 'Pengaturan',

                    'login-as-customer' => [
                        'allow-option' => 'Izinkan Login sebagai Pelanggan',
                        'title'        => 'Login Sebagai Pelanggan',
                        'title-info'   => 'Aktifkan fungsionalitas "Login sebagai Pelanggan".',
                    ],

                    'wishlist' => [
                        'allow-option' => 'Izinkan Opsi Wishlist',
                        'title'        => 'Wishlist',
                        'title-info'   => 'Aktifkan atau nonaktifkan opsi wishlist.',
                    ],

                    'login-options' => [
                        'account'          => 'Akun',
                        'home'             => 'Beranda',
                        'redirect-to-page' => 'Alihkan Pelanggan ke halaman yang dipilih',
                        'title'            => 'Opsi Login',
                        'title-info'       => 'Konfigurasikan opsi login untuk menentukan halaman alihan bagi pelanggan setelah login.',
                    ],

                    'create-new-account-option' => [
                        'news-letter'      => 'Izinkan Newsletter',
                        'news-letter-info' => 'Aktifkan opsi langganan newsletter pada halaman pendaftaran.',
                        'title'            => 'Opsi Pembuatan Akun Baru',
                        'title-info'       => 'Setel opsi untuk akun baru, termasuk menetapkan grup pelanggan default dan mengaktifkan opsi langganan newsletter selama pendaftaran.',

                        'default-group' => [
                            'general'    => 'Umum',
                            'guest'      => 'Tamu',
                            'title'      => 'Grup Default',
                            'title-info' => 'Tetapkan grup pelanggan tertentu sebagai default untuk pelanggan baru.',
                            'wholesale'  => 'Grosir',
                        ],
                    ],

                    'newsletter' => [
                        'subscription' => 'Izinkan Langganan Newsletter',
                        'title'        => 'Langganan Newsletter',
                        'title-info'   => '"Informasi Newsletter" berisi pembaruan, penawaran, atau konten yang dibagikan secara teratur melalui email kepada pelanggan, menjaga mereka tetap terinformasi dan terlibat.',
                    ],

                    'email' => [
                        'email-verification' => 'Izinkan Verifikasi Email',
                        'title'              => 'Verifikasi Email',
                        'title-info'         => '"Verifikasi email" mengonfirmasi keaslian alamat email, sering kali dengan mengirimkan tautan konfirmasi, meningkatkan keamanan akun dan keandalan komunikasi.',
                    ],

                    'social-login' => [
                        'title' => 'Login Sosial',
                        'info'  => '"Login sosial" memungkinkan pengguna mengakses situs web menggunakan akun media sosial mereka, sehingga proses registrasi dan masuk menjadi lebih mudah.',

                        'google' => [
                            'enable-google' => 'Aktifkan Google',

                            'client-id' => [
                                'title'      => 'Client ID',
                                'title-info' => 'Pengenal unik yang diberikan oleh Google saat Anda membuat aplikasi OAuth.',
                            ],

                            'client-secret' => [
                                'title'      => 'Client Secret',
                                'title-info' => 'Kunci rahasia yang terkait dengan klien OAuth Google Anda. Jaga kerahasiaannya.',
                            ],

                            'redirect' => [
                                'title'      => 'URL Pengalihan',
                                'title-info' => 'URL callback tempat pengguna diarahkan setelah otentikasi dengan Google. Harus sesuai dengan URL yang dikonfigurasi di konsol Google Anda.',
                            ],
                        ],

                        'facebook' => [
                            'enable-facebook' => 'Aktifkan Facebook',

                            'client-id' => [
                                'title'      => 'Client ID',
                                'title-info' => 'App ID yang diberikan oleh Facebook saat membuat aplikasi di konsol pengembang Facebook.',
                            ],

                            'client-secret' => [
                                'title'      => 'Client Secret',
                                'title-info' => 'App secret yang terkait dengan aplikasi Facebook Anda. Jaga kerahasiaannya.',
                            ],

                            'redirect' => [
                                'title'      => 'URL Pengalihan',
                                'title-info' => 'URL callback tempat pengguna diarahkan setelah otentikasi dengan Facebook. Harus sesuai dengan URL yang dikonfigurasi di pengaturan aplikasi Facebook Anda.',
                            ],
                        ],

                        'github' => [
                            'enable-github' => 'Aktifkan GitHub',

                            'client-id' => [
                                'title'      => 'Client ID',
                                'title-info' => 'Pengenal unik yang diberikan oleh GitHub saat membuat aplikasi OAuth.',
                            ],

                            'client-secret' => [
                                'title'      => 'Client Secret',
                                'title-info' => 'Kunci rahasia yang terkait dengan klien OAuth GitHub Anda. Jaga kerahasiaannya.',
                            ],

                            'redirect' => [
                                'title'      => 'URL Pengalihan',
                                'title-info' => 'URL callback tempat pengguna diarahkan setelah otentikasi dengan GitHub. Harus sesuai dengan URL yang dikonfigurasi di konsol GitHub Anda.',
                            ],
                        ],

                        'linkedin' => [
                            'enable-linkedin' => 'Aktifkan LinkedIn',

                            'client-id' => [
                                'title'      => 'Client ID',
                                'title-info' => 'Pengenal unik yang diberikan oleh LinkedIn saat membuat aplikasi OAuth.',
                            ],

                            'client-secret' => [
                                'title'      => 'Client Secret',
                                'title-info' => 'Kunci rahasia yang terkait dengan klien OAuth LinkedIn Anda. Jaga kerahasiaannya.',
                            ],

                            'redirect' => [
                                'title'      => 'URL Pengalihan',
                                'title-info' => 'URL callback tempat pengguna diarahkan setelah otentikasi dengan LinkedIn. Harus sesuai dengan URL yang dikonfigurasi di konsol LinkedIn Anda.',
                            ],
                        ],

                        'twitter' => [
                            'enable-twitter' => 'Aktifkan Twitter',

                            'client-id' => [
                                'title'      => 'Client ID',
                                'title-info' => 'Pengenal unik yang diberikan oleh Twitter saat membuat aplikasi OAuth.',
                            ],

                            'client-secret' => [
                                'title'      => 'Client Secret',
                                'title-info' => 'Kunci rahasia yang terkait dengan klien OAuth Twitter Anda. Jaga kerahasiaannya.',
                            ],

                            'redirect' => [
                                'title'      => 'URL Pengalihan',
                                'title-info' => 'URL callback tempat pengguna diarahkan setelah otentikasi dengan Twitter. Harus sesuai dengan URL yang dikonfigurasi di konsol Twitter Anda.',
                            ],
                        ],
                    ],
                ],
            ],

            'email' => [
                'info'  => 'Email',
                'title' => 'Email',

                'email-settings' => [
                    'admin-email'           => 'Email Admin',
                    'admin-email-tip'       => 'Alamat email admin untuk saluran ini agar menerima email',
                    'admin-name'            => 'Nama Admin',
                    'admin-name-tip'        => 'Nama ini akan ditampilkan di semua email admin',
                    'admin-page-limit'      => 'Item Default Per Halaman (Admin)',
                    'contact-email'         => 'Email Kontak',
                    'contact-email-tip'     => 'Alamat email ini akan ditampilkan di bagian bawah email Anda',
                    'contact-name'          => 'Nama Kontak',
                    'contact-name-tip'      => 'Nama ini akan ditampilkan di bagian bawah email Anda',
                    'email-sender-name'     => 'Nama Pengirim Email',
                    'email-sender-name-tip' => 'Nama ini akan ditampilkan di kotak masuk pelanggan',
                    'info'                  => 'Setel nama pengirim email, alamat email toko, nama admin, dan alamat email admin.',
                    'shop-email-from'       => 'Alamat Email Toko',
                    'shop-email-from-tip'   => 'Alamat email dari saluran ini untuk mengirim email ke pelanggan Anda',
                    'title'                 => 'Pengaturan Email',
                ],

                'notifications' => [
                    'cancel-order'                                     => 'Kirim notifikasi kepada pelanggan setelah membatalkan pesanan',
                    'cancel-order-mail-to-admin'                       => 'Kirim email notifikasi kepada admin setelah membatalkan pesanan',
                    'customer'                                         => 'Kirim kredensial akun pelanggan setelah pendaftaran',
                    'customer-registration-confirmation-mail-to-admin' => 'Kirim email konfirmasi kepada admin setelah pendaftaran pelanggan',
                    'info'                                             => 'Untuk mengonfigurasi, terima email untuk verifikasi akun, konfirmasi pesanan, pembaruan faktur, pengembalian dana, pengiriman, dan pembatalan pesanan.',
                    'new-inventory-source'                             => 'Kirim email notifikasi kepada sumber inventaris setelah membuat pengiriman',
                    'new-invoice'                                      => 'Kirim email notifikasi kepada pelanggan setelah membuat faktur baru',
                    'new-invoice-mail-to-admin'                        => 'Kirim email notifikasi kepada admin setelah membuat faktur baru',
                    'new-order'                                        => 'Kirim email konfirmasi kepada pelanggan setelah melakukan pesanan baru',
                    'new-order-mail-to-admin'                          => 'Kirim email konfirmasi kepada admin setelah melakukan pesanan baru',
                    'new-refund'                                       => 'Kirim email notifikasi kepada pelanggan setelah membuat pengembalian dana',
                    'new-refund-mail-to-admin'                         => 'Kirim email notifikasi kepada admin setelah membuat pengembalian dana baru',
                    'new-shipment'                                     => 'Kirim email notifikasi kepada pelanggan setelah membuat pengiriman baru',
                    'new-shipment-mail-to-admin'                       => 'Kirim email notifikasi kepada admin setelah membuat pengiriman baru',
                    'registration'                                     => 'Kirim email konfirmasi setelah pendaftaran pelanggan',
                    'title'                                            => 'Notifikasi',
                    'verification'                                     => 'Kirim email verifikasi setelah pendaftaran pelanggan',
                ],
            ],

            'sales' => [
                'info'  => 'Penjualan',
                'title' => 'Penjualan',

                'shipping-setting' => [
                    'info'  => 'Konfigurasi pengaturan pengiriman termasuk Negara, Provinsi, Kota, Alamat Jalan, Kode Pos, Nama Toko, Nomor PPN, Nomor Kontak, dan Detail Bank.',
                    'title' => 'Pengaturan Pengiriman',

                    'origin' => [
                        'bank-details'   => 'Detail Bank',
                        'city'           => 'Kota',
                        'contact-number' => 'Nomor Kontak',
                        'country'        => 'Negara',
                        'state'          => 'Provinsi',
                        'store-name'     => 'Nama Toko',
                        'street-address' => 'Alamat Jalan',
                        'title'          => 'Asal Pengiriman',
                        'title-info'     => 'Asal pengiriman merujuk pada lokasi tempat barang atau produk berasal sebelum dikirim ke tujuan.',
                        'vat-number'     => 'Nomor PPN',
                        'zip'            => 'Kode Pos',
                    ],
                ],

                'shipping-methods' => [
                    'info'  => 'Konfigurasi metode pengiriman, termasuk Pengiriman Gratis, Tarif Tetap, dan opsi tambahan sesuai kebutuhan.',
                    'title' => 'Metode Pengiriman',

                    'free-shipping' => [
                        'description' => 'Deskripsi',
                        'page-title'  => 'Pengiriman Gratis',
                        'status'      => 'Status',
                        'title'       => 'Judul',
                        'title-info'  => '"Pengiriman gratis" merujuk pada metode pengiriman di mana biaya pengiriman diabaikan, dan penjual menanggung biaya pengiriman untuk mengantarkan barang kepada pembeli.',
                    ],

                    'flat-rate-shipping' => [
                        'description' => 'Deskripsi',
                        'page-title'  => 'Pengiriman Tarif Tetap',
                        'rate'        => 'Tarif',
                        'status'      => 'Status',
                        'title'       => 'Judul',
                        'title-info'  => 'Pengiriman tarif tetap adalah metode pengiriman di mana biaya tetap dikenakan untuk pengiriman, terlepas dari berat, ukuran, atau jarak paket. Ini menyederhanakan biaya pengiriman dan dapat menguntungkan bagi pembeli dan penjual.',
                        'type'        => [
                            'per-order' => 'Per Pesanan',
                            'per-unit'  => 'Per Unit',
                            'title'     => 'Jenis',
                        ],
                    ],
                ],

                'payment-methods' => [
                    'accepted-currencies'            => 'Mata Uang yang Diterima',
                    'accepted-currencies-info'       => 'Tambahkan kode mata uang yang dipisahkan dengan koma misalnya USD,INR,...',
                    'business-account'               => 'Akun Bisnis',
                    'cash-on-delivery'               => 'Pembayaran di Tempat',
                    'cash-on-delivery-info'          => 'Metode pembayaran di mana pelanggan membayar dengan uang tunai saat menerima barang atau layanan di depan pintu mereka.',
                    'client-id'                      => 'ID Klien',
                    'client-id-info'                 => 'Gunakan "sb" untuk pengujian.',
                    'client-secret'                  => 'Kunci Rahasia Klien',
                    'client-secret-info'             => 'Tambahkan kunci rahasia Anda di sini',
                    'description'                    => 'Deskripsi',
                    'generate-invoice'               => 'Secara otomatis menghasilkan faktur setelah melakukan pesanan',
                    'generate-invoice-applicable'    => 'Berlaku jika pembuatan faktur otomatis diaktifkan',
                    'info'                           => 'Setel informasi metode pembayaran',
                    'instructions'                   => 'Instruksi',
                    'logo'                           => 'Logo',
                    'logo-information'               => 'Resolusi gambar harus seperti 55px X 45px',
                    'mailing-address'                => 'Kirim Cek ke',
                    'money-transfer'                 => 'Transfer Uang',
                    'money-transfer-info'            => 'Transfer dana dari satu orang atau akun ke orang atau akun lain, sering kali secara elektronik, untuk berbagai tujuan seperti transaksi atau pengiriman uang.',
                    'page-title'                     => 'Metode Pembayaran',
                    'paid'                           => 'Telah Dibayar',
                    'paypal-smart-button'            => 'PayPal',
                    'paypal-smart-button-info'       => 'PayPal Smart Button: Menyederhanakan pembayaran online dengan tombol yang dapat disesuaikan untuk transaksi multi-metode yang aman di situs web dan aplikasi.',
                    'paypal-standard'                => 'PayPal Standard',
                    'paypal-standard-info'           => 'PayPal Standard adalah opsi pembayaran dasar PayPal untuk bisnis online, memungkinkan pelanggan untuk membayar menggunakan akun PayPal mereka atau kartu kredit/debit.',
                    'pending'                        => 'Menunggu',
                    'pending-payment'                => 'Pembayaran Menunggu',
                    'processing'                     => 'Proses',
                    'sandbox'                        => 'Sandbox',
                    'set-invoice-status'             => 'Setel status faktur setelah membuat faktur ke',
                    'set-order-status'               => 'Setel status pesanan setelah membuat faktur ke',
                    'sort-order'                     => 'Urutan Pengurutan',
                    'status'                         => 'Status',
                    'title'                          => 'Judul',
                ],

                'order-settings' => [
                    'info'               => 'Atur nomor pesanan, pesanan minimum, dan pesanan balik.',
                    'title'              => 'Pengaturan Pesanan',

                    'order-number' => [
                        'generator'   => 'Generator Nomor Pesanan',
                        'info'        => 'Identifikasi unik yang diberikan pada pesanan pelanggan tertentu, membantu pelacakan, komunikasi, dan referensi sepanjang proses pembelian.',
                        'length'      => 'Panjang Nomor Pesanan',
                        'prefix'      => 'Awalan Nomor Pesanan',
                        'suffix'      => 'Akhiran Nomor Pesanan',
                        'title'       => 'Pengaturan Nomor Pesanan',
                    ],

                    'minimum-order' => [
                        'description'             => 'Deskripsi',
                        'enable'                  => 'Aktifkan',
                        'include-discount-amount' => 'Termasuk Jumlah Diskon',
                        'include-tax-amount'      => 'Termasuk Pajak ke Jumlah',
                        'info'                    => 'Kriteria yang dikonfigurasi yang menentukan jumlah atau nilai terendah yang diperlukan agar pesanan dapat diproses atau memenuhi syarat untuk mendapatkan manfaat.',
                        'minimum-order-amount'    => 'Jumlah Pesanan Minimum',
                        'title'                   => 'Pengaturan Pesanan Minimum',
                    ],

                    'reorder' => [
                        'admin-reorder'      => 'Pemesanan Ulang Admin',
                        'admin-reorder-info' => 'Aktifkan atau nonaktifkan fitur pemesanan ulang untuk pengguna admin.',
                        'info'               => 'Aktifkan atau nonaktifkan fitur pemesanan ulang untuk pengguna admin.',
                        'shop-reorder'       => 'Pemesan Ulang Toko',
                        'shop-reorder-info'  => 'Aktifkan atau nonaktifkan fitur pemesanan ulang untuk pengguna toko.',
                        'title'              => 'Izinkan Pemesanan Ulang',
                    ],

                    'stock-options' => [
                        'allow-back-orders' => 'Izinkan Pesanan Balik',
                        'info'              => 'Opsi stok adalah kontrak investasi yang memberikan hak untuk membeli atau menjual saham perusahaan pada harga yang telah ditentukan, memengaruhi potensi keuntungan.',
                        'title'             => 'Opsi Stok',
                    ],
                ],

                'invoice-settings' => [
                    'info'  => 'Atur nomor faktur, syarat pembayaran, desain slip faktur, dan pengingat faktur.',
                    'title' => 'Pengaturan Faktur',

                    'invoice-number' => [
                        'generator'  => 'Generator Nomor Faktur',
                        'info'       => 'Konfigurasi aturan atau parameter untuk menghasilkan dan menetapkan nomor identifikasi unik untuk faktur guna tujuan organisasi dan pelacakan.',
                        'length'     => 'Panjang Nomor Faktur',
                        'prefix'     => 'Awalan Nomor Faktur',
                        'suffix'     => 'Akhiran Nomor Faktur',
                        'title'      => 'Pengaturan Nomor Faktur',
                    ],

                    'payment-terms' => [
                        'due-duration'      => 'Durasi Jatuh Tempo',
                        'due-duration-day'  => ':due-duration Hari',
                        'due-duration-days' => ':due-duration Hari',
                        'info'              => 'Kondisi yang disepakati yang mengatur kapan dan bagaimana pembayaran untuk barang atau jasa harus dilakukan oleh pembeli kepada penjual.',
                        'title'             => 'Syarat Pembayaran',
                    ],

                    'pdf-print-outs' => [
                        'footer-text'      => 'Teks Footer',
                        'footer-text-info' => 'Masukkan teks yang akan ditampilkan di bagian footer PDF.',
                        'info'             => 'Atur tampilan cetakan PDF untuk menampilkan ID Faktur, ID Pesanan di header, dan menyertakan logo faktur.',
                        'invoice-id-info'  => 'Atur tampilan ID Faktur di Header Faktur.',
                        'invoice-id-title' => 'Tampilkan ID Faktur di Header',
                        'logo'             => 'Logo',
                        'logo-info'        => 'Resolusi gambar disarankan sekitar 131px × 30px.',
                        'order-id-info'    => 'Atur tampilan ID Pesanan di Header Faktur.',
                        'order-id-title'   => 'Tampilkan ID Pesanan di Header',
                        'title'            => 'Cetakan PDF',
                    ],

                    'invoice-reminders' => [
                        'info'                       => 'Pemberitahuan otomatis atau komunikasi yang dikirimkan kepada pelanggan untuk mengingatkan mereka tentang pembayaran faktur yang akan datang atau yang telah jatuh tempo.',
                        'interval-between-reminders' => 'Interval antara pengingat',
                        'maximum-limit-of-reminders' => 'Batas maksimum pengingat',
                        'title'                      => 'Pengingat Faktur',
                    ],
                ],

                'taxes' => [
                    'title'      => 'Pajak',
                    'title-info' => 'Pajak adalah biaya wajib yang dikenakan oleh pemerintah pada barang, jasa, atau transaksi, yang dikumpulkan oleh penjual dan disetorkan ke otoritas yang berwenang.',

                    'categories' => [
                        'title'      => 'Kategori Pajak',
                        'title-info' => 'Kategori pajak adalah klasifikasi untuk berbagai jenis pajak, seperti pajak penjualan, pajak pertambahan nilai, atau pajak cukai, yang digunakan untuk mengkategorikan dan menerapkan tarif pajak pada produk atau layanan.',
                        'product'    => 'Kategori Pajak Default Produk',
                        'shipping'   => 'Kategori Pajak Pengiriman',
                        'none'       => 'Tidak Ada',
                    ],

                    'calculation' => [
                        'title'            => 'Pengaturan Perhitungan',
                        'title-info'       => 'Detail tentang biaya barang atau jasa, termasuk harga dasar, diskon, pajak, dan biaya tambahan.',
                        'based-on'         => 'Perhitungan Berdasarkan',
                        'shipping-address' => 'Alamat Pengiriman',
                        'billing-address'  => 'Alamat Penagihan',
                        'shipping-origin'  => 'Asal Pengiriman',
                        'product-prices'   => 'Harga Produk',
                        'shipping-prices'  => 'Harga Pengiriman',
                        'excluding-tax'    => 'Tanpa Pajak',
                        'including-tax'    => 'Termasuk Pajak',
                    ],

                    'default-destination-calculation' => [
                        'default-country'   => 'Negara Default',
                        'default-post-code' => 'Kode Pos Default',
                        'default-state'     => 'Negara Bagian Default',
                        'title'             => 'Perhitungan Tujuan Default',
                        'title-info'        => 'Penentuan otomatis tujuan standar atau awal berdasarkan faktor atau pengaturan yang telah ditentukan sebelumnya.',
                    ],

                    'shopping-cart' => [
                        'title'                   => 'Pengaturan Tampilan Keranjang Belanja',
                        'title-info'              => 'Atur tampilan pajak di dalam keranjang belanja',
                        'display-prices'          => 'Tampilkan Harga',
                        'display-subtotal'        => 'Tampilkan Subtotal',
                        'display-shipping-amount' => 'Tampilkan Jumlah Pengiriman',
                        'excluding-tax'           => 'Tanpa Pajak',
                        'including-tax'           => 'Termasuk Pajak',
                        'both'                    => 'Tanpa dan Termasuk Kedua-duanya',
                    ],

                    'sales' => [
                        'title'                   => 'Pengaturan Tampilan Pesanan, Faktur, dan Pengembalian',
                        'title-info'              => 'Atur tampilan pajak pada pesanan, faktur, dan pengembalian',
                        'display-prices'          => 'Tampilkan Harga',
                        'display-subtotal'        => 'Tampilkan Subtotal',
                        'display-shipping-amount' => 'Tampilkan Jumlah Pengiriman',
                        'excluding-tax'           => 'Tanpa Pajak',
                        'including-tax'           => 'Termasuk Pajak',
                        'both'                    => 'Tanpa dan Termasuk Kedua-duanya',
                    ],
                ],

                'checkout' => [
                    'title' => 'Pembayaran',
                    'info'  => 'Atur checkout tamu, Aktifkan atau Nonaktifkan Mini Cart, ringkasan keranjang.',

                    'shopping-cart' => [
                        'cart-page'              => 'Halaman Keranjang',
                        'cart-page-info'         => 'Mengontrol visibilitas Halaman Keranjang untuk meningkatkan pengalaman berbelanja pengguna.',
                        'cross-sell'             => 'Produk Cross-sell',
                        'cross-sell-info'        => 'Aktifkan produk cross-sell untuk meningkatkan peluang penjualan tambahan.',
                        'estimate-shipping'      => 'Perkiraan Pengiriman',
                        'estimate-shipping-info' => 'Aktifkan perkiraan pengiriman untuk memberikan biaya pengiriman di muka.',
                        'guest-checkout'         => 'Izinkan checkout tamu',
                        'guest-checkout-info'    => 'Aktifkan checkout tamu untuk proses pembelian yang lebih cepat dan tanpa hambatan.',
                        'info'                   => 'Aktifkan checkout tamu, halaman keranjang, produk cross-sell, dan perkiraan pengiriman untuk meningkatkan kenyamanan pengguna dan memperlancar proses belanja untuk meningkatkan penjualan.',
                        'title'                  => 'Keranjang Belanja',
                    ],

                    'my-cart' => [
                        'display-item-quantities' => 'Tampilkan jumlah item',
                        'display-number-in-cart'  => 'Tampilkan jumlah item dalam keranjang',
                        'info'                    => 'Aktifkan pengaturan untuk My Cart agar menampilkan ringkasan jumlah item dan menampilkan total jumlah item dalam keranjang untuk pelacakan yang mudah.',
                        'summary'                 => 'Ringkasan',
                        'title'                   => 'Keranjang Saya',
                    ],

                    'mini-cart' => [
                        'display-mini-cart'    => 'Tampilkan Mini Cart',
                        'info'                 => 'Aktifkan pengaturan Mini Cart untuk menampilkan mini cart dan menampilkan Informasi Penawaran Mini Cart untuk akses cepat ke detail keranjang dan promosi.',
                        'mini-cart-offer-info' => 'Informasi Penawaran Mini Cart',
                        'title'                => 'Mini Cart',
                    ],
                ],
            ],
        ],
    ],

    'components' => [
        'layouts' => [
            'header' => [
                'account-title' => 'Akun',
                'app-version'   => 'Versi : :version',
                'logout'        => 'Keluar',
                'my-account'    => 'Akun Saya',
                'notifications' => 'Notifikasi',
                'visit-shop'    => 'Kunjungi Toko',

                'mega-search' => [
                    'categories'                      => 'Kategori',
                    'customers'                       => 'Pelanggan',
                    'explore-all-categories'          => 'Jelajahi semua kategori',
                    'explore-all-customers'           => 'Jelajahi semua pelanggan',
                    'explore-all-matching-categories' => 'Jelajahi semua kategori yang cocok dengan “:query” (:count)',
                    'explore-all-matching-customers'  => 'Jelajahi semua pelanggan yang cocok dengan “:query” (:count)',
                    'explore-all-matching-orders'     => 'Jelajahi semua Pesanan yang cocok dengan “:query” (:count)',
                    'explore-all-matching-products'   => 'Jelajahi semua produk yang cocok dengan “:query” (:count)',
                    'explore-all-orders'              => 'Jelajahi semua Pesanan',
                    'explore-all-products'            => 'Jelajahi semua produk',
                    'orders'                          => 'Pesanan',
                    'products'                        => 'Produk',
                    'sku'                             => 'SKU: :sku',
                    'title'                           => 'Pencarian Mega',
                ],
            ],

            'sidebar' => [
                'attribute-families'       => 'Keluarga Atribut',
                'attributes'               => 'Atribut',
                'booking-product'          => 'Pemesanan',
                'campaigns'                => 'Kampanye',
                'catalog'                  => 'Katalog',
                'categories'               => 'Kategori',
                'channels'                 => 'Saluran',
                'cms'                      => 'CMS',
                'collapse'                 => 'Tutup/Perkecil',
                'communications'           => 'Komunikasi',
                'configure'                => 'Konfigurasi',
                'currencies'               => 'Mata Uang',
                'customers'                => 'Pelanggan',
                'dashboard'                => 'Dasbor',
                'data-transfer'            => 'Transfer Data',
                'discount'                 => 'Diskon',
                'email-templates'          => 'Template Email',
                'events'                   => 'Acara',
                'exchange-rates'           => 'Kurs',
                'gdpr-data-requests'       => 'Permintaan Data GDPR',
                'groups'                   => 'Grup',
                'imports'                  => 'Impor',
                'inventory-sources'        => 'Sumber Inventaris',
                'invoices'                 => 'Faktur',
                'locales'                  => 'Bahasa & Lokalisasi',
                'marketing'                => 'Pemasaran',
                'mode'                     => 'Mode Gelap',
                'newsletter-subscriptions' => 'Langganan Newsletter',
                'orders'                   => 'Pesanan',
                'products'                 => 'Produk',
                'promotions'               => 'Promosi',
                'refunds'                  => 'Pengembalian Dana',
                'reporting'                => 'Pelaporan',
                'reviews'                  => 'Ulasan',
                'roles'                    => 'Peran',
                'sales'                    => 'Penjualan',
                'search-seo'               => 'Pencarian & SEO',
                'search-synonyms'          => 'Sinonim Pencarian',
                'search-terms'             => 'Istilah Pencarian',
                'settings'                 => 'Pengaturan',
                'shipments'                => 'Pengiriman',
                'sitemaps'                 => 'Peta Situs',
                'tax-categories'           => 'Kategori Pajak',
                'tax-rates'                => 'Tarif Pajak',
                'taxes'                    => 'Pajak',
                'themes'                   => 'Tema',
                'transactions'             => 'Transaksi',
                'url-rewrites'             => 'Penulisan Ulang URL',
                'users'                    => 'Pengguna',
            ],

            'powered-by' => [
                'description' => 'Dibuat oleh :bagisto, proyek open-source dari :webkul.',
            ],
        ],

        'datagrid' => [
            'index' => [
                'no-records-selected'              => 'Tidak ada data yang dipilih.',
                'must-select-a-mass-action-option' => 'Anda harus memilih opsi tindakan massal.',
                'must-select-a-mass-action'        => 'Anda harus memilih tindakan massal.',
            ],

            'toolbar' => [
                'length-of' => ':length dari',
                'of'        => 'dari',
                'per-page'  => 'Per Halaman',
                'results'   => ':total Hasil',
                'selected'  => ':total Terpilih',

                'mass-actions' => [
                    'submit'        => 'Kirim',
                    'select-option' => 'Pilih Opsi',
                    'select-action' => 'Pilih Tindakan',
                ],

                'filter' => [
                    'apply-filters-btn' => 'Terapkan Filter',
                    'back-btn'          => 'Kembali',
                    'create-new-filter' => 'Buat Filter Baru',
                    'custom-filters'    => 'Filter Kustom',
                    'delete-error'      => 'Terjadi kesalahan saat menghapus filter, coba lagi.',
                    'delete-success'    => 'Filter berhasil dihapus.',
                    'empty-description' => 'Tidak ada filter yang dipilih untuk disimpan. Harap pilih filter untuk disimpan.',
                    'empty-title'       => 'Tambahkan Filter untuk Disimpan',
                    'name'              => 'Nama',
                    'quick-filters'     => 'Filter Cepat',
                    'save-btn'          => 'Simpan',
                    'save-filter'       => 'Simpan Filter',
                    'saved-success'     => 'Filter berhasil disimpan.',
                    'selected-filters'  => 'Filter Terpilih',
                    'title'             => 'Filter',
                    'update'            => 'Perbarui',
                    'update-filter'     => 'Perbarui Filter',
                    'updated-success'   => 'Filter berhasil diperbarui.',
                ],

                'search' => [
                    'title' => 'Pencarian',
                ],
            ],

            'filters' => [
                'select' => 'Pilih',
                'title'  => 'Filter',

                'dropdown' => [
                    'searchable' => [
                        'atleast-two-chars' => 'Ketik setidaknya 2 karakter...',
                        'no-results'        => 'Tidak ada hasil ditemukan...',
                    ],
                ],

                'custom-filters' => [
                    'clear-all' => 'Hapus Semua',
                    'title'     => 'Filter Kustom',
                ],

                'boolean-options' => [
                    'false' => 'Salah',
                    'true'  => 'Benar',
                ],

                'date-options' => [
                    'last-month'        => 'Bulan Lalu',
                    'last-six-months'   => '6 Bulan Terakhir',
                    'last-three-months' => '3 Bulan Terakhir',
                    'this-month'        => 'Bulan Ini',
                    'this-week'         => 'Minggu Ini',
                    'this-year'         => 'Tahun Ini',
                    'today'             => 'Hari Ini',
                    'yesterday'         => 'Kemarin',
                ],
            ],

            'table' => [
                'actions'              => 'Tindakan',
                'no-records-available' => 'Tidak Ada Data Tersedia.',
            ],
        ],

        'modal' => [
            'confirm' => [
                'agree-btn'    => 'Setuju',
                'disagree-btn' => 'Tidak Setuju',
                'message'      => 'Apakah Anda yakin ingin melakukan tindakan ini?',
                'title'        => 'Apakah Anda yakin?',
            ],
        ],

        'products' => [
            'search' => [
                'add-btn'       => 'Tambahkan Produk Terpilih',
                'empty-info'    => 'Tidak ada produk yang tersedia untuk kata kunci pencarian.',
                'empty-title'   => 'Tidak ada produk ditemukan',
                'product-image' => 'Gambar Produk',
                'qty'           => ':qty Tersedia',
                'sku'           => 'SKU - :sku',
                'title'         => 'Pilih Produk',
            ],
        ],

        'media' => [
            'images' => [
                'add-image-btn'     => 'Tambah Gambar',
                'ai-add-image-btn'  => 'AI Magic',
                'ai-btn-info'       => 'Hasilkan Gambar',
                'allowed-types'     => 'png, jpeg, jpg',
                'not-allowed-error' => 'Hanya file gambar (.jpeg, .jpg, .png, dll) yang diperbolehkan.',

                'ai-generation' => [
                    '1024x1024'        => '1024x1024',
                    '1024x1792'        => '1024x1792',
                    '1792x1024'        => '1792x1024',
                    'apply'            => 'Terapkan',
                    'dall-e-2'         => 'Dall.E 2',
                    'dall-e-3'         => 'Dall.E 3',
                    'generate'         => 'Hasilkan',
                    'generating'       => 'Sedang Dihasilkan...',
                    'hd'               => 'HD',
                    'model'            => 'Model',
                    'number-of-images' => 'Jumlah Gambar',
                    'prompt'           => 'Prompt',
                    'quality'          => 'Kualitas',
                    'regenerate'       => 'Hasilkan Ulang',
                    'regenerating'     => 'Sedang Menghasilkan Ulang...',
                    'size'             => 'Ukuran',
                    'standard'         => 'Standar',
                    'title'            => 'Generasi Gambar AI',
                ],

                'placeholders' => [
                    'front'     => 'Depan',
                    'next'      => 'Selanjutnya',
                    'size'      => 'Ukuran',
                    'use-cases' => 'Kasus Penggunaan',
                    'zoom'      => 'Perbesar',
                ],
            ],

            'videos' => [
                'add-video-btn'     => 'Tambah Video',
                'allowed-types'     => 'mp4, webm, mkv',
                'not-allowed-error' => 'Hanya file video (.mp4, .mov, .ogg, dll) yang diperbolehkan.',
            ],
        ],

        'tinymce' => [
            'ai-btn-tile' => 'Magic AI',

            'ai-generation' => [
                'apply'                    => 'Terapkan',
                'deepseek-r1-8b'           => 'DeepSeek R1 (8b)',
                'enabled'                  => 'Aktifkan',
                'gemini-2-0-flash'         => 'Gemini 2.0 Flash',
                'generate'                 => 'Buat',
                'generated-content'        => 'Konten yang Dihasilkan',
                'generated-content-info'   => 'Konten yang dihasilkan AI bisa saja menyesatkan. Harap periksa konten yang dihasilkan sebelum diterapkan.',
                'generating'               => 'Membuat...',
                'gpt-4-turbo'              => 'OpenAI gpt-4 Turbo',
                'gpt-4o'                   => 'OpenAI gpt-4o',
                'gpt-4o-mini'              => 'OpenAI gpt-4o Mini',
                'llama-groq'               => 'Llama 3.3 (Groq)',
                'llama3-1-8b'              => '(Ollama) Llama 3.1 (8B)',
                'llama3-2-1b'              => '(Ollama) Llama 3.2 (1B)',
                'llama3-2-3b'              => '(Ollama) Llama 3.2 (3B)',
                'llama3-8b'                => '(Ollama) Llama 3 (8B)',
                'llava-7b'                 => 'Llava (7b)',
                'mistral-7b'               => 'Mistral (7b)',
                'model'                    => 'Model',
                'orca-mini'                => 'Orca Mini',
                'phi3-5'                   => 'Phi 3.5',
                'prompt'                   => 'Prompt',
                'qwen2-5-0-5b'             => 'Qwen 2.5 (0.5b)',
                'qwen2-5-1-5b'             => 'Qwen 2.5 (1.5b)',
                'qwen2-5-14b'              => 'Qwen 2.5 (14b)',
                'qwen2-5-3b'               => 'Qwen 2.5 (3b)',
                'qwen2-5-7b'               => 'Qwen 2.5 (7b)',
                'starling-lm-7b'           => 'Starling-lm (7b)',
                'title'                    => 'AI Assistance',
                'vicuna-13b'               => 'Vicuna (13b)',
                'vicuna-7b'                => 'Vicuna (7b)',
            ],
        ],
    ],

    'acl' => [
        'addresses'                => 'Alamat',
        'attribute-families'       => 'Keluarga Atribut',
        'attributes'               => 'Atribut',
        'campaigns'                => 'Kampanye',
        'cancel'                   => 'Batalkan',
        'cart-rules'               => 'Aturan Keranjang',
        'catalog'                  => 'Katalog',
        'catalog-rules'            => 'Aturan Katalog',
        'categories'               => 'Kategori',
        'channels'                 => 'Saluran',
        'cms'                      => 'CMS',
        'communications'           => 'Komunikasi',
        'configure'                => 'Konfigurasi',
        'copy'                     => 'Salin',
        'create'                   => 'Buat',
        'currencies'               => 'Mata Uang',
        'customers'                => 'Pelanggan',
        'dashboard'                => 'Dasbor',
        'data-transfer'            => 'Transfer Data',
        'delete'                   => 'Hapus',
        'edit'                     => 'Sunting',
        'email-templates'          => 'Template Email',
        'events'                   => 'Acara',
        'exchange-rates'           => 'Kurs',
        'gdpr'                     => 'GDPR',
        'groups'                   => 'Grup',
        'import'                   => 'Impor',
        'imports'                  => 'Impor',
        'inventory-sources'        => 'Sumber Inventaris',
        'invoices'                 => 'Faktur',
        'locales'                  => 'Bahasa & Lokalisasi',
        'marketing'                => 'Pemasaran',
        'newsletter-subscriptions' => 'Langganan Newsletter',
        'note'                     => 'Catatan',
        'orders'                   => 'Pesanan',
        'products'                 => 'Produk',
        'promotions'               => 'Promosi',
        'refunds'                  => 'Pengembalian Dana',
        'reporting'                => 'Pelaporan',
        'reviews'                  => 'Ulasan',
        'roles'                    => 'Peran',
        'sales'                    => 'Penjualan',
        'search-seo'               => 'Pencarian & SEO',
        'search-synonyms'          => 'Sinonim Pencarian',
        'search-terms'             => 'Istilah Pencarian',
        'settings'                 => 'Pengaturan',
        'shipments'                => 'Pengiriman',
        'sitemaps'                 => 'Peta Situs',
        'subscribers'              => 'Pelanggan Newsletter',
        'tax-categories'           => 'Kategori Pajak',
        'tax-rates'                => 'Tarif Pajak',
        'taxes'                    => 'Pajak',
        'themes'                   => 'Tema',
        'transactions'             => 'Transaksi',
        'url-rewrites'             => 'Penulisan Ulang URL',
        'users'                    => 'Pengguna',
        'view'                     => 'Lihat',
    ],

    'errors' => [
        'dashboard' => 'Dasbor',
        'go-back'   => 'Kembali',
        'support'   => 'Jika masalah berlanjut, hubungi kami di <a href=":link" class=":class">:email</a> untuk mendapatkan bantuan.',

        '404' => [
            'description' => 'Ups! Halaman yang Anda cari sedang liburan. Sepertinya kami tidak dapat menemukan apa yang Anda cari.',
            'title'       => '404 Halaman Tidak Ditemukan',
        ],

        '401' => [
            'description' => 'Ups! Sepertinya Anda tidak diizinkan untuk mengakses halaman ini. Anda tampaknya kekurangan kredensial yang diperlukan.',
            'title'       => '401 Tidak Sah',
        ],

        '403' => [
            'description' => 'Ups! Halaman ini tidak dapat diakses. Sepertinya Anda tidak memiliki izin yang diperlukan untuk melihat konten ini.',
            'title'       => '403 Terlarang',
        ],

        '500' => [
            'description' => 'Ups! Terjadi kesalahan. Sepertinya kami sedang kesulitan memuat halaman yang Anda cari.',
            'title'       => '500 Kesalahan Server Internal',
        ],

        '503' => [
            'description' => 'Ups! Sepertinya kami sedang dalam pemeliharaan sementara. Silakan coba lagi beberapa saat lagi.',
            'title'       => '503 Layanan Tidak Tersedia',
        ],
    ],

    'export' => [
        'csv'        => 'CSV',
        'download'   => 'Unduh',
        'export'     => 'Ekspor',
        'no-records' => 'Tidak ada yang dapat diekspor',
        'xls'        => 'XLS',
        'xlsx'       => 'XLSX',
    ],

    'validations' => [
        'slug-being-used' => 'Slug ini sedang digunakan pada kategori atau produk.',
        'slug-reserved'   => 'Slug ini sudah dipreservasi.',
    ],

    'footer' => [
        'copy-right' => 'Didukung oleh <a href="https://bagisto.com/" target="_blank">Bagisto</a>, proyek komunitas yang dikembangkan oleh <a href="https://webkul.com/" target="_blank">Webkul</a>',
    ],

    'emails' => [
        'dear'   => 'Kepada Yth. :admin_name',
        'thanks' => 'Jika Anda membutuhkan bantuan, silakan hubungi kami di <a href=":link" style=":style">:email</a>.<br/>Terima kasih!',

        'admin' => [
            'forgot-password' => [
                'description'    => 'Anda menerima email ini karena kami menerima permintaan reset kata sandi untuk akun Anda.',
                'greeting'       => 'Lupa Kata Sandi!',
                'reset-password' => 'Reset Kata Sandi',
                'subject'        => 'Email Reset Kata Sandi',
            ],
        ],

        'customers' => [
            'registration' => [
                'description' => 'Akun pelanggan baru telah berhasil dibuat. Mereka sekarang dapat login menggunakan alamat email dan kata sandi. Setelah login, mereka dapat mengakses berbagai layanan, termasuk melihat pesanan sebelumnya, mengelola daftar keinginan, dan memperbarui informasi akun mereka.',
                'greeting'    => 'Kami mengucapkan selamat datang kepada pelanggan baru, :customer_name yang baru saja terdaftar bersama kami!',
                'subject'     => 'Pendaftaran Pelanggan Baru',
            ],

            'gdpr' => [
                'new-delete-request' => 'Permintaan Baru untuk Penghapusan Data',
                'new-update-request' => 'Permintaan Baru untuk Pembaruan Data',

                'new-request' => [
                    'customer-name'  => 'Nama Pelanggan : ',
                    'delete-summary' => 'Ringkasan Permintaan Penghapusan',
                    'message'        => 'Pesan : ',
                    'request-status' => 'Status Permintaan : ',
                    'request-type'   => 'Jenis Permintaan : ',
                    'update-summary' => 'Ringkasan Permintaan Pembaruan',
                ],

                'status-update' => [
                    'subject'        => 'Permintaan GDPR Telah Diperbarui',
                    'summary'        => 'Status Permintaan GDPR Telah Diperbarui',
                    'request-status' => 'Status Permintaan:',
                    'request-type'   => 'Jenis Permintaan:',
                    'message'        => 'Pesan:',
                ],
            ],
        ],

        'orders' => [
            'created' => [
                'greeting' => 'Anda memiliki pesanan baru :order_id yang ditempatkan pada :created_at',
                'subject'  => 'Konfirmasi Pesanan Baru',
                'summary'  => 'Ringkasan Pesanan',
                'title'    => 'Konfirmasi Pesanan!',
            ],

            'invoiced' => [
                'greeting' => 'Faktur Anda #:invoice_id untuk pesanan :order_id yang dibuat pada :created_at',
                'subject'  => 'Konfirmasi Faktur Baru',
                'summary'  => 'Ringkasan Faktur',
                'title'    => 'Konfirmasi Faktur!',
            ],

            'shipped' => [
                'greeting' => 'Pesanan :order_id yang ditempatkan pada :created_at telah dikirim',
                'subject'  => 'Konfirmasi Pengiriman Baru',
                'summary'  => 'Ringkasan Pengiriman',
                'title'    => 'Pesanan Dikirim!',
            ],

            'inventory-source' => [
                'greeting' => 'Pesanan :order_id yang ditempatkan pada :created_at telah dikirim',
                'subject'  => 'Konfirmasi Pengiriman Baru',
                'summary'  => 'Ringkasan Pengiriman',
                'title'    => 'Pesanan Dikirim!',
            ],

            'refunded' => [
                'greeting' => 'Pesanan :order_id yang ditempatkan pada :created_at telah dibatalkan',
                'subject'  => 'Konfirmasi Pengembalian Dana Baru',
                'summary'  => 'Ringkasan Pengembalian Dana',
                'title'    => 'Pesanan Dibayar Kembali!',
            ],

            'canceled' => [
                'greeting' => 'Pesanan :order_id yang ditempatkan pada :created_at telah dibatalkan',
                'subject'  => 'Pesanan Dibatalkan',
                'summary'  => 'Ringkasan Pesanan',
                'title'    => 'Pesanan Dibatalkan!',
            ],

            'billing-address'            => 'Alamat Penagihan',
            'carrier'                    => 'Pengangkut',
            'contact'                    => 'Kontak',
            'discount'                   => 'Diskon',
            'excl-tax'                   => 'Tanpa Pajak: ',
            'grand-total'                => 'Total Keseluruhan',
            'name'                       => 'Nama',
            'payment'                    => 'Pembayaran',
            'price'                      => 'Harga',
            'qty'                        => 'Jumlah',
            'shipping-address'           => 'Alamat Pengiriman',
            'shipping-handling-excl-tax' => 'Pengiriman (Tanpa Pajak)',
            'shipping-handling-incl-tax' => 'Pengiriman (Dengan Pajak)',
            'shipping-handling'          => 'Biaya Pengiriman',
            'shipping'                   => 'Pengiriman',
            'sku'                        => 'SKU',
            'subtotal-excl-tax'          => 'Subtotal (Tanpa Pajak)',
            'subtotal-incl-tax'          => 'Subtotal (Dengan Pajak)',
            'subtotal'                   => 'Subtotal',
            'tax'                        => 'Pajak',
            'tracking-number'            => 'Nomor Pelacakan : :tracking_number',
        ],
    ],
];
