{"__meta": {"id": "01K2D0E3NMZRWGQBGH5ARDK3K1", "datetime": "2025-08-11 22:16:50", "utime": **********.548989, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/admin/dashboard", "ip": "::1"}, "modules": {"count": 2, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\ChannelTranslation (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "duration": 2.02, "duration_str": "2.02s", "connection": "Online_store"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 17.51, "duration_str": "17.51s", "connection": "Online_store"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 1.09, "duration_str": "1.09s", "connection": "Online_store"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.050935, "end": **********.573991, "duration": 1.5230560302734375, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": **********.050935, "relative_start": 0, "end": **********.790651, "relative_end": **********.790651, "duration": 0.****************, "duration_str": "740ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.790673, "relative_start": 0.****************, "end": **********.573994, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "783ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.817947, "relative_start": 0.****************, "end": **********.823496, "relative_end": **********.823496, "duration": 0.005549192428588867, "duration_str": "5.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.006431, "relative_start": 0.***************, "end": **********.545461, "relative_end": **********.545461, "duration": 0.****************, "duration_str": "539ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::dashboard.index", "start": **********.012905, "relative_start": 0.****************, "end": **********.012905, "relative_end": **********.012905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::dashboard.over-all-details", "start": **********.036257, "relative_start": 0.****************, "end": **********.036257, "relative_end": **********.036257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.over-all-details", "start": **********.040258, "relative_start": 0.9893229007720947, "end": **********.040258, "relative_end": **********.040258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.over-all-details", "start": **********.04218, "relative_start": 0.9912450313568115, "end": **********.04218, "relative_end": **********.04218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::dashboard.todays-details", "start": **********.066384, "relative_start": 1.015449047088623, "end": **********.066384, "relative_end": **********.066384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.todays-details", "start": **********.071083, "relative_start": 1.0201480388641357, "end": **********.071083, "relative_end": **********.071083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.todays-details", "start": **********.074781, "relative_start": 1.023845911026001, "end": **********.074781, "relative_end": **********.074781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::dashboard.stock-threshold-products", "start": **********.080679, "relative_start": 1.0297439098358154, "end": **********.080679, "relative_end": **********.080679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.stock-threshold-products", "start": **********.087504, "relative_start": 1.0365688800811768, "end": **********.087504, "relative_end": **********.087504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.stock-threshold-products", "start": **********.091638, "relative_start": 1.0407030582427979, "end": **********.091638, "relative_end": **********.091638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::dashboard.total-sales", "start": **********.126314, "relative_start": 1.0753788948059082, "end": **********.126314, "relative_end": **********.126314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.total-sales", "start": **********.131305, "relative_start": 1.0803699493408203, "end": **********.131305, "relative_end": **********.131305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.total-sales", "start": **********.134621, "relative_start": 1.0836858749389648, "end": **********.134621, "relative_end": **********.134621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.charts.bar", "start": **********.139566, "relative_start": 1.0886309146881104, "end": **********.139566, "relative_end": **********.139566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::dashboard.total-visitors", "start": **********.144049, "relative_start": 1.093113899230957, "end": **********.144049, "relative_end": **********.144049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.total-sales", "start": **********.148139, "relative_start": 1.0972039699554443, "end": **********.148139, "relative_end": **********.148139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.total-sales", "start": **********.150797, "relative_start": 1.0998618602752686, "end": **********.150797, "relative_end": **********.150797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.charts.bar", "start": **********.154446, "relative_start": 1.103510856628418, "end": **********.154446, "relative_end": **********.154446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::dashboard.top-selling-products", "start": **********.1581, "relative_start": 1.1071648597717285, "end": **********.1581, "relative_end": **********.1581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.top-selling-products", "start": **********.16246, "relative_start": 1.111525058746338, "end": **********.16246, "relative_end": **********.16246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.top-selling-products", "start": **********.164297, "relative_start": 1.1133620738983154, "end": **********.164297, "relative_end": **********.164297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::dashboard.top-customers", "start": **********.167785, "relative_start": 1.1168498992919922, "end": **********.167785, "relative_end": **********.167785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.top-customers", "start": **********.171588, "relative_start": 1.1206529140472412, "end": **********.171588, "relative_end": **********.171588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.dashboard.top-customers", "start": **********.174135, "relative_start": 1.1231999397277832, "end": **********.174135, "relative_end": **********.174135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.menu.item", "start": **********.179413, "relative_start": 1.1284780502319336, "end": **********.179413, "relative_end": **********.179413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.181932, "relative_start": 1.1309969425201416, "end": **********.181932, "relative_end": **********.181932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flat-picker.date", "start": **********.185523, "relative_start": 1.1345880031585693, "end": **********.185523, "relative_end": **********.185523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flat-picker.date", "start": **********.187219, "relative_start": 1.1362838745117188, "end": **********.187219, "relative_end": **********.187219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.index", "start": **********.315829, "relative_start": 1.2648940086364746, "end": **********.315829, "relative_end": **********.315829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flash-group.index", "start": **********.405034, "relative_start": 1.3540990352630615, "end": **********.405034, "relative_end": **********.405034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flash-group.item", "start": **********.408214, "relative_start": 1.3572790622711182, "end": **********.408214, "relative_end": **********.408214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.modal.confirm", "start": **********.411322, "relative_start": 1.3603870868682861, "end": **********.411322, "relative_end": **********.411322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.header.index", "start": **********.414496, "relative_start": 1.3635609149932861, "end": **********.414496, "relative_end": **********.414496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.429485, "relative_start": 1.3785500526428223, "end": **********.429485, "relative_end": **********.429485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.43126, "relative_start": 1.3803250789642334, "end": **********.43126, "relative_end": **********.43126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.drawer.index", "start": **********.485544, "relative_start": 1.4346089363098145, "end": **********.485544, "relative_end": **********.485544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.products", "start": **********.488285, "relative_start": 1.4373500347137451, "end": **********.488285, "relative_end": **********.488285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.orders", "start": **********.491923, "relative_start": 1.4409880638122559, "end": **********.491923, "relative_end": **********.491923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.categories", "start": **********.494724, "relative_start": 1.443789005279541, "end": **********.494724, "relative_end": **********.494724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.customers", "start": **********.497472, "relative_start": 1.4465370178222656, "end": **********.497472, "relative_end": **********.497472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.504177, "relative_start": 1.4532420635223389, "end": **********.504177, "relative_end": **********.504177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.sidebar.index", "start": **********.511323, "relative_start": 1.460387945175171, "end": **********.511323, "relative_end": **********.511323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.tabs", "start": **********.542743, "relative_start": 1.4918079376220703, "end": **********.542743, "relative_end": **********.542743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 41544384, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 43, "nb_templates": 43, "templates": [{"name": "admin::dashboard.index", "param_count": null, "params": [], "start": **********.012746, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/dashboard/index.blade.phpadmin::dashboard.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fdashboard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::dashboard.over-all-details", "param_count": null, "params": [], "start": **********.036074, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/dashboard/over-all-details.blade.phpadmin::dashboard.over-all-details", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fdashboard%2Fover-all-details.blade.php&line=1", "ajax": false, "filename": "over-all-details.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.over-all-details", "param_count": null, "params": [], "start": **********.040068, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/over-all-details.blade.phpadmin::components.shimmer.dashboard.over-all-details", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Fover-all-details.blade.php&line=1", "ajax": false, "filename": "over-all-details.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.over-all-details", "param_count": null, "params": [], "start": **********.041992, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/over-all-details.blade.phpadmin::components.shimmer.dashboard.over-all-details", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Fover-all-details.blade.php&line=1", "ajax": false, "filename": "over-all-details.blade.php", "line": "?"}}, {"name": "admin::dashboard.todays-details", "param_count": null, "params": [], "start": **********.066052, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/dashboard/todays-details.blade.phpadmin::dashboard.todays-details", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fdashboard%2Ftodays-details.blade.php&line=1", "ajax": false, "filename": "todays-details.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.todays-details", "param_count": null, "params": [], "start": **********.070767, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/todays-details.blade.phpadmin::components.shimmer.dashboard.todays-details", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Ftodays-details.blade.php&line=1", "ajax": false, "filename": "todays-details.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.todays-details", "param_count": null, "params": [], "start": **********.07446, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/todays-details.blade.phpadmin::components.shimmer.dashboard.todays-details", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Ftodays-details.blade.php&line=1", "ajax": false, "filename": "todays-details.blade.php", "line": "?"}}, {"name": "admin::dashboard.stock-threshold-products", "param_count": null, "params": [], "start": **********.080339, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/dashboard/stock-threshold-products.blade.phpadmin::dashboard.stock-threshold-products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fdashboard%2Fstock-threshold-products.blade.php&line=1", "ajax": false, "filename": "stock-threshold-products.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.stock-threshold-products", "param_count": null, "params": [], "start": **********.087169, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/stock-threshold-products.blade.phpadmin::components.shimmer.dashboard.stock-threshold-products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Fstock-threshold-products.blade.php&line=1", "ajax": false, "filename": "stock-threshold-products.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.stock-threshold-products", "param_count": null, "params": [], "start": **********.091282, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/stock-threshold-products.blade.phpadmin::components.shimmer.dashboard.stock-threshold-products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Fstock-threshold-products.blade.php&line=1", "ajax": false, "filename": "stock-threshold-products.blade.php", "line": "?"}}, {"name": "admin::dashboard.total-sales", "param_count": null, "params": [], "start": **********.125961, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/dashboard/total-sales.blade.phpadmin::dashboard.total-sales", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fdashboard%2Ftotal-sales.blade.php&line=1", "ajax": false, "filename": "total-sales.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.total-sales", "param_count": null, "params": [], "start": **********.131007, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/total-sales.blade.phpadmin::components.shimmer.dashboard.total-sales", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Ftotal-sales.blade.php&line=1", "ajax": false, "filename": "total-sales.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.total-sales", "param_count": null, "params": [], "start": **********.134326, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/total-sales.blade.phpadmin::components.shimmer.dashboard.total-sales", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Ftotal-sales.blade.php&line=1", "ajax": false, "filename": "total-sales.blade.php", "line": "?"}}, {"name": "admin::components.charts.bar", "param_count": null, "params": [], "start": **********.138901, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/charts/bar.blade.phpadmin::components.charts.bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcharts%2Fbar.blade.php&line=1", "ajax": false, "filename": "bar.blade.php", "line": "?"}}, {"name": "admin::dashboard.total-visitors", "param_count": null, "params": [], "start": **********.143759, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/dashboard/total-visitors.blade.phpadmin::dashboard.total-visitors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fdashboard%2Ftotal-visitors.blade.php&line=1", "ajax": false, "filename": "total-visitors.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.total-sales", "param_count": null, "params": [], "start": **********.147844, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/total-sales.blade.phpadmin::components.shimmer.dashboard.total-sales", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Ftotal-sales.blade.php&line=1", "ajax": false, "filename": "total-sales.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.total-sales", "param_count": null, "params": [], "start": **********.150503, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/total-sales.blade.phpadmin::components.shimmer.dashboard.total-sales", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Ftotal-sales.blade.php&line=1", "ajax": false, "filename": "total-sales.blade.php", "line": "?"}}, {"name": "admin::components.charts.bar", "param_count": null, "params": [], "start": **********.154128, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/charts/bar.blade.phpadmin::components.charts.bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcharts%2Fbar.blade.php&line=1", "ajax": false, "filename": "bar.blade.php", "line": "?"}}, {"name": "admin::dashboard.top-selling-products", "param_count": null, "params": [], "start": **********.157833, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/dashboard/top-selling-products.blade.phpadmin::dashboard.top-selling-products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fdashboard%2Ftop-selling-products.blade.php&line=1", "ajax": false, "filename": "top-selling-products.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.top-selling-products", "param_count": null, "params": [], "start": **********.16226, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/top-selling-products.blade.phpadmin::components.shimmer.dashboard.top-selling-products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Ftop-selling-products.blade.php&line=1", "ajax": false, "filename": "top-selling-products.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.top-selling-products", "param_count": null, "params": [], "start": **********.164103, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/top-selling-products.blade.phpadmin::components.shimmer.dashboard.top-selling-products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Ftop-selling-products.blade.php&line=1", "ajax": false, "filename": "top-selling-products.blade.php", "line": "?"}}, {"name": "admin::dashboard.top-customers", "param_count": null, "params": [], "start": **********.167482, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/dashboard/top-customers.blade.phpadmin::dashboard.top-customers", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fdashboard%2Ftop-customers.blade.php&line=1", "ajax": false, "filename": "top-customers.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.top-customers", "param_count": null, "params": [], "start": **********.17139, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/top-customers.blade.phpadmin::components.shimmer.dashboard.top-customers", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Ftop-customers.blade.php&line=1", "ajax": false, "filename": "top-customers.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.dashboard.top-customers", "param_count": null, "params": [], "start": **********.173855, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/dashboard/top-customers.blade.phpadmin::components.shimmer.dashboard.top-customers", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fdashboard%2Ftop-customers.blade.php&line=1", "ajax": false, "filename": "top-customers.blade.php", "line": "?"}}, {"name": "admin::components.dropdown.menu.item", "param_count": null, "params": [], "start": **********.17923, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/dropdown/menu/item.blade.phpadmin::components.dropdown.menu.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "admin::components.dropdown.index", "param_count": null, "params": [], "start": **********.181749, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/dropdown/index.blade.phpadmin::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.flat-picker.date", "param_count": null, "params": [], "start": **********.185342, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/flat-picker/date.blade.phpadmin::components.flat-picker.date", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflat-picker%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}}, {"name": "admin::components.flat-picker.date", "param_count": null, "params": [], "start": **********.187029, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/flat-picker/date.blade.phpadmin::components.flat-picker.date", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflat-picker%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}}, {"name": "admin::components.layouts.index", "param_count": null, "params": [], "start": **********.315695, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/index.blade.phpadmin::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.flash-group.index", "param_count": null, "params": [], "start": **********.404856, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/flash-group/index.blade.phpadmin::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.flash-group.item", "param_count": null, "params": [], "start": **********.408032, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/flash-group/item.blade.phpadmin::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "admin::components.modal.confirm", "param_count": null, "params": [], "start": **********.411156, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/modal/confirm.blade.phpadmin::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}}, {"name": "admin::components.layouts.header.index", "param_count": null, "params": [], "start": **********.414313, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/header/index.blade.phpadmin::components.layouts.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.form.index", "param_count": null, "params": [], "start": **********.429363, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/form/index.blade.phpadmin::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.dropdown.index", "param_count": null, "params": [], "start": **********.431141, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/dropdown/index.blade.phpadmin::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.drawer.index", "param_count": null, "params": [], "start": **********.485414, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/drawer/index.blade.phpadmin::components.drawer.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdrawer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.header.mega-search.products", "param_count": null, "params": [], "start": **********.488159, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/products.blade.phpadmin::components.shimmer.header.mega-search.products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fproducts.blade.php&line=1", "ajax": false, "filename": "products.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.header.mega-search.orders", "param_count": null, "params": [], "start": **********.49171, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/orders.blade.phpadmin::components.shimmer.header.mega-search.orders", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Forders.blade.php&line=1", "ajax": false, "filename": "orders.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.header.mega-search.categories", "param_count": null, "params": [], "start": **********.49452, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/categories.blade.phpadmin::components.shimmer.header.mega-search.categories", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fcategories.blade.php&line=1", "ajax": false, "filename": "categories.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.header.mega-search.customers", "param_count": null, "params": [], "start": **********.497048, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/customers.blade.phpadmin::components.shimmer.header.mega-search.customers", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fcustomers.blade.php&line=1", "ajax": false, "filename": "customers.blade.php", "line": "?"}}, {"name": "admin::components.dropdown.index", "param_count": null, "params": [], "start": **********.503147, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/dropdown/index.blade.phpadmin::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.layouts.sidebar.index", "param_count": null, "params": [], "start": **********.511168, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/sidebar/index.blade.phpadmin::components.layouts.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.layouts.tabs", "param_count": null, "params": [], "start": **********.542662, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/tabs.blade.phpadmin::components.layouts.tabs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Ftabs.blade.php&line=1", "ajax": false, "filename": "tabs.blade.php", "line": "?"}}]}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.022390000000000004, "accumulated_duration_str": "22.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.963259, "duration": 0.01751, "duration_str": "17.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 78.205}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 109}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.9936988, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "Online_store", "explain": null, "start_percent": 78.205, "width_percent": 4.868}, {"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "type": "query", "params": [], "bindings": ["localhost", "http://localhost", "https://localhost"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 169}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 246}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 218}], "start": **********.101921, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "Online_store", "explain": null, "start_percent": 83.073, "width_percent": 9.022}, {"sql": "select * from `channel_translations` where `channel_translations`.`channel_id` = 1 and `channel_translations`.`channel_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 405}, {"index": 24, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 90}], "start": **********.2262099, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 92.095, "width_percent": 7.905}]}, "models": {"data": {"Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\ChannelTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannelTranslation.php&line=1", "ajax": false, "filename": "ChannelTranslation.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/dashboard", "action_name": "admin.dashboard.index", "controller_action": "Webkul\\Admin\\Http\\Controllers\\DashboardController@index", "uri": "GET admin/dashboard", "controller": "Webkul\\Admin\\Http\\Controllers\\DashboardController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=36\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/dashboard", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=36\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/DashboardController.php:36-42</a>", "middleware": "web, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "1.52s", "peak_memory": "42MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-304349838 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-304349838\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1544201329 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1544201329\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-556764458 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InNZS2M2TzNzRVZrSnlPNFZvS2pLWmc9PSIsInZhbHVlIjoiSnNLUkdyMlVIaFpmMjMzL0Z4RXM1ZEZBREtpU3lRMHFRQ1JSWGFqVURKaHhQdVFPVkRqRXI1QThyc1ZZbVp6Vythb0pOOXB5dHRNOFE2TUQwcGNIbzZROW5wM1NRVEdDd1gva2pVaWM5SVIyWVJvMW1MZG5TeEZRVFNEOFB1L1UiLCJtYWMiOiI4NTkzNmNhZjhjMjdiNDYyNjFmMTFjYTZhNjIzZTVjNjM2Mjk4NzQ0MmJjMzJmZjZjYjlmNTUzMzVmYjFmYWFlIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/sales/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1450 characters\">bagisto_session=eyJpdiI6ImpDWlNrdnV1KzVRcHJsU1NOQ1RaRHc9PSIsInZhbHVlIjoiNit6V3FHZEdhMzRvcGk4WnFBZUlOK09WTVZxNm5NbElac2JoaE5hV1krZGFOeExFMVpLZVcyMVdJOUFkdk0rRVZZczB3bkF0R0hxRzVjdnMrUm1KU3pkNmlHeGJYT2JuNXVyblByaUg1Qlozc3BmaEFzMkZEZmx4VzBTT3lydnEiLCJtYWMiOiIyYzI4MGJhZTYyZDJkN2I1MDdjMGE4YjIxM2M1ZGJiOTFiYWY3OGYxNGU5ZGIyZTg4ODlkN2U5NTY4ZGIwYTg4IiwidGFnIjoiIn0%3D; dark_mode=0; laravel_session=eyJpdiI6IkJ1bkdwR1A3SG9VNlBmSlgxR3orR0E9PSIsInZhbHVlIjoiY0lhNEVuSnY5YkZDYkRyemFpZGh0MkdzZk1JNVdxQlpSWHF0bEthRGh2aU5QMDhJZWgwZnNCS1ZZOXYrbm9tWTI5VVdsK1hXcGwzUVlxRk5ENSs5TXVhZG0xcFJMVGxIUC9IcjNjM1p4TFQ4dUJ5MSsyS0g0R2hsanlKS2ZXdFIiLCJtYWMiOiIxYWUzMGU3MWJlY2E1ZjZhZTU4OWRiOWJiZDM1MWMwNGYwNjNiMDc2NzAwZTI5MzYwOWNkODI0Zjk3YzRkN2NlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImV5MVhTOG1zY1dtVkp6TFpRUTJuVkE9PSIsInZhbHVlIjoiditJc0E3QVZyUlVuaWdFU1c4dDFSYkZtOGRxenpKOFJXeHBTR3hpUm0yN1lFdEhDbkxTTCtQSmdqNDBiTWFYZHVQZTRoemg1T2Q5eStudGgzejRqYkZMN3lHU05Vd2E5andLSEQ0TDZCa0FwOXZvWGV3OGlpRHlzTEFHU1htbzMiLCJtYWMiOiI0ZmYzNDllYmY3YjI0MGM3ZDNjZjM1YTRiMGFkOWE1Y2Q1MGY3ODRjODE5OWIxMjRiYjA2N2NmNjUxM2M5N2IyIiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6IjVPU21zbnVOamVBSWxVNDFTZWlka2c9PSIsInZhbHVlIjoiV0grNmhDM1R0ekk3djh5SDFIQlhxL1l1ZlhHZy9CYXM0bDZoYWJsL0hHZzI4SE9JU25Cdk9PajRyc2FkWGRKeXRoTGh4L3c5MThaMFZiUzNZL3BSaEpqOVJLUFBqTFQzbnl4OVU0Qjd2TDRNNkNGbE1IRkJYbW5GMlhNKzkrb04iLCJtYWMiOiIzYjEyZDM5NGYxYTdhNzI1MWQxNTVkYjI3NzcwMTZmNDEyNmI2NDhkMGRiMzdiMDAxMzgwYTI0NjBkODA1MDQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556764458\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1813374633 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bagisto_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QtNrPDgdBnix8dQJIc98sIh6jLcY9Lhsvc5bz0Lz</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TMRz3GXEM8sAcFV1fikDiVtK46lGff3YWRNL67O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813374633\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-953300364 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 16:46:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953300364\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1564573522 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"66 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/sales/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K2D0E239ZXHAWX6GA9G4J495</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1564573522\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/dashboard", "action_name": "admin.dashboard.index", "controller_action": "Webkul\\Admin\\Http\\Controllers\\DashboardController@index"}, "badge": null}}