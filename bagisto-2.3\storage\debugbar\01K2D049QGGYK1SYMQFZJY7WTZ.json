{"__meta": {"id": "01K2D049QGGYK1SYMQFZJY7WTZ", "datetime": "2025-08-11 22:11:29", "utime": **********.074878, "method": "POST", "uri": "/onlinestore/bagisto-2.3/public/api/checkout/onepage/orders", "ip": "::1"}, "modules": {"count": 9, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\AttributeFamily (8)", "Webkul\\Attribute\\Models\\Attribute (28)"], "views": [], "queries": [{"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 1.11, "duration_str": "1.11s", "connection": "Online_store"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 2.28, "duration_str": "2.28s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.87, "duration_str": "870ms", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.87, "duration_str": "870ms", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 1, "duration_str": "1s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 1.56, "duration_str": "1.56s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 0.9, "duration_str": "900ms", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 2.03, "duration_str": "2.03s", "connection": "Online_store"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 2.23, "duration_str": "2.23s", "connection": "Online_store"}]}, {"name": "Webkul\\CartRule", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `cart_rules` left join `cart_rule_customer_groups` on `cart_rules`.`id` = `cart_rule_customer_groups`.`cart_rule_id` left join `cart_rule_channels` on `cart_rules`.`id` = `cart_rule_channels`.`cart_rule_id` where `cart_rule_customer_groups`.`customer_group_id` = 1 and `cart_rule_channels`.`channel_id` = 1 and (`cart_rules`.`starts_from` <= '2025-08-11 22:08:19' or `cart_rules`.`starts_from` is null) and (`cart_rules`.`ends_till` >= '2025-08-11 22:08:19' or `cart_rules`.`ends_till` is null) and `status` = 1", "duration": 1.24, "duration_str": "1.24s", "connection": "Online_store"}]}, {"name": "Webkul\\CatalogRule", "models": [], "views": [], "queries": [{"sql": "select * from `catalog_rule_product_prices` where `catalog_rule_product_prices`.`product_id` = 11 and `catalog_rule_product_prices`.`product_id` is not null", "duration": 1.38, "duration_str": "1.38s", "connection": "Online_store"}]}, {"name": "Webkul\\Checkout", "models": ["Webkul\\Checkout\\Models\\Cart (3)", "Webkul\\Checkout\\Models\\CartItem (2)", "Webkul\\Checkout\\Models\\CartShippingRate (4)", "Webkul\\Checkout\\Models\\CartAddress (2)", "Webkul\\Checkout\\Models\\CartPayment (1)"], "views": [], "queries": [{"sql": "select * from `cart` where `cart`.`id` = 1 limit 1", "duration": 1.41, "duration_str": "1.41s", "connection": "Online_store"}, {"sql": "select * from `cart_items` where `cart_items`.`cart_id` = 1 and `cart_items`.`cart_id` is not null and `parent_id` is null", "duration": 1.63, "duration_str": "1.63s", "connection": "Online_store"}, {"sql": "select * from `cart_shipping_rates` where `cart_shipping_rates`.`cart_id` = 1 and `cart_shipping_rates`.`cart_id` is not null", "duration": 1.18, "duration_str": "1.18s", "connection": "Online_store"}, {"sql": "select * from `cart` where `cart`.`id` = 1 limit 1", "duration": 1.25, "duration_str": "1.25s", "connection": "Online_store"}, {"sql": "select * from `cart_items` where `cart_items`.`cart_id` = 1 and `cart_items`.`cart_id` is not null and `parent_id` is null", "duration": 1.31, "duration_str": "1.31s", "connection": "Online_store"}, {"sql": "select * from `cart_shipping_rates` where `cart_shipping_rates`.`cart_id` = 1 and `cart_shipping_rates`.`cart_id` is not null", "duration": 1.11, "duration_str": "1.11s", "connection": "Online_store"}, {"sql": "update `cart` set `items_qty` = 2, `grand_total` = 34, `base_grand_total` = 34, `sub_total` = 34, `base_sub_total` = 34, `tax_total` = 0, `base_tax_total` = 0, `discount_amount` = 0, `base_discount_amount` = 0, `shipping_amount` = 0, `base_shipping_amount` = 0, `sub_total_incl_tax` = 34, `base_sub_total_incl_tax` = 34, `cart`.`updated_at` = '2025-08-11 22:11:19' where `id` = 1", "duration": 4.92, "duration_str": "4.92s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 1 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 1.21, "duration_str": "1.21s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 1 and `addresses`.`cart_id` is not null and `address_type` = 'cart_billing' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 1.13, "duration_str": "1.13s", "connection": "Online_store"}, {"sql": "select * from `cart_payment` where `cart_payment`.`cart_id` = 1 and `cart_payment`.`cart_id` is not null limit 1", "duration": 1.06, "duration_str": "1.06s", "connection": "Online_store"}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 1 and `cart_items`.`parent_id` is not null", "duration": 1.23, "duration_str": "1.23s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`order_id` = 1 and `addresses`.`order_id` is not null and `address_type` in ('order_billing', 'order_shipping')", "duration": 1.65, "duration_str": "1.65s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`order_id` = 1 and `addresses`.`order_id` is not null and `address_type` in ('order_billing', 'order_shipping')", "duration": 1.74, "duration_str": "1.74s", "connection": "Online_store"}, {"sql": "select * from `cart` where `cart`.`id` = 1 limit 1", "duration": 2.58, "duration_str": "2.58s", "connection": "Online_store"}, {"sql": "update `cart` set `is_active` = 0, `cart`.`updated_at` = '2025-08-11 22:11:29' where `id` = 1", "duration": 7.17, "duration_str": "7.17s", "connection": "Online_store"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (4)", "Webkul\\Core\\Models\\Locale (3)", "Webkul\\Core\\Models\\Currency (3)", "Webkul\\Core\\Models\\ChannelTranslation (1)", "Webkul\\Core\\Models\\CoreConfig (2)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "duration": 23.44, "duration_str": "23.44s", "connection": "Online_store"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.29, "duration_str": "1.29s", "connection": "Online_store"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 1.67, "duration_str": "1.67s", "connection": "Online_store"}, {"sql": "select * from `currencies` where `code` = 'INR'", "duration": 1.15, "duration_str": "1.15s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.minimum_order.enable'", "duration": 1.17, "duration_str": "1.17s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.calculation.product_prices'", "duration": 1.24, "duration_str": "1.24s", "connection": "Online_store"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "duration": 1.23, "duration_str": "1.23s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.categories.product'", "duration": 1.03, "duration_str": "1.03s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.categories.shipping'", "duration": 0.98, "duration_str": "980ms", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.minimum_order.minimum_order_amount' and `channel_code` = 'default'", "duration": 1.02, "duration_str": "1.02s", "connection": "Online_store"}, {"sql": "select * from `channels` where `channels`.`id` = 1 limit 1", "duration": 0.97, "duration_str": "970ms", "connection": "Online_store"}, {"sql": "select * from `locales`", "duration": 1, "duration_str": "1s", "connection": "Online_store"}, {"sql": "select * from `locales` where `code` = 'en'", "duration": 1.77, "duration_str": "1.77s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.order_number.order_number_prefix' and `channel_code` = 'default'", "duration": 1.08, "duration_str": "1.08s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.order_number.order_number_length' and `channel_code` = 'default'", "duration": 0.94, "duration_str": "940ms", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.order_number.order_number_suffix' and `channel_code` = 'default'", "duration": 0.95, "duration_str": "950ms", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.order_number.order_number_generator' and `channel_code` = 'default'", "duration": 0.97, "duration_str": "970ms", "connection": "Online_store"}, {"sql": "select * from `channels` where `channels`.`id` = 1 limit 1", "duration": 0.98, "duration_str": "980ms", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'emails.general.notifications.emails.general.notifications.new_order_mail_to_admin'", "duration": 0.83, "duration_str": "830ms", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.admin_name' and `channel_code` = 'default'", "duration": 1.29, "duration_str": "1.29s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.admin_email' and `channel_code` = 'default'", "duration": 1.14, "duration_str": "1.14s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.payment_methods.cashondelivery.title' and `channel_code` = 'default' and `locale_code` = 'en'", "duration": 1.27, "duration_str": "1.27s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.payment_methods.cashondelivery.instructions' and `channel_code` = 'default' and `locale_code` = 'en'", "duration": 1.19, "duration_str": "1.19s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.sales.display_prices'", "duration": 1.21, "duration_str": "1.21s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.sales.display_subtotal'", "duration": 1.22, "duration_str": "1.22s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.sales.display_shipping_amount'", "duration": 1.37, "duration_str": "1.37s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'general.design.admin_logo.logo_image'", "duration": 1.16, "duration_str": "1.16s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.contact_name' and `channel_code` = 'default'", "duration": 1.16, "duration_str": "1.16s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.contact_email' and `channel_code` = 'default'", "duration": 1.13, "duration_str": "1.13s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.sender_name' and `channel_code` = 'default'", "duration": 1.29, "duration_str": "1.29s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.shop_email_from' and `channel_code` = 'default'", "duration": 1.2, "duration_str": "1.2s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.payment_methods.cashondelivery.generate_invoice' and `channel_code` = 'default'", "duration": 1.21, "duration_str": "1.21s", "connection": "Online_store"}, {"sql": "select * from `channels`", "duration": 3.55, "duration_str": "3.55s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'emails.general.notifications.emails.general.notifications.new_order'", "duration": 1.07, "duration_str": "1.07s", "connection": "Online_store"}, {"sql": "select * from `currencies`", "duration": 1.42, "duration_str": "1.42s", "connection": "Online_store"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 1.51, "duration_str": "1.51s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 1 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 1.21, "duration_str": "1.21s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 1 and `addresses`.`cart_id` is not null and `address_type` = 'cart_billing' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 1.13, "duration_str": "1.13s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`order_id` = 1 and `addresses`.`order_id` is not null and `address_type` in ('order_billing', 'order_shipping')", "duration": 1.65, "duration_str": "1.65s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`order_id` = 1 and `addresses`.`order_id` is not null and `address_type` in ('order_billing', 'order_shipping')", "duration": 1.74, "duration_str": "1.74s", "connection": "Online_store"}]}, {"name": "Webkul\\Inventory", "models": ["Webkul\\Inventory\\Models\\InventorySource (1)"], "views": [], "queries": [{"sql": "select `inventory_sources`.*, `channel_inventory_sources`.`channel_id` as `pivot_channel_id`, `channel_inventory_sources`.`inventory_source_id` as `pivot_inventory_source_id` from `inventory_sources` inner join `channel_inventory_sources` on `inventory_sources`.`id` = `channel_inventory_sources`.`inventory_source_id` where `channel_inventory_sources`.`channel_id` = 1", "duration": 3.42, "duration_str": "3.42s", "connection": "Online_store"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (13)", "Webkul\\Product\\Models\\ProductAttributeValue (154)", "Webkul\\Product\\Models\\ProductInventoryIndex (3)", "Webkul\\Product\\Models\\ProductInventory (2)", "Webkul\\Product\\Models\\ProductOrderedInventory (1)"], "views": [], "queries": [{"sql": "select * from `products` where `products`.`id` = 11 and `products`.`id` is not null limit 1", "duration": 1.28, "duration_str": "1.28s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 11 and `product_attribute_values`.`product_id` is not null", "duration": 1.56, "duration_str": "1.56s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 11 and `product_inventory_indices`.`product_id` is not null", "duration": 2.75, "duration_str": "2.75s", "connection": "Online_store"}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 11 and `product_customer_group_prices`.`product_id` is not null", "duration": 1.18, "duration_str": "1.18s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 11 and `products`.`id` is not null limit 1", "duration": 1.08, "duration_str": "1.08s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 11 limit 1", "duration": 0.95, "duration_str": "950ms", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 11 and `product_attribute_values`.`product_id` is not null", "duration": 1.35, "duration_str": "1.35s", "connection": "Online_store"}, {"sql": "select * from `product_inventories` where `product_inventories`.`product_id` = 11 and `product_inventories`.`product_id` is not null", "duration": 1.91, "duration_str": "1.91s", "connection": "Online_store"}, {"sql": "select * from `product_ordered_inventories` where `product_ordered_inventories`.`product_id` = 11 and `product_ordered_inventories`.`product_id` is not null and `channel_id` = 1 limit 1", "duration": 1.75, "duration_str": "1.75s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "duration": 0.91, "duration_str": "910ms", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 11 limit 1", "duration": 1.07, "duration_str": "1.07s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 11 limit 1", "duration": 0.85, "duration_str": "850ms", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "duration": 1.82, "duration_str": "1.82s", "connection": "Online_store"}, {"sql": "select * from `product_bundle_option_products` where `product_id` = 11", "duration": 20.55, "duration_str": "20.55s", "connection": "Online_store"}, {"sql": "select * from `product_grouped_products` where `associated_product_id` = 11", "duration": 1.93, "duration_str": "1.93s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 11 and `product_attribute_values`.`product_id` is not null", "duration": 1.5, "duration_str": "1.5s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 7 and `product_attribute_values`.`product_id` is not null", "duration": 1.51, "duration_str": "1.51s", "connection": "Online_store"}, {"sql": "select * from `products` where `id` in (11) order by FIELD(id, 11)", "duration": 1.12, "duration_str": "1.12s", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 11 and `product_inventory_indices`.`product_id` is not null", "duration": 5.96, "duration_str": "5.96s", "connection": "Online_store"}, {"sql": "select * from `product_inventories` where `product_inventories`.`product_id` = 11 and `product_inventories`.`product_id` is not null", "duration": 0.94, "duration_str": "940ms", "connection": "Online_store"}, {"sql": "select * from `product_ordered_inventories` where `product_ordered_inventories`.`product_id` = 11 and `product_ordered_inventories`.`product_id` is not null", "duration": 0.98, "duration_str": "980ms", "connection": "Online_store"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`id` = 8 limit 1", "duration": 0.82, "duration_str": "820ms", "connection": "Online_store"}, {"sql": "update `product_inventory_indices` set `qty` = 98, `product_inventory_indices`.`updated_at` = '2025-08-11 22:11:26' where `id` = 8", "duration": 0.75, "duration_str": "750ms", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` in (11)", "duration": 0.89, "duration_str": "890ms", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` in (7)", "duration": 0.84, "duration_str": "840ms", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "duration": 1.3, "duration_str": "1.3s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (11)", "duration": 1.26, "duration_str": "1.26s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` = 11 limit 1", "duration": 0.93, "duration_str": "930ms", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` in (11)", "duration": 2.86, "duration_str": "2.86s", "connection": "Online_store"}, {"sql": "select * from `products` where `products`.`id` in (7)", "duration": 1.95, "duration_str": "1.95s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "duration": 2.45, "duration_str": "2.45s", "connection": "Online_store"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (11)", "duration": 2.61, "duration_str": "2.61s", "connection": "Online_store"}]}, {"name": "Webkul\\Sales", "models": ["Webkul\\Sales\\Models\\Order (5)", "Webkul\\Sales\\Models\\OrderItem (9)", "Webkul\\Sales\\Models\\OrderAddress (4)", "Webkul\\Sales\\Models\\OrderPayment (4)"], "views": [], "queries": [{"sql": "select * from `addresses` where `addresses`.`cart_id` = 1 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 1.21, "duration_str": "1.21s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 1 and `addresses`.`cart_id` is not null and `address_type` = 'cart_billing' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "duration": 1.13, "duration_str": "1.13s", "connection": "Online_store"}, {"sql": "select * from `orders` order by `id` desc limit 1", "duration": 4.87, "duration_str": "4.87s", "connection": "Online_store"}, {"sql": "select * from `orders` where `orders`.`id` = 1 limit 1", "duration": 1.24, "duration_str": "1.24s", "connection": "Online_store"}, {"sql": "select * from `order_items` where `order_items`.`order_id` = 1 and `order_items`.`order_id` is not null and `parent_id` is null", "duration": 1.51, "duration_str": "1.51s", "connection": "Online_store"}, {"sql": "select * from `orders` where `orders`.`id` = 1 limit 1", "duration": 1.43, "duration_str": "1.43s", "connection": "Online_store"}, {"sql": "select * from `order_items` where `parent_id` is null and `order_items`.`order_id` in (1)", "duration": 1.21, "duration_str": "1.21s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`order_id` = 1 and `addresses`.`order_id` is not null and `address_type` in ('order_billing', 'order_shipping')", "duration": 1.65, "duration_str": "1.65s", "connection": "Online_store"}, {"sql": "select * from `order_payment` where `order_payment`.`order_id` = 1 and `order_payment`.`order_id` is not null limit 1", "duration": 1.24, "duration_str": "1.24s", "connection": "Online_store"}, {"sql": "select * from `orders` where `orders`.`id` = 1 limit 1", "duration": 1.32, "duration_str": "1.32s", "connection": "Online_store"}, {"sql": "select * from `order_items` where `parent_id` is null and `order_items`.`order_id` in (1)", "duration": 1.53, "duration_str": "1.53s", "connection": "Online_store"}, {"sql": "select * from `order_items` where `order_items`.`order_id` = 1 and `order_items`.`order_id` is not null and `parent_id` is null", "duration": 1.31, "duration_str": "1.31s", "connection": "Online_store"}, {"sql": "select * from `order_items` where `order_items`.`order_id` = 1 and `order_items`.`order_id` is not null", "duration": 3.18, "duration_str": "3.18s", "connection": "Online_store"}, {"sql": "select * from `order_payment` where `order_payment`.`order_id` = 1 and `order_payment`.`order_id` is not null limit 1", "duration": 0.99, "duration_str": "990ms", "connection": "Online_store"}, {"sql": "select * from `orders` where `orders`.`id` = 1 limit 1", "duration": 1.43, "duration_str": "1.43s", "connection": "Online_store"}, {"sql": "select * from `order_items` where `parent_id` is null and `order_items`.`order_id` in (1)", "duration": 1.27, "duration_str": "1.27s", "connection": "Online_store"}, {"sql": "select * from `order_items` where `order_items`.`order_id` in (1)", "duration": 1.25, "duration_str": "1.25s", "connection": "Online_store"}, {"sql": "select * from `order_payment` where `order_payment`.`order_id` in (1)", "duration": 1.02, "duration_str": "1.02s", "connection": "Online_store"}, {"sql": "select * from `addresses` where `addresses`.`order_id` = 1 and `addresses`.`order_id` is not null and `address_type` in ('order_billing', 'order_shipping')", "duration": 1.74, "duration_str": "1.74s", "connection": "Online_store"}, {"sql": "select * from `orders` where `orders`.`id` = 1 limit 1", "duration": 3.01, "duration_str": "3.01s", "connection": "Online_store"}, {"sql": "select * from `order_items` where `parent_id` is null and `order_items`.`order_id` in (1)", "duration": 2.79, "duration_str": "2.79s", "connection": "Online_store"}, {"sql": "select * from `order_items` where `order_items`.`order_id` in (1)", "duration": 2.5, "duration_str": "2.5s", "connection": "Online_store"}, {"sql": "select * from `order_payment` where `order_payment`.`order_id` in (1)", "duration": 2.31, "duration_str": "2.31s", "connection": "Online_store"}]}]}, "messages": {"count": 4, "messages": [{"message": "[22:11:19] LOG.warning: date_default_timezone_set(): Passing null to parameter #1 ($timezoneId) of type string is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php on line 613", "message_html": null, "is_string": false, "label": "warning", "time": **********.329805, "xdebug_link": null, "collector": "log"}, {"message": "[22:11:19] LOG.warning: class_exists(): Passing null to parameter #1 ($class) of type string is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Generators\\Sequencer.php on line 97", "message_html": null, "is_string": false, "label": "warning", "time": **********.642738, "xdebug_link": null, "collector": "log"}, {"message": "[22:11:25] LOG.error: Error in Sending EmailExpected response code \"250\" but got code \"530\", with message \"530 5.7.1 Authentication required\".", "message_html": null, "is_string": false, "label": "error", "time": **********.947687, "xdebug_link": null, "collector": "log"}, {"message": "[22:11:28] LOG.error: Error in Sending EmailExpected response code \"250\" but got code \"530\", with message \"530 5.7.1 Authentication required\".", "message_html": null, "is_string": false, "label": "error", "time": **********.918415, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754930478.333052, "end": **********.10437, "duration": 10.771318197250366, "duration_str": "10.77s", "measures": [{"label": "Booting", "start": 1754930478.333052, "relative_start": 0, "end": **********.001351, "relative_end": **********.001351, "duration": 0.****************, "duration_str": "668ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.00137, "relative_start": 0.****************, "end": **********.104374, "relative_end": 3.814697265625e-06, "duration": 10.***************, "duration_str": "10.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.033886, "relative_start": 0.****************, "end": **********.040445, "relative_end": **********.040445, "duration": 0.006559133529663086, "duration_str": "6.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::emails.orders.created", "start": **********.972054, "relative_start": 1.****************, "end": **********.972054, "relative_end": **********.972054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::emails.layout", "start": **********.724144, "relative_start": 3.**************, "end": **********.724144, "relative_end": **********.724144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "mail: New Order Confirmation", "start": **********.070436, "relative_start": 3.***************, "end": **********.104397, "relative_end": 2.6941299438476562e-05, "duration": 7.033961057662964, "duration_str": "7.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "mail", "group": null}, {"label": "View: shop::emails.orders.created", "start": **********.241304, "relative_start": 7.908252000808716, "end": **********.241304, "relative_end": **********.241304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::emails.layout", "start": 1754930487.696829, "relative_start": 9.363777160644531, "end": 1754930487.696829, "relative_end": 1754930487.696829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "mail: New Order Confirmation", "start": 1754930487.976216, "relative_start": 9.643164157867432, "end": **********.104404, "relative_end": 3.3855438232421875e-05, "duration": 1.128187894821167, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "mail", "group": null}, {"label": "Preparing Response", "start": **********.036159, "relative_start": 10.703107118606567, "end": **********.064666, "relative_end": **********.064666, "duration": 0.028506994247436523, "duration_str": "28.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 46424656, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 4, "nb_templates": 4, "templates": [{"name": "admin::emails.orders.created", "param_count": null, "params": [], "start": **********.971878, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/emails/orders/created.blade.phpadmin::emails.orders.created", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Femails%2Forders%2Fcreated.blade.php&line=1", "ajax": false, "filename": "created.blade.php", "line": "?"}}, {"name": "admin::emails.layout", "param_count": null, "params": [], "start": **********.723899, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/emails/layout.blade.phpadmin::emails.layout", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Femails%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}, {"name": "shop::emails.orders.created", "param_count": null, "params": [], "start": **********.241133, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/emails/orders/created.blade.phpshop::emails.orders.created", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Femails%2Forders%2Fcreated.blade.php&line=1", "ajax": false, "filename": "created.blade.php", "line": "?"}}, {"name": "shop::emails.layout", "param_count": null, "params": [], "start": 1754930487.696682, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src/resources/views/emails/layout.blade.phpshop::emails.layout", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Femails%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 129, "nb_statements": 127, "nb_visible_statements": 129, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.34113999999999994, "accumulated_duration_str": "341ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 27 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "type": "query", "params": [], "bindings": ["localhost", "http://localhost", "https://localhost"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.0908432, "duration": 0.023440000000000003, "duration_str": "23.44ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 6.871}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.1333928, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 6.871, "width_percent": 0.378}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.141608, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 7.249, "width_percent": 0.49}, {"sql": "select * from `currencies` where `code` = 'INR'", "type": "query", "params": [], "bindings": ["INR"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 296}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 295}], "start": **********.155692, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "Online_store", "explain": null, "start_percent": 7.739, "width_percent": 0.337}, {"sql": "select * from `cart` where `cart`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 79}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}], "start": **********.197165, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "Online_store", "explain": null, "start_percent": 8.076, "width_percent": 0.413}, {"sql": "select * from `cart_items` where `cart_items`.`cart_id` = 1 and `cart_items`.`cart_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 801}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 738}, {"index": 22, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 723}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 149}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2052128, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "Cart.php:801", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 801}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FCart.php&line=801", "ajax": false, "filename": "Cart.php", "line": "801"}, "connection": "Online_store", "explain": null, "start_percent": 8.489, "width_percent": 0.478}, {"sql": "select * from `products` where `products`.`id` = 11 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, {"index": 22, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 815}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 802}, {"index": 24, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 738}, {"index": 25, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 723}], "start": **********.225184, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "CartItem.php:45", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/CartItem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Models\\CartItem.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartItem.php&line=45", "ajax": false, "filename": "CartItem.php", "line": "45"}, "connection": "Online_store", "explain": null, "start_percent": 8.967, "width_percent": 0.375}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 497}], "start": **********.235782, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 9.342, "width_percent": 0.325}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 213}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}], "start": **********.241509, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "Online_store", "explain": null, "start_percent": 9.668, "width_percent": 0.668}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 11 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 152}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 497}], "start": **********.2530901, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 10.336, "width_percent": 0.457}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 11 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 700}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 507}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 156}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 497}], "start": **********.261619, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 10.793, "width_percent": 0.806}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.minimum_order.enable'", "type": "query", "params": [], "bindings": ["sales.order_settings.minimum_order.enable"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.272907, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 11.599, "width_percent": 0.343}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 744}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.2891011, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "Online_store", "explain": null, "start_percent": 11.942, "width_percent": 0.443}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 11 and `product_customer_group_prices`.`product_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomerGroupPriceRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomerGroupPriceRepository.php", "line": 62}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 165}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 109}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 671}], "start": **********.30147, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 12.385, "width_percent": 0.346}, {"sql": "select * from `catalog_rule_product_prices` where `catalog_rule_product_prices`.`product_id` = 11 and `catalog_rule_product_prices`.`product_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 214}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 111}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 671}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 292}], "start": **********.311114, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 12.731, "width_percent": 0.405}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.calculation.product_prices'", "type": "query", "params": [], "bindings": ["sales.taxes.calculation.product_prices"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.334083, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 13.135, "width_percent": 0.363}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 455}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 477}], "start": **********.345642, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 13.499, "width_percent": 0.361}, {"sql": "select count(*) as aggregate from `cart_rules` left join `cart_rule_customer_groups` on `cart_rules`.`id` = `cart_rule_customer_groups`.`cart_rule_id` left join `cart_rule_channels` on `cart_rules`.`id` = `cart_rule_channels`.`cart_rule_id` where `cart_rule_customer_groups`.`customer_group_id` = 1 and `cart_rule_channels`.`channel_id` = 1 and (`cart_rules`.`starts_from` <= '2025-08-11 22:08:19' or `cart_rules`.`starts_from` is null) and (`cart_rules`.`ends_till` >= '2025-08-11 22:08:19' or `cart_rules`.`ends_till` is null) and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, "2025-08-11 22:08:19", "2025-08-11 22:08:19", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 566}, {"index": 17, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 62}, {"index": 18, "namespace": null, "name": "packages/Webkul/CartRule/src/Listeners/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\CartRule\\src\\Listeners\\Cart.php", "line": 25}, {"index": 23, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 834}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 156}], "start": **********.367707, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "CartRule.php:566", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/CartRule/src/Helpers/CartRule.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\CartRule\\src\\Helpers\\CartRule.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCartRule%2Fsrc%2FHelpers%2FCartRule.php&line=566", "ajax": false, "filename": "CartRule.php", "line": "566"}, "connection": "Online_store", "explain": null, "start_percent": 13.859, "width_percent": 0.363}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.categories.product'", "type": "query", "params": [], "bindings": ["sales.taxes.categories.product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.375241, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 14.223, "width_percent": 0.302}, {"sql": "select * from `cart_shipping_rates` where `cart_shipping_rates`.`cart_id` = 1 and `cart_shipping_rates`.`cart_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 106}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 115}, {"index": 27, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 1104}, {"index": 28, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 838}, {"index": 30, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 156}], "start": **********.383861, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Cart.php:106", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCart.php&line=106", "ajax": false, "filename": "Cart.php", "line": "106"}, "connection": "Online_store", "explain": null, "start_percent": 14.525, "width_percent": 0.346}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.categories.shipping'", "type": "query", "params": [], "bindings": ["sales.taxes.categories.shipping"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.3977711, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 14.871, "width_percent": 0.287}, {"sql": "select * from `cart` where `cart`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 92}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 840}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 156}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.404651, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "Online_store", "explain": null, "start_percent": 15.158, "width_percent": 0.366}, {"sql": "select * from `cart_items` where `cart_items`.`cart_id` = 1 and `cart_items`.`cart_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 855}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 156}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.4110608, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Cart.php:855", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 855}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FCart.php&line=855", "ajax": false, "filename": "Cart.php", "line": "855"}, "connection": "Online_store", "explain": null, "start_percent": 15.524, "width_percent": 0.384}, {"sql": "select * from `cart_shipping_rates` where `cart_shipping_rates`.`cart_id` = 1 and `cart_shipping_rates`.`cart_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 106}, {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 115}, {"index": 27, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 878}, {"index": 29, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 156}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.417686, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Cart.php:106", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCart.php&line=106", "ajax": false, "filename": "Cart.php", "line": "106"}, "connection": "Online_store", "explain": null, "start_percent": 15.908, "width_percent": 0.325}, {"sql": "update `cart` set `items_qty` = 2, `grand_total` = 34, `base_grand_total` = 34, `sub_total` = 34, `base_sub_total` = 34, `tax_total` = 0, `base_tax_total` = 0, `discount_amount` = 0, `base_discount_amount` = 0, `shipping_amount` = 0, `base_shipping_amount` = 0, `sub_total_incl_tax` = 34, `base_sub_total_incl_tax` = 34, `cart`.`updated_at` = '2025-08-11 22:11:19' where `id` = 1", "type": "query", "params": [], "bindings": [2, 34, 34, 34, 34, 0, 0, 0, 0, 0, 0, 34, 34, "2025-08-11 22:11:19", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 910}, {"index": 16, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 156}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.423536, "duration": 0.00492, "duration_str": "4.92ms", "memory": 0, "memory_str": null, "filename": "Cart.php:910", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Checkout/src/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Cart.php", "line": 910}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FCart.php&line=910", "ajax": false, "filename": "Cart.php", "line": "910"}, "connection": "Online_store", "explain": null, "start_percent": 16.234, "width_percent": 1.442}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.minimum_order.minimum_order_amount' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["sales.order_settings.minimum_order.minimum_order_amount", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.437602, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 17.676, "width_percent": 0.299}, {"sql": "select * from `products` where `products`.`id` = 11 and `products`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 133}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 218}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 159}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.458333, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Cart.php:133", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Checkout/src/Models/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Checkout\\src\\Models\\Cart.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCart.php&line=133", "ajax": false, "filename": "Cart.php", "line": "133"}, "connection": "Online_store", "explain": null, "start_percent": 17.975, "width_percent": 0.317}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 1 and `addresses`.`cart_id` is not null and `address_type` = 'cart_shipping' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "type": "query", "params": [], "bindings": [1, "cart_shipping", "cart_billing", "cart_shipping"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 218}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 159}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.466803, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "OnepageController.php:218", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 218}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FOnepageController.php&line=218", "ajax": false, "filename": "OnepageController.php", "line": "218"}, "connection": "Online_store", "explain": null, "start_percent": 18.292, "width_percent": 0.355}, {"sql": "select * from `addresses` where `addresses`.`cart_id` = 1 and `addresses`.`cart_id` is not null and `address_type` = 'cart_billing' and `address_type` in ('cart_billing', 'cart_shipping') limit 1", "type": "query", "params": [], "bindings": [1, "cart_billing", "cart_billing", "cart_shipping"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 222}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 159}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.472709, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "OnepageController.php:222", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FOnepageController.php&line=222", "ajax": false, "filename": "OnepageController.php", "line": "222"}, "connection": "Online_store", "explain": null, "start_percent": 18.646, "width_percent": 0.331}, {"sql": "select * from `cart_payment` where `cart_payment`.`cart_id` = 1 and `cart_payment`.`cart_id` is not null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 233}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 159}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.478918, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "OnepageController.php:233", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FOnepageController.php&line=233", "ajax": false, "filename": "OnepageController.php", "line": "233"}, "connection": "Online_store", "explain": null, "start_percent": 18.978, "width_percent": 0.311}, {"sql": "select * from `channels` where `channels`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "packages/Webkul/Sales/src/Transformers/OrderResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Transformers\\OrderResource.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 175}], "start": **********.490179, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "Online_store", "explain": null, "start_percent": 19.288, "width_percent": 0.284}, {"sql": "select * from `locales`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 218}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 217}], "start": **********.4987671, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "Online_store", "explain": null, "start_percent": 19.573, "width_percent": 0.293}, {"sql": "select * from `channel_translations` where `channel_translations`.`channel_id` = 1 and `channel_translations`.`channel_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Sales/src/Transformers/OrderResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Transformers\\OrderResource.php", "line": 50}], "start": **********.509969, "duration": 0.0081, "duration_str": "8.1ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 19.866, "width_percent": 2.374}, {"sql": "select * from `locales` where `code` = 'en'", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 296}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 295}], "start": **********.542584, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "Online_store", "explain": null, "start_percent": 22.24, "width_percent": 0.519}, {"sql": "select * from `cart_items` where `cart_items`.`parent_id` = 1 and `cart_items`.`parent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Transformers/OrderItemResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Transformers\\OrderItemResource.php", "line": 49}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.5521939, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "Online_store", "explain": null, "start_percent": 22.759, "width_percent": 0.361}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 46}, {"index": 10, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 11, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.563556, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:46", "source": {"index": 9, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderRepository.php&line=46", "ajax": false, "filename": "OrderRepository.php", "line": "46"}, "connection": "Online_store", "explain": null, "start_percent": 23.12, "width_percent": 0}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.order_number.order_number_prefix' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["sales.order_settings.order_number.order_number_prefix", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.566597, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 23.12, "width_percent": 0.317}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.order_number.order_number_length' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["sales.order_settings.order_number.order_number_length", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.5845828, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 23.436, "width_percent": 0.276}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.order_number.order_number_suffix' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["sales.order_settings.order_number.order_number_suffix", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.606931, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 23.712, "width_percent": 0.278}, {"sql": "select * from `core_config` where `code` = 'sales.order_settings.order_number.order_number_generator' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["sales.order_settings.order_number.order_number_generator", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.6251922, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 23.99, "width_percent": 0.284}, {"sql": "select * from `orders` order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Sales/src/Generators/OrderSequencer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Generators\\OrderSequencer.php", "line": 45}, {"index": 17, "namespace": null, "name": "packages/Webkul/Sales/src/Generators/OrderSequencer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Generators\\OrderSequencer.php", "line": 35}, {"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Generators/OrderSequencer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Generators\\OrderSequencer.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}], "start": **********.634244, "duration": 0.00487, "duration_str": "4.87ms", "memory": 0, "memory_str": null, "filename": "OrderSequencer.php:45", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Sales/src/Generators/OrderSequencer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Generators\\OrderSequencer.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FGenerators%2FOrderSequencer.php&line=45", "ajax": false, "filename": "OrderSequencer.php", "line": "45"}, "connection": "Online_store", "explain": null, "start_percent": 24.274, "width_percent": 1.428}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'Online_store' and table_name = 'orders' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 53}, {"index": 22, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.648637, "duration": 0.02128, "duration_str": "21.28ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:53", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderRepository.php&line=53", "ajax": false, "filename": "OrderRepository.php", "line": "53"}, "connection": "Online_store", "explain": null, "start_percent": 25.702, "width_percent": 6.238}, {"sql": "insert into `orders` (`cart_id`, `is_guest`, `customer_id`, `customer_type`, `customer_email`, `customer_first_name`, `customer_last_name`, `channel_id`, `channel_name`, `channel_type`, `total_item_count`, `total_qty_ordered`, `base_currency_code`, `channel_currency_code`, `order_currency_code`, `grand_total`, `base_grand_total`, `sub_total`, `sub_total_incl_tax`, `base_sub_total`, `base_sub_total_incl_tax`, `tax_amount`, `base_tax_amount`, `shipping_tax_amount`, `base_shipping_tax_amount`, `coupon_code`, `applied_cart_rule_ids`, `discount_amount`, `base_discount_amount`, `shipping_method`, `shipping_title`, `shipping_description`, `shipping_amount`, `base_shipping_amount`, `shipping_amount_incl_tax`, `base_shipping_amount_incl_tax`, `shipping_discount_amount`, `base_shipping_discount_amount`, `status`, `increment_id`, `updated_at`, `created_at`) values (1, 1, null, null, '<EMAIL>', 'Vignesh', 'Chanran', 1, 'Default', 'Webkul\\\\Core\\\\Models\\\\Channel', 1, 2, 'INR', 'INR', 'INR', 34, 34, 34, 34, 34, 34, 0, 0, '0.0000', '0.0000', null, null, 0, 0, 'free_free', 'Free Shipping - Free Shipping', 'Free Shipping', 0, 0, '0.0000', '0.0000', '0.0000', '0.0000', 'pending', '1', '2025-08-11 22:11:19', '2025-08-11 22:11:19')", "type": "query", "params": [], "bindings": [1, 1, null, null, "<EMAIL>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", 1, "<PERSON><PERSON><PERSON>", "Webkul\\Core\\Models\\Channel", 1, 2, "INR", "INR", "INR", 34, 34, 34, 34, 34, 34, 0, 0, "0.0000", "0.0000", null, null, 0, 0, "free_free", "Free Shipping - Free Shipping", "Free Shipping", 0, 0, "0.0000", "0.0000", "0.0000", "0.0000", "pending", "1", "2025-08-11 22:11:19", "2025-08-11 22:11:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.676178, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:53", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderRepository.php&line=53", "ajax": false, "filename": "OrderRepository.php", "line": "53"}, "connection": "Online_store", "explain": null, "start_percent": 31.94, "width_percent": 0.821}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'Online_store' and table_name = 'order_payment' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 55}, {"index": 19, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.682626, "duration": 0.00583, "duration_str": "5.83ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:55", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderRepository.php&line=55", "ajax": false, "filename": "OrderRepository.php", "line": "55"}, "connection": "Online_store", "explain": null, "start_percent": 32.761, "width_percent": 1.709}, {"sql": "insert into `order_payment` (`method`, `method_title`, `additional`, `order_id`, `updated_at`, `created_at`) values ('cashondelivery', 'Cash On Delivery', null, 1, '2025-08-11 22:11:19', '2025-08-11 22:11:19')", "type": "query", "params": [], "bindings": ["cashondelivery", "Cash On Delivery", null, 1, "2025-08-11 22:11:19", "2025-08-11 22:11:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 55}, {"index": 19, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.692495, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:55", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderRepository.php&line=55", "ajax": false, "filename": "OrderRepository.php", "line": "55"}, "connection": "Online_store", "explain": null, "start_percent": 34.47, "width_percent": 0.49}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'Online_store' and table_name = 'addresses' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 58}, {"index": 19, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.698588, "duration": 0.00653, "duration_str": "6.53ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:58", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderRepository.php&line=58", "ajax": false, "filename": "OrderRepository.php", "line": "58"}, "connection": "Online_store", "explain": null, "start_percent": 34.959, "width_percent": 1.914}, {"sql": "insert into `addresses` (`address_type`, `first_name`, `last_name`, `gender`, `company_name`, `address`, `city`, `state`, `country`, `postcode`, `email`, `phone`, `vat_id`, `order_id`, `updated_at`, `created_at`) values ('order_shipping', 'Vignesh', 'Chanran', null, 'Vignesh', 'test', 'cbe', 'TN', 'IN', '641019', '<EMAIL>', '9677601505', null, 1, '2025-08-11 22:11:19', '2025-08-11 22:11:19')", "type": "query", "params": [], "bindings": ["order_shipping", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", null, "<PERSON><PERSON><PERSON>", "test", "cbe", "TN", "IN", "641019", "<EMAIL>", "9677601505", null, 1, "2025-08-11 22:11:19", "2025-08-11 22:11:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 58}, {"index": 19, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.709578, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:58", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderRepository.php&line=58", "ajax": false, "filename": "OrderRepository.php", "line": "58"}, "connection": "Online_store", "explain": null, "start_percent": 36.873, "width_percent": 0.61}, {"sql": "insert into `addresses` (`address_type`, `first_name`, `last_name`, `gender`, `company_name`, `address`, `city`, `state`, `country`, `postcode`, `email`, `phone`, `vat_id`, `order_id`, `updated_at`, `created_at`) values ('order_billing', 'Vignesh', 'Chanran', null, 'Vignesh', 'test', 'cbe', 'TN', 'IN', '641019', '<EMAIL>', '9677601505', null, 1, '2025-08-11 22:11:19', '2025-08-11 22:11:19')", "type": "query", "params": [], "bindings": ["order_billing", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", null, "<PERSON><PERSON><PERSON>", "test", "cbe", "TN", "IN", "641019", "<EMAIL>", "9677601505", null, 1, "2025-08-11 22:11:19", "2025-08-11 22:11:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 61}, {"index": 19, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.7162302, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:61", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderRepository.php&line=61", "ajax": false, "filename": "OrderRepository.php", "line": "61"}, "connection": "Online_store", "explain": null, "start_percent": 37.483, "width_percent": 0.249}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'Online_store' and table_name = 'order_items' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 650}, {"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 66}, {"index": 19, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.721871, "duration": 0.00859, "duration_str": "8.59ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:650", "source": {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 650}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=650", "ajax": false, "filename": "BaseRepository.php", "line": "650"}, "connection": "Online_store", "explain": null, "start_percent": 37.732, "width_percent": 2.518}, {"sql": "insert into `order_items` (`product_id`, `product_type`, `sku`, `type`, `name`, `weight`, `total_weight`, `qty_ordered`, `price`, `price_incl_tax`, `base_price`, `base_price_incl_tax`, `total`, `total_incl_tax`, `base_total`, `base_total_incl_tax`, `tax_percent`, `tax_amount`, `base_tax_amount`, `tax_category_id`, `discount_percent`, `discount_amount`, `base_discount_amount`, `additional`, `order_id`, `updated_at`, `created_at`) values (11, 'Webkul\\\\Product\\\\Models\\\\Product', 'SP-008', 'simple', 'OmniHeat Men\\'s Solid Hooded Puffer Jacket-Blue-Green-L', '1.0000', '2.0000', 2, '17.0000', '17.0000', '17.0000', '17.0000', '34.0000', '34.0000', '34.0000', '34.0000', '0.0000', '0.0000', '0.0000', null, '0.0000', '0.0000', '0.0000', '{\\\"cart_id\\\":1,\\\"product_id\\\":\\\"11\\\",\\\"is_buy_now\\\":\\\"0\\\",\\\"quantity\\\":2,\\\"locale\\\":\\\"en\\\"}', 1, '2025-08-11 22:11:19', '2025-08-11 22:11:19')", "type": "query", "params": [], "bindings": [11, "Webkul\\Product\\Models\\Product", "SP-008", "simple", "OmniHeat Men's Solid Hooded Puffer Jacket-Blue-Green-L", "1.0000", "2.0000", 2, "17.0000", "17.0000", "17.0000", "17.0000", "34.0000", "34.0000", "34.0000", "34.0000", "0.0000", "0.0000", "0.0000", null, "0.0000", "0.0000", "0.0000", "{\"cart_id\":1,\"product_id\":\"11\",\"is_buy_now\":\"0\",\"quantity\":2,\"locale\":\"en\"}", 1, "2025-08-11 22:11:19", "2025-08-11 22:11:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 66}, {"index": 17, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 18, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7374809, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "Online_store", "explain": null, "start_percent": 40.25, "width_percent": 0.695}, {"sql": "select * from `products` where `products`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Models/OrderItem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Models\\OrderItem.php", "line": 60}, {"index": 22, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 85}, {"index": 23, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 74}, {"index": 24, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}], "start": **********.749408, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "OrderItem.php:60", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Models/OrderItem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Models\\OrderItem.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrderItem.php&line=60", "ajax": false, "filename": "OrderItem.php", "line": "60"}, "connection": "Online_store", "explain": null, "start_percent": 40.945, "width_percent": 0.278}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 94}, {"index": 26, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 74}], "start": **********.754176, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 41.224, "width_percent": 0.255}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 11 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 94}, {"index": 25, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 74}], "start": **********.7605522, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 41.479, "width_percent": 0.396}, {"sql": "select * from `product_inventories` where `product_inventories`.`product_id` = 11 and `product_inventories`.`product_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 104}, {"index": 22, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 74}, {"index": 23, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}], "start": **********.766342, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 41.874, "width_percent": 0.56}, {"sql": "select * from `orders` where `orders`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 106}, {"index": 22, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 74}, {"index": 23, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.772051, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "OrderItemRepository.php:106", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderItemRepository.php&line=106", "ajax": false, "filename": "OrderItemRepository.php", "line": "106"}, "connection": "Online_store", "explain": null, "start_percent": 42.434, "width_percent": 0.363}, {"sql": "select * from `product_ordered_inventories` where `product_ordered_inventories`.`product_id` = 11 and `product_ordered_inventories`.`product_id` is not null and `channel_id` = 1 limit 1", "type": "query", "params": [], "bindings": [11, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 107}, {"index": 20, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 74}, {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.777656, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "OrderItemRepository.php:107", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 107}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderItemRepository.php&line=107", "ajax": false, "filename": "OrderItemRepository.php", "line": "107"}, "connection": "Online_store", "explain": null, "start_percent": 42.798, "width_percent": 0.513}, {"sql": "select * from `channels` where `channels`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 123}, {"index": 22, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 74}, {"index": 23, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.785428, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "OrderItemRepository.php:123", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderItemRepository.php&line=123", "ajax": false, "filename": "OrderItemRepository.php", "line": "123"}, "connection": "Online_store", "explain": null, "start_percent": 43.311, "width_percent": 0.287}, {"sql": "insert into `product_ordered_inventories` (`qty`, `product_id`, `channel_id`) values (2, 11, 1)", "type": "query", "params": [], "bindings": [2, 11, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 120}, {"index": 19, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 74}, {"index": 20, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 21, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7903519, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "OrderItemRepository.php:120", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderItemRepository.php&line=120", "ajax": false, "filename": "OrderItemRepository.php", "line": "120"}, "connection": "Online_store", "explain": null, "start_percent": 43.598, "width_percent": 0.434}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 107}, {"index": 23, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderItemRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderItemRepository.php", "line": 200}, {"index": 24, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 76}, {"index": 25, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}], "start": **********.798589, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 44.032, "width_percent": 0.267}, {"sql": "select * from `core_config` where `code` = 'emails.general.notifications.emails.general.notifications.new_order_mail_to_admin'", "type": "query", "params": [], "bindings": ["emails.general.notifications.emails.general.notifications.new_order_mail_to_admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.805823, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 44.299, "width_percent": 0.243}, {"sql": "select * from `order_items` where `order_items`.`order_id` = 1 and `order_items`.`order_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Listeners/Base.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Listeners\\Base.php", "line": 22}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Listeners/Base.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Listeners\\Base.php", "line": 34}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Listeners\\Order.php", "line": 23}, {"index": 27, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 83}, {"index": 28, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}], "start": **********.820641, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Base.php:22", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Listeners/Base.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Listeners\\Base.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FListeners%2FBase.php&line=22", "ajax": false, "filename": "Base.php", "line": "22"}, "connection": "Online_store", "explain": null, "start_percent": 44.542, "width_percent": 0.443}, {"sql": "select * from `orders` where `orders`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 93}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 63}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 102}], "start": **********.864621, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:110", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=110", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "110"}, "connection": "Online_store", "explain": null, "start_percent": 44.984, "width_percent": 0.419}, {"sql": "select * from `order_items` where `parent_id` is null and `order_items`.`order_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 63}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 102}], "start": **********.871317, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:110", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=110", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "110"}, "connection": "Online_store", "explain": null, "start_percent": 45.404, "width_percent": 0.355}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.admin_name' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["emails.configure.email_settings.admin_name", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.879783, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 45.758, "width_percent": 0.378}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.admin_email' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["emails.configure.email_settings.admin_email", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.89921, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 46.136, "width_percent": 0.334}, {"sql": "select * from `addresses` where `addresses`.`order_id` = 1 and `addresses`.`order_id` is not null and `address_type` in ('order_billing', 'order_shipping')", "type": "query", "params": [], "bindings": [1, "order_billing", "order_shipping"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Sales/src/Models/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Models\\Order.php", "line": 261}, {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Models/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Models\\Order.php", "line": 270}, {"index": 27, "namespace": "view", "name": "admin::emails.orders.created", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/emails/orders/created.blade.php", "line": 25}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.508031, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "Order.php:261", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Sales/src/Models/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Models\\Order.php", "line": 261}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrder.php&line=261", "ajax": false, "filename": "Order.php", "line": "261"}, "connection": "Online_store", "explain": null, "start_percent": 46.471, "width_percent": 0.484}, {"sql": "select * from `order_payment` where `order_payment`.`order_id` = 1 and `order_payment`.`order_id` is not null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "admin::emails.orders.created", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/emails/orders/created.blade.php", "line": 87}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.516758, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "admin::emails.orders.created:87", "source": {"index": 21, "namespace": "view", "name": "admin::emails.orders.created", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/emails/orders/created.blade.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Femails%2Forders%2Fcreated.blade.php&line=87", "ajax": false, "filename": "created.blade.php", "line": "87"}, "connection": "Online_store", "explain": null, "start_percent": 46.954, "width_percent": 0.363}, {"sql": "select * from `core_config` where `code` = 'sales.payment_methods.cashondelivery.title' and `channel_code` = 'default' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["sales.payment_methods.cashondelivery.title", "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.5286489, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 47.318, "width_percent": 0.372}, {"sql": "select * from `core_config` where `code` = 'sales.payment_methods.cashondelivery.instructions' and `channel_code` = 'default' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["sales.payment_methods.cashondelivery.instructions", "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.550923, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 47.69, "width_percent": 0.349}, {"sql": "select * from `products` where `products`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Models/OrderItem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Models\\OrderItem.php", "line": 60}, {"index": 22, "namespace": "view", "name": "admin::emails.orders.created", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/emails/orders/created.blade.php", "line": 120}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.566111, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "OrderItem.php:60", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Models/OrderItem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Models\\OrderItem.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrderItem.php&line=60", "ajax": false, "filename": "OrderItem.php", "line": "60"}, "connection": "Online_store", "explain": null, "start_percent": 48.039, "width_percent": 0.314}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.sales.display_prices'", "type": "query", "params": [], "bindings": ["sales.taxes.sales.display_prices"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.581249, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 48.353, "width_percent": 0.355}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.sales.display_subtotal'", "type": "query", "params": [], "bindings": ["sales.taxes.sales.display_subtotal"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.650394, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 48.707, "width_percent": 0.358}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.sales.display_shipping_amount'", "type": "query", "params": [], "bindings": ["sales.taxes.sales.display_shipping_amount"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.695207, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 49.065, "width_percent": 0.402}, {"sql": "select * from `core_config` where `code` = 'general.design.admin_logo.logo_image'", "type": "query", "params": [], "bindings": ["general.design.admin_logo.logo_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.868391, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 49.466, "width_percent": 0.34}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.contact_name' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["emails.configure.email_settings.contact_name", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.897784, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 49.807, "width_percent": 0.34}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.contact_email' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["emails.configure.email_settings.contact_email", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.921558, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 50.147, "width_percent": 0.331}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.sender_name' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["emails.configure.email_settings.sender_name", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.9904058, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 50.478, "width_percent": 0.378}, {"sql": "select * from `core_config` where `code` = 'emails.configure.email_settings.shop_email_from' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["emails.configure.email_settings.shop_email_from", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.015922, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 50.856, "width_percent": 0.352}, {"sql": "select * from `orders` where `orders`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 93}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 282}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 254}], "start": **********.936366, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:110", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=110", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "110"}, "connection": "Online_store", "explain": null, "start_percent": 51.208, "width_percent": 0.387}, {"sql": "select * from `order_items` where `parent_id` is null and `order_items`.`order_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 282}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 254}], "start": **********.94178, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:110", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=110", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "110"}, "connection": "Online_store", "explain": null, "start_percent": 51.595, "width_percent": 0.448}, {"sql": "select * from `order_items` where `order_items`.`order_id` = 1 and `order_items`.`order_id` is not null and `parent_id` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/BookingProduct/src/Repositories/BookingRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\BookingProduct\\src\\Repositories\\BookingRepository.php", "line": 29}, {"index": 17, "namespace": null, "name": "packages/Webkul/BookingProduct/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\BookingProduct\\src\\Listeners\\Order.php", "line": 23}, {"index": 22, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 83}, {"index": 23, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 24, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}], "start": **********.9499838, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "BookingRepository.php:29", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/BookingProduct/src/Repositories/BookingRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\BookingProduct\\src\\Repositories\\BookingRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FBookingProduct%2Fsrc%2FRepositories%2FBookingRepository.php&line=29", "ajax": false, "filename": "BookingRepository.php", "line": "29"}, "connection": "Online_store", "explain": null, "start_percent": 52.043, "width_percent": 0.384}, {"sql": "select * from `order_items` where `order_items`.`order_id` = 1 and `order_items`.`order_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Order.php", "line": 17}, {"index": 25, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 83}, {"index": 26, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 27, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.961439, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "Order.php:17", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Order.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FFPC%2Fsrc%2FListeners%2FOrder.php&line=17", "ajax": false, "filename": "Order.php", "line": "17"}, "connection": "Online_store", "explain": null, "start_percent": 52.427, "width_percent": 0.932}, {"sql": "select * from `products` where `products`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Order.php", "line": 18}, {"index": 26, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 83}, {"index": 27, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 28, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.969746, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Order.php:18", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Order.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FFPC%2Fsrc%2FListeners%2FOrder.php&line=18", "ajax": false, "filename": "Order.php", "line": "18"}, "connection": "Online_store", "explain": null, "start_percent": 53.359, "width_percent": 0.249}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 82}, {"index": 23, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 61}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Order.php", "line": 22}, {"index": 29, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 83}], "start": **********.975498, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 53.608, "width_percent": 0.534}, {"sql": "select * from `product_bundle_option_products` where `product_id` = 11", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 114}, {"index": 18, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 87}, {"index": 19, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 61}], "start": **********.983012, "duration": 0.020550000000000002, "duration_str": "20.55ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 54.142, "width_percent": 6.024}, {"sql": "select * from `product_grouped_products` where `associated_product_id` = 11", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 135}, {"index": 18, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 88}, {"index": 19, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 61}], "start": **********.009573, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 60.166, "width_percent": 0.566}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 64}, {"index": 26, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Order.php", "line": 22}], "start": **********.017493, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 60.732, "width_percent": 0.255}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 11 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 64}, {"index": 25, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Order.php", "line": 22}], "start": **********.022952, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 60.987, "width_percent": 0.44}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 64}, {"index": 26, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Order.php", "line": 22}], "start": **********.030385, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 61.426, "width_percent": 0.293}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 7 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 64}, {"index": 25, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\FPC\\src\\Listeners\\Order.php", "line": 22}], "start": **********.036364, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 61.72, "width_percent": 0.443}, {"sql": "insert into `notifications` (`type`, `order_id`, `updated_at`, `created_at`) values ('order', 1, '2025-08-11 22:11:26', '2025-08-11 22:11:26')", "type": "query", "params": [], "bindings": ["order", 1, "2025-08-11 22:11:26", "2025-08-11 22:11:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Notification/src/Listeners/Order.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Notification\\src\\Listeners\\Order.php", "line": 25}, {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 83}, {"index": 22, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}], "start": **********.066432, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "Online_store", "explain": null, "start_percent": 62.162, "width_percent": 0.683}, {"sql": "select * from `order_payment` where `order_payment`.`order_id` = 1 and `order_payment`.`order_id` is not null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Payment/src/Listeners/GenerateInvoice.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Payment\\src\\Listeners\\GenerateInvoice.php", "line": 32}, {"index": 26, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 83}, {"index": 27, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 28, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0967991, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "GenerateInvoice.php:32", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Payment/src/Listeners/GenerateInvoice.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Payment\\src\\Listeners\\GenerateInvoice.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FPayment%2Fsrc%2FListeners%2FGenerateInvoice.php&line=32", "ajax": false, "filename": "GenerateInvoice.php", "line": "32"}, "connection": "Online_store", "explain": null, "start_percent": 62.845, "width_percent": 0.29}, {"sql": "select * from `core_config` where `code` = 'sales.payment_methods.cashondelivery.generate_invoice' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["sales.payment_methods.cashondelivery.generate_invoice", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.104566, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 63.135, "width_percent": 0.355}, {"sql": "select * from `products` where `id` in (11) order by FIELD(id, 11)", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreateInventoryIndex.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreateInventoryIndex.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.1204998, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "UpdateCreateInventoryIndex.php:44", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreateInventoryIndex.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreateInventoryIndex.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FJobs%2FUpdateCreateInventoryIndex.php&line=44", "ajax": false, "filename": "UpdateCreateInventoryIndex.php", "line": "44"}, "connection": "Online_store", "explain": null, "start_percent": 63.49, "width_percent": 0.328}, {"sql": "select * from `channels`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 218}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 217}], "start": **********.135048, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "Online_store", "explain": null, "start_percent": 63.818, "width_percent": 1.041}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 11 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 120}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreateInventoryIndex.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreateInventoryIndex.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.149113, "duration": 0.00596, "duration_str": "5.96ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 64.859, "width_percent": 1.747}, {"sql": "select `inventory_sources`.*, `channel_inventory_sources`.`channel_id` as `pivot_channel_id`, `channel_inventory_sources`.`inventory_source_id` as `pivot_inventory_source_id` from `inventory_sources` inner join `channel_inventory_sources` on `inventory_sources`.`id` = `channel_inventory_sources`.`inventory_source_id` where `channel_inventory_sources`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 180}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 125}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}], "start": **********.161783, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 66.606, "width_percent": 1.003}, {"sql": "select * from `product_inventories` where `product_inventories`.`product_id` = 11 and `product_inventories`.`product_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 184}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 125}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}], "start": **********.171119, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 67.609, "width_percent": 0.276}, {"sql": "select * from `product_ordered_inventories` where `product_ordered_inventories`.`product_id` = 11 and `product_ordered_inventories`.`product_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 125}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}], "start": **********.1778162, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 67.884, "width_percent": 0.287}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 138}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreateInventoryIndex.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreateInventoryIndex.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.18573, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "Online_store", "explain": null, "start_percent": 68.171, "width_percent": 0.24}, {"sql": "update `product_inventory_indices` set `qty` = 98, `product_inventory_indices`.`updated_at` = '2025-08-11 22:11:26' where `id` = 8", "type": "query", "params": [], "bindings": [98, "2025-08-11 22:11:26", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 138}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreateInventoryIndex.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreateInventoryIndex.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1937969, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "Online_store", "explain": null, "start_percent": 68.412, "width_percent": 0.22}, {"sql": "select * from `core_config` where `code` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.204635, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 68.632, "width_percent": 0.314}, {"sql": "select * from `orders` where `orders`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.213278, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 68.945, "width_percent": 0.419}, {"sql": "select * from `order_items` where `parent_id` is null and `order_items`.`order_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.215559, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 69.364, "width_percent": 0.372}, {"sql": "select * from `order_items` where `order_items`.`order_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.217693, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 69.737, "width_percent": 0.366}, {"sql": "select * from `products` where `products`.`id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.219895, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 70.103, "width_percent": 0.261}, {"sql": "select * from `products` where `products`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.221527, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 70.364, "width_percent": 0.246}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.223591, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 70.61, "width_percent": 0.457}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.226099, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 71.068, "width_percent": 0.381}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.228927, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 71.449, "width_percent": 0.264}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.230707, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 71.712, "width_percent": 0.369}, {"sql": "select * from `order_payment` where `order_payment`.`order_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.233546, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 72.082, "width_percent": 0.299}, {"sql": "select * from `addresses` where `addresses`.`order_id` = ? and `addresses`.`order_id` is not null and `address_type` in (?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1754930487.443199, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 72.381, "width_percent": 0.51}, {"sql": "select * from `products` where `products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1754930487.457222, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 72.891, "width_percent": 0.273}, {"sql": "select * from `currencies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1754930487.522625, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 73.164, "width_percent": 0.416}, {"sql": "select * from `orders` where `orders`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.874932, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 73.58, "width_percent": 0.882}, {"sql": "select * from `order_items` where `parent_id` is null and `order_items`.`order_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.879474, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 74.462, "width_percent": 0.818}, {"sql": "select * from `order_items` where `order_items`.`order_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8838089, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 75.28, "width_percent": 0.733}, {"sql": "select * from `products` where `products`.`id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.888773, "duration": 0.0028599999999999997, "duration_str": "2.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 76.013, "width_percent": 0.838}, {"sql": "select * from `products` where `products`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.892979, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 76.851, "width_percent": 0.572}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8964372, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 77.423, "width_percent": 0.595}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.900263, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 78.018, "width_percent": 0.718}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9057682, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 78.736, "width_percent": 0.654}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.909604, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 79.39, "width_percent": 0.765}, {"sql": "select * from `order_payment` where `order_payment`.`order_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.914609, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 80.155, "width_percent": 0.677}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 98}, {"index": 10, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 111}, {"index": 11, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\OnepageController.php", "line": 177}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.96189, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:98", "source": {"index": 9, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/OrderRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Sales\\src\\Repositories\\OrderRepository.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FOrderRepository.php&line=98", "ajax": false, "filename": "OrderRepository.php", "line": "98"}, "connection": "Online_store", "explain": null, "start_percent": 80.832, "width_percent": 0}, {"sql": "select * from `cart` where `cart`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.96295, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 80.832, "width_percent": 0.756}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'Online_store' and table_name = 'cart' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.966633, "duration": 0.05564, "duration_str": "55.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 81.588, "width_percent": 16.31}, {"sql": "update `cart` set `is_active` = ?, `cart`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.025114, "duration": 0.00717, "duration_str": "7.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "Online_store", "explain": null, "start_percent": 97.898, "width_percent": 2.102}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 154, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Sales\\Models\\OrderItem": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrderItem.php&line=1", "ajax": false, "filename": "OrderItem.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Sales\\Models\\Order": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Checkout\\Models\\CartShippingRate": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartShippingRate.php&line=1", "ajax": false, "filename": "CartShippingRate.php", "line": "?"}}, "Webkul\\Sales\\Models\\OrderAddress": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Webkul\\Sales\\Models\\OrderPayment": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrderPayment.php&line=1", "ajax": false, "filename": "OrderPayment.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Checkout\\Models\\Cart": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCart.php&line=1", "ajax": false, "filename": "Cart.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}, "Webkul\\Checkout\\Models\\CartItem": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartItem.php&line=1", "ajax": false, "filename": "CartItem.php", "line": "?"}}, "Webkul\\Checkout\\Models\\CartAddress": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartAddress.php&line=1", "ajax": false, "filename": "CartAddress.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventory": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventory.php&line=1", "ajax": false, "filename": "ProductInventory.php", "line": "?"}}, "Webkul\\Core\\Models\\CoreConfig": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCoreConfig.php&line=1", "ajax": false, "filename": "CoreConfig.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Checkout\\Models\\CartPayment": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCartPayment.php&line=1", "ajax": false, "filename": "CartPayment.php", "line": "?"}}, "Webkul\\Core\\Models\\ChannelTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannelTranslation.php&line=1", "ajax": false, "filename": "ChannelTranslation.php", "line": "?"}}, "Webkul\\Inventory\\Models\\InventorySource": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FInventory%2Fsrc%2FModels%2FInventorySource.php&line=1", "ajax": false, "filename": "InventorySource.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductOrderedInventory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductOrderedInventory.php&line=1", "ajax": false, "filename": "ProductOrderedInventory.php", "line": "?"}}}, "count": 258, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/api/checkout/onepage/orders", "action_name": "shop.checkout.onepage.orders.store", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\OnepageController@storeOrder", "uri": "POST api/checkout/onepage/orders", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\OnepageController@storeOrder<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FOnepageController.php&line=147\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/checkout/onepage", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FOnepageController.php&line=147\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/OnepageController.php:147-187</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "10.81s", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-562871311 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-562871311\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-505830479 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-505830479\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1038987567 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlhKaGF6R3pheXVnUmRYeU93cnVSRUE9PSIsInZhbHVlIjoicTFqalNEM2hGZTd2WmMyNVp6RVQ3eHN4UCtIeWFGNnNmVTJXTFhHUzh6eFdsM3ZmYTBaMkdHaWU5QUljUGRCV0tDNFc4WUx6UzdjM3VyZ29UUy9GZGIvZTIybEhmWmZUdVFnbHhPVW52RW82N1JBb1pUeTRRMHRvNE5paEFXWXciLCJtYWMiOiJmNzhjNTc3NTFkMWJiOGI3MzA0ODEzODc3ZTdiMzY5ZTY0MTU0YjExYTAxNTY1NmUyOTZkOTAwZmE3YjUyZjE4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">http://localhost/onlinestore/bagisto-2.3/public/checkout/onepage</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1090 characters\">bagisto_session=eyJpdiI6ImpDWlNrdnV1KzVRcHJsU1NOQ1RaRHc9PSIsInZhbHVlIjoiNit6V3FHZEdhMzRvcGk4WnFBZUlOK09WTVZxNm5NbElac2JoaE5hV1krZGFOeExFMVpLZVcyMVdJOUFkdk0rRVZZczB3bkF0R0hxRzVjdnMrUm1KU3pkNmlHeGJYT2JuNXVyblByaUg1Qlozc3BmaEFzMkZEZmx4VzBTT3lydnEiLCJtYWMiOiIyYzI4MGJhZTYyZDJkN2I1MDdjMGE4YjIxM2M1ZGJiOTFiYWY3OGYxNGU5ZGIyZTg4ODlkN2U5NTY4ZGIwYTg4IiwidGFnIjoiIn0%3D; dark_mode=0; XSRF-TOKEN=eyJpdiI6IlhKaGF6R3pheXVnUmRYeU93cnVSRUE9PSIsInZhbHVlIjoicTFqalNEM2hGZTd2WmMyNVp6RVQ3eHN4UCtIeWFGNnNmVTJXTFhHUzh6eFdsM3ZmYTBaMkdHaWU5QUljUGRCV0tDNFc4WUx6UzdjM3VyZ29UUy9GZGIvZTIybEhmWmZUdVFnbHhPVW52RW82N1JBb1pUeTRRMHRvNE5paEFXWXciLCJtYWMiOiJmNzhjNTc3NTFkMWJiOGI3MzA0ODEzODc3ZTdiMzY5ZTY0MTU0YjExYTAxNTY1NmUyOTZkOTAwZmE3YjUyZjE4IiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6IklTMWczMjYvbUFlOHViZGE4OTNqQkE9PSIsInZhbHVlIjoiZk5Uc0ZycXZIbHEyQWorYitpQzBpaEY2enNXc0R6NnZCOVFRb0lUbEJORmd6UUR3S1E2WTJyQUU4YTFXZ25zZUJpWi9VazlnVllPbG9RWGFFbWt6ejZuOTEwOWhuc0RzLzZiUnJTUXBxMkhNNmswUFVad09NZUxsblcyN1YwU1QiLCJtYWMiOiIzYWU5MWQxY2IxM2IyZTg4MTA5MWE0MzU4MjJhN2NhNDQwNThiZDc3MTUzMmIzYWZmZmQ4YzFlN2MwMWQ3M2YwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038987567\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1250559353 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bagisto_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TMRz3GXEM8sAcFV1fikDiVtK46lGff3YWRNL67O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250559353\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-22282475 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 16:41:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22282475\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2103937849 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://localhost/onlinestore/bagisto-2.3/public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">order_id</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103937849\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/api/checkout/onepage/orders", "action_name": "shop.checkout.onepage.orders.store", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\OnepageController@storeOrder"}, "badge": null}}