{"__meta": {"id": "01K2D0KXSPXJQTD7BTJSXA9TTD", "datetime": "2025-08-11 22:20:01", "utime": **********.145913, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/admin/reporting/sales/stats?type=purchase-funnel", "ip": "::1"}, "modules": {"count": 4, "modules": [{"name": "Webkul\\Checkout", "models": ["Webkul\\Checkout\\Models\\Cart (1)"], "views": [], "queries": [{"sql": "select * from `cart` where `cart`.`channel_id` in (1) and `created_at` between '2025-07-12 00:00:00' and '2025-08-11 22:20:00' group by CONCAT(customer_email, \"-\", customer_id)", "duration": 2.8, "duration_str": "2.8s", "connection": "Online_store"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Visit (2)"], "views": [], "queries": [{"sql": "select * from `channels`", "duration": 4.03, "duration_str": "4.03s", "connection": "Online_store"}, {"sql": "select * from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-07-12 00:00:00' and '2025-08-11 22:20:00' group by CONCAT(ip, \"-\", visitor_id)", "duration": 25.81, "duration_str": "25.81s", "connection": "Online_store"}, {"sql": "select * from `visits` where `visitable_type` = 'Webkul\\Product\\Models\\Product' and `channel_id` in (1) and `created_at` between '2025-07-12 00:00:00' and '2025-08-11 22:20:00' group by CONCAT(ip, \"-\", visitor_id, \"-\", visitable_type)", "duration": 29.91, "duration_str": "29.91s", "connection": "Online_store"}]}, {"name": "Webkul\\Sales", "models": ["Webkul\\Sales\\Models\\Order (1)"], "views": [], "queries": [{"sql": "select * from `orders` where `channel_id` in (1) and `created_at` between '2025-07-12 00:00:00' and '2025-08-11 22:20:00' group by CONCAT(customer_email, \"-\", customer_id)", "duration": 2.12, "duration_str": "2.12s", "connection": "Online_store"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.98, "duration_str": "980ms", "connection": "Online_store"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 1.07, "duration_str": "1.07s", "connection": "Online_store"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754930998.823634, "end": **********.227852, "duration": 2.4042181968688965, "duration_str": "2.4s", "measures": [{"label": "Booting", "start": 1754930998.823634, "relative_start": 0, "end": **********.746347, "relative_end": **********.746347, "duration": 1.***************, "duration_str": "1.92s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.746373, "relative_start": 1.***************, "end": **********.227856, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "481ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.808578, "relative_start": 1.****************, "end": **********.818305, "relative_end": **********.818305, "duration": 0.009727001190185547, "duration_str": "9.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.138063, "relative_start": 2.****************, "end": **********.139, "relative_end": **********.139, "duration": 0.0009369850158691406, "duration_str": "937μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06672, "accumulated_duration_str": "66.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 218}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 217}], "start": **********.8815181, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 6.04}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.015818, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "Online_store", "explain": null, "start_percent": 6.04, "width_percent": 1.469}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 109}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.028411, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "Online_store", "explain": null, "start_percent": 7.509, "width_percent": 1.604}, {"sql": "select * from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-07-12 00:00:00' and '2025-08-11 22:20:00' group by CONCAT(ip, \"-\", visitor_id)", "type": "query", "params": [], "bindings": [1, "2025-07-12 00:00:00", "2025-08-11 22:20:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 105}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting.php", "line": 161}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Reporting/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Reporting\\Controller.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.036695, "duration": 0.02581, "duration_str": "25.81ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:105", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FVisitor.php&line=105", "ajax": false, "filename": "Visitor.php", "line": "105"}, "connection": "Online_store", "explain": null, "start_percent": 9.113, "width_percent": 38.684}, {"sql": "select * from `visits` where `visitable_type` = 'Webkul\\\\Product\\\\Models\\\\Product' and `channel_id` in (1) and `created_at` between '2025-07-12 00:00:00' and '2025-08-11 22:20:00' group by CONCAT(ip, \"-\", visitor_id, \"-\", visitable_type)", "type": "query", "params": [], "bindings": ["Webkul\\Product\\Models\\Product", 1, "2025-07-12 00:00:00", "2025-08-11 22:20:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 95}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting.php", "line": 166}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Reporting/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Reporting\\Controller.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.0700178, "duration": 0.02991, "duration_str": "29.91ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:95", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FVisitor.php&line=95", "ajax": false, "filename": "Visitor.php", "line": "95"}, "connection": "Online_store", "explain": null, "start_percent": 47.797, "width_percent": 44.829}, {"sql": "select * from `cart` where `cart`.`channel_id` in (1) and `created_at` between '2025-07-12 00:00:00' and '2025-08-11 22:20:00' group by CONCAT(customer_email, \"-\", customer_id)", "type": "query", "params": [], "bindings": [1, "2025-07-12 00:00:00", "2025-08-11 22:20:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Cart.php", "line": 207}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting.php", "line": 171}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Reporting/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Reporting\\Controller.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.107147, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "Cart.php:207", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Cart.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Cart.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FCart.php&line=207", "ajax": false, "filename": "Cart.php", "line": "207"}, "connection": "Online_store", "explain": null, "start_percent": 92.626, "width_percent": 4.197}, {"sql": "select * from `orders` where `channel_id` in (1) and `created_at` between '2025-07-12 00:00:00' and '2025-08-11 22:20:00' group by CONCAT(customer_email, \"-\", customer_id)", "type": "query", "params": [], "bindings": [1, "2025-07-12 00:00:00", "2025-08-11 22:20:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 595}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting.php", "line": 176}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Reporting/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Reporting\\Controller.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.116858, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "Sale.php:595", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 595}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=595", "ajax": false, "filename": "Sale.php", "line": "595"}, "connection": "Online_store", "explain": null, "start_percent": 96.823, "width_percent": 3.177}]}, "models": {"data": {"Webkul\\Core\\Models\\Visit": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Checkout\\Models\\Cart": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCheckout%2Fsrc%2FModels%2FCart.php&line=1", "ajax": false, "filename": "Cart.php", "line": "?"}}, "Webkul\\Sales\\Models\\Order": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/reporting/sales/stats?type=purchase-funnel", "action_name": "admin.reporting.sales.stats", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Reporting\\SaleController@stats", "uri": "GET admin/reporting/sales/stats", "controller": "Webkul\\Admin\\Http\\Controllers\\Reporting\\SaleController@stats<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FReporting%2FController.php&line=31\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/reporting/sales", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FReporting%2FController.php&line=31\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Reporting/Controller.php:31-39</a>", "middleware": "web, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "2.41s", "peak_memory": "40MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1891377208 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"15 characters\">purchase-funnel</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891377208\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1993613973 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1993613973\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-107806063 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IloxM2dMaWNNeERKNWs5a1hJYVVhdVE9PSIsInZhbHVlIjoiWVhlQzVudFEzWGpKUG9UV2VUWnVvUm5pWU53YVhpZngxanVRSVpxYVZvNy9pZ1NkUGN5MWl2Rm16SDdZZy9ld3F0bGZGcGlYaXZaRVFBRHJlYTh1NjhhT1dVQ3hNUzlTRUlDanZVbGdYMHcrbmordktUbG1JK2Y4V28xR1N4NW8iLCJtYWMiOiI5MWY3Y2E0OWE2OGM5YTcyZGFiMWNjMTRhMTE1ZjczMjc4NTY4Yjg0NDJjYTViN2QwMTViYmE4ZDg1NDE5YjI0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/reporting/sales</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1450 characters\">bagisto_session=eyJpdiI6ImpDWlNrdnV1KzVRcHJsU1NOQ1RaRHc9PSIsInZhbHVlIjoiNit6V3FHZEdhMzRvcGk4WnFBZUlOK09WTVZxNm5NbElac2JoaE5hV1krZGFOeExFMVpLZVcyMVdJOUFkdk0rRVZZczB3bkF0R0hxRzVjdnMrUm1KU3pkNmlHeGJYT2JuNXVyblByaUg1Qlozc3BmaEFzMkZEZmx4VzBTT3lydnEiLCJtYWMiOiIyYzI4MGJhZTYyZDJkN2I1MDdjMGE4YjIxM2M1ZGJiOTFiYWY3OGYxNGU5ZGIyZTg4ODlkN2U5NTY4ZGIwYTg4IiwidGFnIjoiIn0%3D; dark_mode=0; laravel_session=eyJpdiI6IkJ1bkdwR1A3SG9VNlBmSlgxR3orR0E9PSIsInZhbHVlIjoiY0lhNEVuSnY5YkZDYkRyemFpZGh0MkdzZk1JNVdxQlpSWHF0bEthRGh2aU5QMDhJZWgwZnNCS1ZZOXYrbm9tWTI5VVdsK1hXcGwzUVlxRk5ENSs5TXVhZG0xcFJMVGxIUC9IcjNjM1p4TFQ4dUJ5MSsyS0g0R2hsanlKS2ZXdFIiLCJtYWMiOiIxYWUzMGU3MWJlY2E1ZjZhZTU4OWRiOWJiZDM1MWMwNGYwNjNiMDc2NzAwZTI5MzYwOWNkODI0Zjk3YzRkN2NlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IloxM2dMaWNNeERKNWs5a1hJYVVhdVE9PSIsInZhbHVlIjoiWVhlQzVudFEzWGpKUG9UV2VUWnVvUm5pWU53YVhpZngxanVRSVpxYVZvNy9pZ1NkUGN5MWl2Rm16SDdZZy9ld3F0bGZGcGlYaXZaRVFBRHJlYTh1NjhhT1dVQ3hNUzlTRUlDanZVbGdYMHcrbmordktUbG1JK2Y4V28xR1N4NW8iLCJtYWMiOiI5MWY3Y2E0OWE2OGM5YTcyZGFiMWNjMTRhMTE1ZjczMjc4NTY4Yjg0NDJjYTViN2QwMTViYmE4ZDg1NDE5YjI0IiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6InkzSWs0SUN3Z3Z4ak9weGtpQWNXNVE9PSIsInZhbHVlIjoibzliL3N2NmRyaXZUbzlndGFzSW1kSmJsb28wMEoraFFBN0taTlRJaVNlU0sweEI3b1BJY0ppVm9VbjVudnlyRWZDeUJDeHhuRmNYd2NCNkFxaFBQbThkVG8xeHlYRXNhVEN2dlhjS3NBcHBHWXNTcldGSHlUMnorbTdxd1NvdzUiLCJtYWMiOiIzMDQzOTZiNGY0N2YwNzQ1ODU5MmY5NGVjYWZhYmQyNjQ0YjZhMTI2ZDI0MzQ1ODViNDgxNjM1YjljZTg1ZDM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107806063\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1198702274 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bagisto_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QtNrPDgdBnix8dQJIc98sIh6jLcY9Lhsvc5bz0Lz</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TMRz3GXEM8sAcFV1fikDiVtK46lGff3YWRNL67O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198702274\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1233685697 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 16:50:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233685697\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-608407229 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"69 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/reporting/sales</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-608407229\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/reporting/sales/stats?type=purchase-funnel", "action_name": "admin.reporting.sales.stats", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Reporting\\SaleController@stats"}, "badge": null}}