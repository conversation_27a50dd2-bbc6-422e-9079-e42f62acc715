<div class="grid grid-cols-[1fr_auto] gap-8 max-lg:grid-cols-[1fr] max-md:gap-5">
    <div class="max-sm:hidden">
        <!-- Billing Address Shimmer -->
        <div class="mb-7 mt-8">
            <!-- Header -->
            <div class="flex items-center justify-between">
                <h2 class="shimmer h-8 w-[180px]"></h2>
                
                <span class="shimmer h-6 w-6"></span>
            </div>

            <?php if (isset($component)) { $__componentOriginal80c21b16724bf8aa3af805a15476ab8f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal80c21b16724bf8aa3af805a15476ab8f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.shimmer.checkout.onepage.address','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::shimmer.checkout.onepage.address'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal80c21b16724bf8aa3af805a15476ab8f)): ?>
<?php $attributes = $__attributesOriginal80c21b16724bf8aa3af805a15476ab8f; ?>
<?php unset($__attributesOriginal80c21b16724bf8aa3af805a15476ab8f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal80c21b16724bf8aa3af805a15476ab8f)): ?>
<?php $component = $__componentOriginal80c21b16724bf8aa3af805a15476ab8f; ?>
<?php unset($__componentOriginal80c21b16724bf8aa3af805a15476ab8f); ?>
<?php endif; ?>
        </div>

        <!-- Shipping Method Shimmer -->
        <?php if (isset($component)) { $__componentOriginald87fc392b8affb87fbc8104a00c8e1ff = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald87fc392b8affb87fbc8104a00c8e1ff = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.shimmer.checkout.onepage.shipping-method','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::shimmer.checkout.onepage.shipping-method'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald87fc392b8affb87fbc8104a00c8e1ff)): ?>
<?php $attributes = $__attributesOriginald87fc392b8affb87fbc8104a00c8e1ff; ?>
<?php unset($__attributesOriginald87fc392b8affb87fbc8104a00c8e1ff); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald87fc392b8affb87fbc8104a00c8e1ff)): ?>
<?php $component = $__componentOriginald87fc392b8affb87fbc8104a00c8e1ff; ?>
<?php unset($__componentOriginald87fc392b8affb87fbc8104a00c8e1ff); ?>
<?php endif; ?>

        <!-- Payment Method Shimmer -->
        <?php if (isset($component)) { $__componentOriginal27d1292a99e0d106fe10d79161c30d5f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal27d1292a99e0d106fe10d79161c30d5f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.shimmer.checkout.onepage.payment-method','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::shimmer.checkout.onepage.payment-method'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal27d1292a99e0d106fe10d79161c30d5f)): ?>
<?php $attributes = $__attributesOriginal27d1292a99e0d106fe10d79161c30d5f; ?>
<?php unset($__attributesOriginal27d1292a99e0d106fe10d79161c30d5f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal27d1292a99e0d106fe10d79161c30d5f)): ?>
<?php $component = $__componentOriginal27d1292a99e0d106fe10d79161c30d5f; ?>
<?php unset($__componentOriginal27d1292a99e0d106fe10d79161c30d5f); ?>
<?php endif; ?>
    </div>

    <?php if (isset($component)) { $__componentOriginalac78205f9a2356133ec33ad7532440fd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalac78205f9a2356133ec33ad7532440fd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.shimmer.checkout.onepage.cart-summary','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::shimmer.checkout.onepage.cart-summary'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalac78205f9a2356133ec33ad7532440fd)): ?>
<?php $attributes = $__attributesOriginalac78205f9a2356133ec33ad7532440fd; ?>
<?php unset($__attributesOriginalac78205f9a2356133ec33ad7532440fd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalac78205f9a2356133ec33ad7532440fd)): ?>
<?php $component = $__componentOriginalac78205f9a2356133ec33ad7532440fd; ?>
<?php unset($__componentOriginalac78205f9a2356133ec33ad7532440fd); ?>
<?php endif; ?>

    <!-- For Mobile View Billing Address -->
    <div class="sm:hidden">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <h2 class="shimmer h-8 w-[180px]"></h2>
            
            <span class="shimmer h-6 w-6"></span>
        </div>

        <?php if (isset($component)) { $__componentOriginal80c21b16724bf8aa3af805a15476ab8f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal80c21b16724bf8aa3af805a15476ab8f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.shimmer.checkout.onepage.address','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::shimmer.checkout.onepage.address'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal80c21b16724bf8aa3af805a15476ab8f)): ?>
<?php $attributes = $__attributesOriginal80c21b16724bf8aa3af805a15476ab8f; ?>
<?php unset($__attributesOriginal80c21b16724bf8aa3af805a15476ab8f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal80c21b16724bf8aa3af805a15476ab8f)): ?>
<?php $component = $__componentOriginal80c21b16724bf8aa3af805a15476ab8f; ?>
<?php unset($__componentOriginal80c21b16724bf8aa3af805a15476ab8f); ?>
<?php endif; ?>
    </div>
</div>
<?php /**PATH D:\xampp\htdocs\onlinestore\bagisto-2.3\packages\Webkul\Shop\src/resources/views/components/shimmer/checkout/onepage/index.blade.php ENDPATH**/ ?>