{"__meta": {"id": "01K2D0E239ZXHAWX6GA9G4J495", "datetime": "2025-08-11 22:16:48", "utime": **********.942094, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/admin/login", "ip": "::1"}, "modules": {"count": 1, "modules": [{"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)"], "views": [], "queries": [{"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 9.98, "duration_str": "9.98s", "connection": "Online_store"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.108405, "end": **********.00356, "duration": 0.8951549530029297, "duration_str": "895ms", "measures": [{"label": "Booting", "start": **********.108405, "relative_start": 0, "end": **********.746592, "relative_end": **********.746592, "duration": 0.****************, "duration_str": "638ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.746611, "relative_start": 0.****************, "end": **********.003565, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "257ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.783372, "relative_start": 0.****************, "end": **********.792231, "relative_end": **********.792231, "duration": 0.*****************, "duration_str": "8.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.927813, "relative_start": 0.****************, "end": **********.930251, "relative_end": **********.930251, "duration": 0.0024378299713134766, "duration_str": "2.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "33MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.009980000000000001, "accumulated_duration_str": "9.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/User/SessionController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\User\\SessionController.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.873316, "duration": 0.009980000000000001, "duration_str": "9.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/login", "action_name": "admin.session.create", "controller_action": "Webkul\\Admin\\Http\\Controllers\\User\\SessionController@create", "uri": "GET admin/login", "controller": "Webkul\\Admin\\Http\\Controllers\\User\\SessionController@create<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FUser%2FSessionController.php&line=14\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/login", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FUser%2FSessionController.php&line=14\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/User/SessionController.php:14-29</a>", "middleware": "web, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "894ms", "peak_memory": "34MB", "response": "Redirect to http://localhost/onlinestore/bagisto-2.3/public/admin/dashboard", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1645113697 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1645113697\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-224728783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-224728783\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1879359923 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InNZS2M2TzNzRVZrSnlPNFZvS2pLWmc9PSIsInZhbHVlIjoiSnNLUkdyMlVIaFpmMjMzL0Z4RXM1ZEZBREtpU3lRMHFRQ1JSWGFqVURKaHhQdVFPVkRqRXI1QThyc1ZZbVp6Vythb0pOOXB5dHRNOFE2TUQwcGNIbzZROW5wM1NRVEdDd1gva2pVaWM5SVIyWVJvMW1MZG5TeEZRVFNEOFB1L1UiLCJtYWMiOiI4NTkzNmNhZjhjMjdiNDYyNjFmMTFjYTZhNjIzZTVjNjM2Mjk4NzQ0MmJjMzJmZjZjYjlmNTUzMzVmYjFmYWFlIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/sales/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1450 characters\">bagisto_session=eyJpdiI6ImpDWlNrdnV1KzVRcHJsU1NOQ1RaRHc9PSIsInZhbHVlIjoiNit6V3FHZEdhMzRvcGk4WnFBZUlOK09WTVZxNm5NbElac2JoaE5hV1krZGFOeExFMVpLZVcyMVdJOUFkdk0rRVZZczB3bkF0R0hxRzVjdnMrUm1KU3pkNmlHeGJYT2JuNXVyblByaUg1Qlozc3BmaEFzMkZEZmx4VzBTT3lydnEiLCJtYWMiOiIyYzI4MGJhZTYyZDJkN2I1MDdjMGE4YjIxM2M1ZGJiOTFiYWY3OGYxNGU5ZGIyZTg4ODlkN2U5NTY4ZGIwYTg4IiwidGFnIjoiIn0%3D; dark_mode=0; onlinestore_session=eyJpdiI6InlCVmVxekZpejNIdVRqVmlvNWtabHc9PSIsInZhbHVlIjoiaGoveHExSUVxTGR0cy96WGFHUHh0Tk05MXBsZ1RRQmtIT0t2dzJtNDVKaW9DVm9sN0ZaeFRiQ2pxdGZQQ29zOWM0TW5FZVNqYzc2S1NmaC9SRXpzY1pGVmhEZUhzQ3RBRExwTEdQSFpvVEt4dEFOcDlUU0pBS1ZiVjdJQ2NLZFkiLCJtYWMiOiI0ZjA4N2U5Nzg3NDg1YzY0OTdlZmEwNzUyMTZhZmE5ZWYyZWE3MTY1Y2M0YWNhMmIyZTkxOTMxYTI1MDgyNzgxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlM4UzJYWTZBdDNJZkg0QWlwWWJvb1E9PSIsInZhbHVlIjoiSHQ1TTJKbHh3b3dqQlQvbXJoRFRYS0x2dnZCZlJDeUt5M2dqTzQ1dkJML2VtdWNXbkZ5NEVTWW1oaFdhUjQzQ3dxM2tKYVlLUURtV3ZqVXZCMWFmN00vK2lsN2JBMUFUcnBSczBoSWt0djlTam1UdG5odk10VmhmQW0yRUc4WVYiLCJtYWMiOiIxNTE3MjgyYTQ0MmVmODVlZDI1NWFmMWY5MDA4NzQ3MThhNDc5ZjkyOWI3OWI5MTBlYTJiMjRhYTJkZjE5OTZiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkJ1bkdwR1A3SG9VNlBmSlgxR3orR0E9PSIsInZhbHVlIjoiY0lhNEVuSnY5YkZDYkRyemFpZGh0MkdzZk1JNVdxQlpSWHF0bEthRGh2aU5QMDhJZWgwZnNCS1ZZOXYrbm9tWTI5VVdsK1hXcGwzUVlxRk5ENSs5TXVhZG0xcFJMVGxIUC9IcjNjM1p4TFQ4dUJ5MSsyS0g0R2hsanlKS2ZXdFIiLCJtYWMiOiIxYWUzMGU3MWJlY2E1ZjZhZTU4OWRiOWJiZDM1MWMwNGYwNjNiMDc2NzAwZTI5MzYwOWNkODI0Zjk3YzRkN2NlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1879359923\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-5526616 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bagisto_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TMRz3GXEM8sAcFV1fikDiVtK46lGff3YWRNL67O1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7TXi4wlnYpDQXjzrEcPjjxTqZCPD8qdg01Ab0Xg2</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QtNrPDgdBnix8dQJIc98sIh6jLcY9Lhsvc5bz0Lz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5526616\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-803696871 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 16:46:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803696871\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2117162092 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"66 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/sales/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117162092\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/login", "action_name": "admin.session.create", "controller_action": "Webkul\\Admin\\Http\\Controllers\\User\\SessionController@create"}, "badge": "302 Found"}}