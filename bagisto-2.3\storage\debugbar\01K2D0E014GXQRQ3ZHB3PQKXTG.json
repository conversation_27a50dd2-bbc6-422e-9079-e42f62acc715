{"__meta": {"id": "01K2D0E014GXQRQ3ZHB3PQKXTG", "datetime": "2025-08-11 22:16:46", "utime": **********.822376, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/admin/sales/orders?pagination[page]=1&pagination[per_page]=10", "ip": "::1"}, "modules": {"count": 5, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\AttributeFamily (1)", "Webkul\\Attribute\\Models\\Attribute (28)"], "views": [], "queries": [{"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.93, "duration_str": "930ms", "connection": "Online_store"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 1.89, "duration_str": "1.89s", "connection": "Online_store"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\ChannelTranslation (1)", "Webkul\\Core\\Models\\Channel (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "duration": 1.25, "duration_str": "1.25s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'sales.payment_methods.cashondelivery.title' and `channel_code` = 'default' and `locale_code` = 'en'", "duration": 1.15, "duration_str": "1.15s", "connection": "Online_store"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (1)", "Webkul\\Product\\Models\\ProductImage (2)"], "views": [], "queries": [{"sql": "select * from `products` where `products`.`id` = 11 limit 1", "duration": 0.93, "duration_str": "930ms", "connection": "Online_store"}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 11 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.86, "duration_str": "860ms", "connection": "Online_store"}]}, {"name": "Webkul\\Sales", "models": ["Webkul\\Sales\\Models\\Order (1)", "Webkul\\Sales\\Models\\OrderItem (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `orders` left join `addresses` as `order_address_shipping` on `order_address_shipping`.`order_id` = `orders`.`id` and `order_address_shipping`.`address_type` = 'order_shipping' left join `addresses` as `order_address_billing` on `order_address_billing`.`order_id` = `orders`.`id` and `order_address_billing`.`address_type` = 'order_billing' left join `order_payment` on `orders`.`id` = `order_payment`.`order_id`", "duration": 1.68, "duration_str": "1.68s", "connection": "Online_store"}, {"sql": "select distinct `orders`.`id`, `order_payment`.`method`, `orders`.`increment_id`, `orders`.`base_grand_total`, `orders`.`created_at`, `channel_name`, `channel_id`, `status`, `customer_email`, `orders`.`cart_id` as `items`, CONCAT(orders.customer_first_name, \" \", orders.customer_last_name) as full_name, CONCAT(order_address_billing.city, \", \", order_address_billing.state,\", \", order_address_billing.country) as location from `orders` left join `addresses` as `order_address_shipping` on `order_address_shipping`.`order_id` = `orders`.`id` and `order_address_shipping`.`address_type` = 'order_shipping' left join `addresses` as `order_address_billing` on `order_address_billing`.`order_id` = `orders`.`id` and `order_address_billing`.`address_type` = 'order_billing' left join `order_payment` on `orders`.`id` = `order_payment`.`order_id` order by `id` desc limit 10 offset 0", "duration": 27.49, "duration_str": "27.49s", "connection": "Online_store"}, {"sql": "select * from `orders` where `orders`.`id` = 1 limit 1", "duration": 2.29, "duration_str": "2.29s", "connection": "Online_store"}, {"sql": "select * from `order_items` where `parent_id` is null and `order_items`.`order_id` in (1)", "duration": 1.61, "duration_str": "1.61s", "connection": "Online_store"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 26.73, "duration_str": "26.73s", "connection": "Online_store"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.95, "duration_str": "950ms", "connection": "Online_store"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754930805.384746, "end": **********.847884, "duration": 1.4631378650665283, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1754930805.384746, "relative_start": 0, "end": **********.383055, "relative_end": **********.383055, "duration": 0.****************, "duration_str": "998ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.383077, "relative_start": 0.**************, "end": **********.847888, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "465ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.444176, "relative_start": 1.****************, "end": **********.450486, "relative_end": **********.450486, "duration": 0.006309986114501953, "duration_str": "6.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::sales.orders.items", "start": **********.754148, "relative_start": 1.****************, "end": **********.754148, "relative_end": **********.754148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.817157, "relative_start": 1.****************, "end": **********.817879, "relative_end": **********.817879, "duration": 0.0007219314575195312, "duration_str": "722μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "admin::sales.orders.items", "param_count": null, "params": [], "start": **********.753995, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/sales/orders/items.blade.phpadmin::sales.orders.items", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fsales%2Forders%2Fitems.blade.php&line=1", "ajax": false, "filename": "items.blade.php", "line": "?"}}]}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07053, "accumulated_duration_str": "70.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.517812, "duration": 0.02673, "duration_str": "26.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 37.899}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 109}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.5616078, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "Online_store", "explain": null, "start_percent": 37.899, "width_percent": 1.347}, {"sql": "select * from `channel_translations` where `channel_translations`.`channel_id` = 1 and `channel_translations`.`channel_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/DataGrids/Sales/OrderDataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\DataGrids\\Sales\\OrderDataGrid.php", "line": 156}], "start": **********.625812, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "Online_store", "explain": null, "start_percent": 39.246, "width_percent": 3.927}, {"sql": "select count(*) as aggregate from `orders` left join `addresses` as `order_address_shipping` on `order_address_shipping`.`order_id` = `orders`.`id` and `order_address_shipping`.`address_type` = 'order_shipping' left join `addresses` as `order_address_billing` on `order_address_billing`.`order_id` = `orders`.`id` and `order_address_billing`.`address_type` = 'order_billing' left join `order_payment` on `orders`.`id` = `order_payment`.`order_id`", "type": "query", "params": [], "bindings": ["order_shipping", "order_billing"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 513}, {"index": 17, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 561}, {"index": 18, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 737}, {"index": 19, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 419}, {"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Sales/OrderController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Sales\\OrderController.php", "line": 42}], "start": **********.653356, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "DataGrid.php:513", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 513}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FDataGrid%2Fsrc%2FDataGrid.php&line=513", "ajax": false, "filename": "DataGrid.php", "line": "513"}, "connection": "Online_store", "explain": null, "start_percent": 43.173, "width_percent": 2.382}, {"sql": "select distinct `orders`.`id`, `order_payment`.`method`, `orders`.`increment_id`, `orders`.`base_grand_total`, `orders`.`created_at`, `channel_name`, `channel_id`, `status`, `customer_email`, `orders`.`cart_id` as `items`, CONCAT(orders.customer_first_name, \" \", orders.customer_last_name) as full_name, CONCAT(order_address_billing.city, \", \", order_address_billing.state,\", \", order_address_billing.country) as location from `orders` left join `addresses` as `order_address_shipping` on `order_address_shipping`.`order_id` = `orders`.`id` and `order_address_shipping`.`address_type` = 'order_shipping' left join `addresses` as `order_address_billing` on `order_address_billing`.`order_id` = `orders`.`id` and `order_address_billing`.`address_type` = 'order_billing' left join `order_payment` on `orders`.`id` = `order_payment`.`order_id` order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["order_shipping", "order_billing"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 513}, {"index": 15, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 561}, {"index": 16, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 737}, {"index": 17, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 419}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Sales/OrderController.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Sales\\OrderController.php", "line": 42}], "start": **********.660278, "duration": 0.027489999999999997, "duration_str": "27.49ms", "memory": 0, "memory_str": null, "filename": "DataGrid.php:513", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 513}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FDataGrid%2Fsrc%2FDataGrid.php&line=513", "ajax": false, "filename": "DataGrid.php", "line": "513"}, "connection": "Online_store", "explain": null, "start_percent": 45.555, "width_percent": 38.976}, {"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "type": "query", "params": [], "bindings": ["localhost", "http://localhost", "https://localhost"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 169}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 246}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 218}], "start": **********.707592, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "Online_store", "explain": null, "start_percent": 84.531, "width_percent": 1.772}, {"sql": "select * from `core_config` where `code` = 'sales.payment_methods.cashondelivery.title' and `channel_code` = 'default' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["sales.payment_methods.cashondelivery.title", "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.717802, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 86.304, "width_percent": 1.631}, {"sql": "select * from `orders` where `orders`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/DataGrids/Sales/OrderDataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\DataGrids\\Sales\\OrderDataGrid.php", "line": 194}, {"index": 19, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 631}, {"index": 20, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 665}, {"index": 21, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 425}], "start": **********.730306, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "Online_store", "explain": null, "start_percent": 87.934, "width_percent": 3.247}, {"sql": "select * from `order_items` where `parent_id` is null and `order_items`.`order_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/DataGrids/Sales/OrderDataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src\\DataGrids\\Sales\\OrderDataGrid.php", "line": 194}, {"index": 24, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 631}, {"index": 25, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 665}, {"index": 26, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 425}], "start": **********.73857, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "Online_store", "explain": null, "start_percent": 91.181, "width_percent": 2.283}, {"sql": "select * from `products` where `products`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "admin::sales.orders.items", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/sales/orders/items.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.7566528, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "admin::sales.orders.items:9", "source": {"index": 21, "namespace": "view", "name": "admin::sales.orders.items", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/sales/orders/items.blade.php", "line": 9}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fsales%2Forders%2Fitems.blade.php&line=9", "ajax": false, "filename": "items.blade.php", "line": "9"}, "connection": "Online_store", "explain": null, "start_percent": 93.464, "width_percent": 1.319}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 11 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": "view", "name": "admin::sales.orders.items", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/sales/orders/items.blade.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.762485, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 94.782, "width_percent": 1.219}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": "view", "name": "admin::sales.orders.items", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/sales/orders/items.blade.php", "line": 12}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.772074, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "Online_store", "explain": null, "start_percent": 96.002, "width_percent": 1.319}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 213}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": "view", "name": "admin::sales.orders.items", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Admin\\src/resources/views/sales/orders/items.blade.php", "line": 12}], "start": **********.778781, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "Online_store", "explain": null, "start_percent": 97.32, "width_percent": 2.68}]}, "models": {"data": {"Webkul\\Attribute\\Models\\Attribute": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Core\\Models\\ChannelTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannelTranslation.php&line=1", "ajax": false, "filename": "ChannelTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Sales\\Models\\Order": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Webkul\\Sales\\Models\\OrderItem": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrderItem.php&line=1", "ajax": false, "filename": "OrderItem.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}}, "count": 38, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/sales/orders?pagination%5Bpage%5D=1&pagination...", "action_name": "admin.sales.orders.index", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Sales\\OrderController@index", "uri": "GET admin/sales/orders", "controller": "Webkul\\Admin\\Http\\Controllers\\Sales\\OrderController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSales%2FOrderController.php&line=39\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/sales/orders", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSales%2FOrderController.php&line=39\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Sales/OrderController.php:39-48</a>", "middleware": "web, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "1.47s", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1769137903 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pagination</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>per_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769137903\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-155450612 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-155450612\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1381204178 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImdBK2xiWEZmaFd6OCt0bTBuTm1kbWc9PSIsInZhbHVlIjoiT3ZraDNsSlA2UjY0QXhxWENTS1BybWttaHdMY1ZBaHdROEphYlRmZE15Ymh3WnA1UVNBbFE0Tzgyckh3Q0lFaWxVNjlDS2t6WEdweHVXZVNZd3pxSkJxNkdBNmpteVpySFZTKzh4V3diTVhJYmhnUkJMRUs1WFRtK0hMVXJ0NS8iLCJtYWMiOiI3YTMzYTBmZWIxZDM4YzBiZDU4ZWUzZjQwMTMxZTBiZjg5MjNjYzE3YTExZjExMjg5OTczOTZjOTMwODAwZTQzIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/sales/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1090 characters\">bagisto_session=eyJpdiI6ImpDWlNrdnV1KzVRcHJsU1NOQ1RaRHc9PSIsInZhbHVlIjoiNit6V3FHZEdhMzRvcGk4WnFBZUlOK09WTVZxNm5NbElac2JoaE5hV1krZGFOeExFMVpLZVcyMVdJOUFkdk0rRVZZczB3bkF0R0hxRzVjdnMrUm1KU3pkNmlHeGJYT2JuNXVyblByaUg1Qlozc3BmaEFzMkZEZmx4VzBTT3lydnEiLCJtYWMiOiIyYzI4MGJhZTYyZDJkN2I1MDdjMGE4YjIxM2M1ZGJiOTFiYWY3OGYxNGU5ZGIyZTg4ODlkN2U5NTY4ZGIwYTg4IiwidGFnIjoiIn0%3D; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImdBK2xiWEZmaFd6OCt0bTBuTm1kbWc9PSIsInZhbHVlIjoiT3ZraDNsSlA2UjY0QXhxWENTS1BybWttaHdMY1ZBaHdROEphYlRmZE15Ymh3WnA1UVNBbFE0Tzgyckh3Q0lFaWxVNjlDS2t6WEdweHVXZVNZd3pxSkJxNkdBNmpteVpySFZTKzh4V3diTVhJYmhnUkJMRUs1WFRtK0hMVXJ0NS8iLCJtYWMiOiI3YTMzYTBmZWIxZDM4YzBiZDU4ZWUzZjQwMTMxZTBiZjg5MjNjYzE3YTExZjExMjg5OTczOTZjOTMwODAwZTQzIiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6IlpwSTQyVVpDVDFmMHA3bDl0b3BZOVE9PSIsInZhbHVlIjoiekM3K0FFeGRrQlZSZGJ6S0drQVNGekVaZDFVd1BESmFzNHJXY05YcmowZGFnMTZ0emV1MWYrMEs2OG85VjlnUUJZMFZrWG40WE9IQjh1YUlCbjlIaU91Q25KdUlVMmRMSUYrcGdDdmNhcVN0azRrdU5JVk1aS1FRRHVRM3V5NW0iLCJtYWMiOiJmYjljZDU4MDU2NDRlMjI4ZWZhZjlhNzM1MDEzMzM0YzAyZjAzNzNjYTA3ODg5MGFlNzY2OGIxOWUzY2ZhZjc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1381204178\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1277812995 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bagisto_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TMRz3GXEM8sAcFV1fikDiVtK46lGff3YWRNL67O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277812995\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1838114300 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 16:46:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838114300\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-90590450 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jYy5f3iLbXHSUs3ZAkcKckbPwvZmOscMrokyp9PD</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"66 characters\">http://localhost/onlinestore/bagisto-2.3/public/admin/sales/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-90590450\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/admin/sales/orders?pagination%5Bpage%5D=1&pagination...", "action_name": "admin.sales.orders.index", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Sales\\OrderController@index"}, "badge": null}}