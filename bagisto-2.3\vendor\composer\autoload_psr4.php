<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'voku\\' => array($vendorDir . '/voku/portable-ascii/src/voku'),
    'setasign\\Fpdi\\' => array($vendorDir . '/setasign/fpdi/src'),
    'phpseclib3\\' => array($vendorDir . '/phpseclib/phpseclib/phpseclib'),
    'phpDocumentor\\Reflection\\' => array($vendorDir . '/phpdocumentor/reflection-common/src', $vendorDir . '/phpdocumentor/reflection-docblock/src', $vendorDir . '/phpdocumentor/type-resolver/src'),
    'enshrined\\svgSanitize\\' => array($vendorDir . '/enshrined/svg-sanitize/src'),
    'ZipStream\\' => array($vendorDir . '/maennchen/zipstream-php/src'),
    'Whoops\\' => array($vendorDir . '/filp/whoops/src/Whoops'),
    'Webmozart\\Assert\\' => array($vendorDir . '/webmozart/assert/src'),
    'Webkul\\User\\' => array($baseDir . '/packages/Webkul/User/src'),
    'Webkul\\Theme\\' => array($baseDir . '/packages/Webkul/Theme/src'),
    'Webkul\\Tax\\' => array($baseDir . '/packages/Webkul/Tax/src'),
    'Webkul\\SocialShare\\' => array($baseDir . '/packages/Webkul/SocialShare/src'),
    'Webkul\\SocialLogin\\' => array($baseDir . '/packages/Webkul/SocialLogin/src'),
    'Webkul\\Sitemap\\' => array($baseDir . '/packages/Webkul/Sitemap/src'),
    'Webkul\\Shop\\Tests\\' => array($baseDir . '/packages/Webkul/Shop/tests'),
    'Webkul\\Shop\\' => array($baseDir . '/packages/Webkul/Shop/src'),
    'Webkul\\Shipping\\' => array($baseDir . '/packages/Webkul/Shipping/src'),
    'Webkul\\Sales\\' => array($baseDir . '/packages/Webkul/Sales/src'),
    'Webkul\\Rule\\' => array($baseDir . '/packages/Webkul/Rule/src'),
    'Webkul\\Product\\' => array($baseDir . '/packages/Webkul/Product/src'),
    'Webkul\\Paypal\\' => array($baseDir . '/packages/Webkul/Paypal/src'),
    'Webkul\\Payment\\' => array($baseDir . '/packages/Webkul/Payment/src'),
    'Webkul\\Notification\\' => array($baseDir . '/packages/Webkul/Notification/src'),
    'Webkul\\Marketing\\' => array($baseDir . '/packages/Webkul/Marketing/src'),
    'Webkul\\MagicAI\\' => array($baseDir . '/packages/Webkul/MagicAI/src'),
    'Webkul\\Inventory\\' => array($baseDir . '/packages/Webkul/Inventory/src'),
    'Webkul\\Installer\\' => array($baseDir . '/packages/Webkul/Installer/src'),
    'Webkul\\GDPR\\' => array($baseDir . '/packages/Webkul/GDPR/src'),
    'Webkul\\Faker\\' => array($vendorDir . '/bagisto/laravel-datafaker/src'),
    'Webkul\\FPC\\' => array($baseDir . '/packages/Webkul/FPC/src'),
    'Webkul\\DebugBar\\' => array($baseDir . '/packages/Webkul/DebugBar/src'),
    'Webkul\\DataTransfer\\' => array($baseDir . '/packages/Webkul/DataTransfer/src'),
    'Webkul\\DataGrid\\Tests\\' => array($baseDir . '/packages/Webkul/DataGrid/tests'),
    'Webkul\\DataGrid\\' => array($baseDir . '/packages/Webkul/DataGrid/src'),
    'Webkul\\Customer\\' => array($baseDir . '/packages/Webkul/Customer/src'),
    'Webkul\\Core\\Tests\\' => array($baseDir . '/packages/Webkul/Core/tests'),
    'Webkul\\Core\\' => array($baseDir . '/packages/Webkul/Core/src'),
    'Webkul\\Checkout\\' => array($baseDir . '/packages/Webkul/Checkout/src'),
    'Webkul\\Category\\' => array($baseDir . '/packages/Webkul/Category/src'),
    'Webkul\\CatalogRule\\' => array($baseDir . '/packages/Webkul/CatalogRule/src'),
    'Webkul\\CartRule\\' => array($baseDir . '/packages/Webkul/CartRule/src'),
    'Webkul\\CMS\\' => array($baseDir . '/packages/Webkul/CMS/src'),
    'Webkul\\BookingProduct\\' => array($baseDir . '/packages/Webkul/BookingProduct/src'),
    'Webkul\\Attribute\\' => array($baseDir . '/packages/Webkul/Attribute/src'),
    'Webkul\\Admin\\Tests\\' => array($baseDir . '/packages/Webkul/Admin/tests'),
    'Webkul\\Admin\\' => array($baseDir . '/packages/Webkul/Admin/src'),
    'UAParser\\' => array($vendorDir . '/ua-parser/uap-php/src'),
    'Tree\\' => array($vendorDir . '/nicmart/tree/src'),
    'TijsVerkoyen\\CssToInlineStyles\\' => array($vendorDir . '/tijsverkoyen/css-to-inline-styles/src'),
    'Tests\\' => array($baseDir . '/tests'),
    'Termwind\\' => array($vendorDir . '/nunomaduro/termwind/src'),
    'Symfony\\Polyfill\\Uuid\\' => array($vendorDir . '/symfony/polyfill-uuid'),
    'Symfony\\Polyfill\\Php83\\' => array($vendorDir . '/symfony/polyfill-php83'),
    'Symfony\\Polyfill\\Php82\\' => array($vendorDir . '/symfony/polyfill-php82'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Idn\\' => array($vendorDir . '/symfony/polyfill-intl-idn'),
    'Symfony\\Polyfill\\Intl\\Grapheme\\' => array($vendorDir . '/symfony/polyfill-intl-grapheme'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Translation\\' => array($vendorDir . '/symfony/translation-contracts'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Contracts\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher-contracts'),
    'Symfony\\Component\\VarDumper\\' => array($vendorDir . '/symfony/var-dumper'),
    'Symfony\\Component\\Uid\\' => array($vendorDir . '/symfony/uid'),
    'Symfony\\Component\\Translation\\' => array($vendorDir . '/symfony/translation'),
    'Symfony\\Component\\String\\' => array($vendorDir . '/symfony/string'),
    'Symfony\\Component\\Routing\\' => array($vendorDir . '/symfony/routing'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Symfony\\Component\\Mime\\' => array($vendorDir . '/symfony/mime'),
    'Symfony\\Component\\Mailer\\' => array($vendorDir . '/symfony/mailer'),
    'Symfony\\Component\\HttpKernel\\' => array($vendorDir . '/symfony/http-kernel'),
    'Symfony\\Component\\HttpFoundation\\' => array($vendorDir . '/symfony/http-foundation'),
    'Symfony\\Component\\Finder\\' => array($vendorDir . '/symfony/finder'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\ErrorHandler\\' => array($vendorDir . '/symfony/error-handler'),
    'Symfony\\Component\\DomCrawler\\' => array($vendorDir . '/symfony/dom-crawler'),
    'Symfony\\Component\\CssSelector\\' => array($vendorDir . '/symfony/css-selector'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Symfony\\Bridge\\PsrHttpMessage\\' => array($vendorDir . '/symfony/psr-http-message-bridge'),
    'Svg\\' => array($vendorDir . '/phenx/php-svg-lib/src/Svg'),
    'Spatie\\TemporaryDirectory\\' => array($vendorDir . '/spatie/temporary-directory/src'),
    'Spatie\\Sitemap\\' => array($vendorDir . '/spatie/laravel-sitemap/src'),
    'Spatie\\Robots\\' => array($vendorDir . '/spatie/robots-txt/src'),
    'Spatie\\ResponseCache\\' => array($vendorDir . '/spatie/laravel-responsecache/src'),
    'Spatie\\LaravelPackageTools\\' => array($vendorDir . '/spatie/laravel-package-tools/src'),
    'Spatie\\Crawler\\' => array($vendorDir . '/spatie/crawler/src'),
    'Spatie\\Browsershot\\' => array($vendorDir . '/spatie/browsershot/src'),
    'Shetabit\\Visitor\\' => array($vendorDir . '/shetabit/visitor/src'),
    'Sample\\' => array($vendorDir . '/paypal/paypal-checkout-sdk/samples'),
    'Sabberworm\\CSS\\' => array($vendorDir . '/sabberworm/php-css-parser/src'),
    'Ramsey\\Uuid\\' => array($vendorDir . '/ramsey/uuid/src'),
    'Ramsey\\Collection\\' => array($vendorDir . '/ramsey/collection/src'),
    'Pusher\\' => array($vendorDir . '/pusher/pusher-php-server/src'),
    'Psy\\' => array($vendorDir . '/psy/psysh/src'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psr\\Clock\\' => array($vendorDir . '/psr/clock/src'),
    'Psr\\Cache\\' => array($vendorDir . '/psr/cache/src'),
    'Prettus\\Validator\\' => array($vendorDir . '/prettus/laravel-validation/src/Prettus/Validator'),
    'Prettus\\Repository\\' => array($vendorDir . '/prettus/l5-repository/src/Prettus/Repository'),
    'Predis\\' => array($vendorDir . '/predis/predis/src'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'PhpOption\\' => array($vendorDir . '/phpoption/phpoption/src/PhpOption'),
    'PhpOffice\\PhpSpreadsheet\\' => array($vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet'),
    'Pest\\Plugin\\' => array($vendorDir . '/pestphp/pest-plugin/src'),
    'Pest\\Mutate\\' => array($vendorDir . '/pestphp/pest-plugin-mutate/src'),
    'Pest\\Laravel\\' => array($vendorDir . '/pestphp/pest-plugin-laravel/src'),
    'Pest\\Arch\\' => array($vendorDir . '/pestphp/pest-plugin-arch/src'),
    'Pest\\' => array($vendorDir . '/pestphp/pest/src'),
    'PayPalHttp\\' => array($vendorDir . '/paypal/paypalhttp/lib/PayPalHttp'),
    'PayPalCheckoutSdk\\' => array($vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk'),
    'ParagonIE\\ConstantTime\\' => array($vendorDir . '/paragonie/constant_time_encoding/src'),
    'ParaTest\\' => array($vendorDir . '/brianium/paratest/src'),
    'PHPUnit\\Architecture\\' => array($vendorDir . '/ta-tikoma/phpunit-architecture-test/src'),
    'PHPStan\\PhpDocParser\\' => array($vendorDir . '/phpstan/phpdoc-parser/src'),
    'Opis\\Closure\\' => array($vendorDir . '/opis/closure/src'),
    'OpenTelemetry\\Context\\' => array($vendorDir . '/open-telemetry/context'),
    'OpenTelemetry\\API\\' => array($vendorDir . '/open-telemetry/api'),
    'OpenAI\\Laravel\\' => array($vendorDir . '/openai-php/laravel/src'),
    'OpenAI\\' => array($vendorDir . '/openai-php/client/src'),
    'NunoMaduro\\Collision\\' => array($vendorDir . '/nunomaduro/collision/src'),
    'Mpdf\\PsrLogAwareTrait\\' => array($vendorDir . '/mpdf/psr-log-aware-trait/src'),
    'Mpdf\\PsrHttpMessageShim\\' => array($vendorDir . '/mpdf/psr-http-message-shim/src'),
    'Mpdf\\' => array($vendorDir . '/mpdf/mpdf/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'Mockery\\' => array($vendorDir . '/mockery/mockery/library/Mockery'),
    'Matrix\\' => array($vendorDir . '/markbaker/matrix/classes/src'),
    'Masterminds\\' => array($vendorDir . '/masterminds/html5/src'),
    'Maatwebsite\\Excel\\' => array($vendorDir . '/maatwebsite/excel/src'),
    'League\\Uri\\' => array($vendorDir . '/league/uri', $vendorDir . '/league/uri-interfaces'),
    'League\\OAuth1\\Client\\' => array($vendorDir . '/league/oauth1-client/src'),
    'League\\MimeTypeDetection\\' => array($vendorDir . '/league/mime-type-detection/src'),
    'League\\Flysystem\\Local\\' => array($vendorDir . '/league/flysystem-local'),
    'League\\Flysystem\\' => array($vendorDir . '/league/flysystem/src'),
    'League\\Config\\' => array($vendorDir . '/league/config/src'),
    'League\\CommonMark\\' => array($vendorDir . '/league/commonmark/src'),
    'Laravel\\Ui\\' => array($vendorDir . '/laravel/ui/src'),
    'Laravel\\Tinker\\' => array($vendorDir . '/laravel/tinker/src'),
    'Laravel\\Socialite\\' => array($vendorDir . '/laravel/socialite/src'),
    'Laravel\\SerializableClosure\\' => array($vendorDir . '/laravel/serializable-closure/src'),
    'Laravel\\Sanctum\\' => array($vendorDir . '/laravel/sanctum/src'),
    'Laravel\\Prompts\\' => array($vendorDir . '/laravel/prompts/src'),
    'Laravel\\Octane\\' => array($vendorDir . '/laravel/octane/src'),
    'Laminas\\Diactoros\\' => array($vendorDir . '/laminas/laminas-diactoros/src'),
    'Konekt\\Enum\\Eloquent\\' => array($vendorDir . '/konekt/enum-eloquent/src'),
    'Konekt\\Enum\\' => array($vendorDir . '/konekt/enum/src'),
    'Konekt\\Concord\\' => array($vendorDir . '/konekt/concord/src'),
    'Kalnoy\\Nestedset\\' => array($vendorDir . '/kalnoy/nestedset/src'),
    'Jean85\\' => array($vendorDir . '/jean85/pretty-package-versions/src'),
    'Jaybizzle\\CrawlerDetect\\' => array($vendorDir . '/jaybizzle/crawler-detect/src'),
    'Intervention\\Image\\' => array($vendorDir . '/bagisto/image-cache/src/Intervention/Image', $vendorDir . '/intervention/image/src/Intervention/Image'),
    'Illuminate\\Support\\' => array($vendorDir . '/laravel/framework/src/Illuminate/Macroable', $vendorDir . '/laravel/framework/src/Illuminate/Collections', $vendorDir . '/laravel/framework/src/Illuminate/Conditionable'),
    'Illuminate\\Foundation\\Auth\\' => array($vendorDir . '/laravel/ui/auth-backend'),
    'Illuminate\\' => array($vendorDir . '/laravel/framework/src/Illuminate'),
    'Http\\Promise\\' => array($vendorDir . '/php-http/promise/src'),
    'Http\\Message\\MultipartStream\\' => array($vendorDir . '/php-http/multipart-stream-builder/src'),
    'Http\\Discovery\\' => array($vendorDir . '/php-http/discovery/src'),
    'Http\\Client\\' => array($vendorDir . '/php-http/httplug/src'),
    'GuzzleHttp\\UriTemplate\\' => array($vendorDir . '/guzzlehttp/uri-template/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'GrahamCampbell\\ResultType\\' => array($vendorDir . '/graham-campbell/result-type/src'),
    'Fruitcake\\Cors\\' => array($vendorDir . '/fruitcake/php-cors/src'),
    'FontLib\\' => array($vendorDir . '/phenx/php-font-lib/src/FontLib'),
    'Firebase\\JWT\\' => array($vendorDir . '/firebase/php-jwt/src'),
    'Fidry\\CpuCoreCounter\\' => array($vendorDir . '/fidry/cpu-core-counter/src'),
    'Faker\\' => array($vendorDir . '/fakerphp/faker/src/Faker'),
    'Facade\\IgnitionContracts\\' => array($vendorDir . '/facade/ignition-contracts/src'),
    'Elastic\\Transport\\' => array($vendorDir . '/elastic/transport/src'),
    'Elastic\\Elasticsearch\\' => array($vendorDir . '/elasticsearch/elasticsearch/src'),
    'Egulias\\EmailValidator\\' => array($vendorDir . '/egulias/email-validator/src'),
    'Dotenv\\' => array($vendorDir . '/vlucas/phpdotenv/src'),
    'Dompdf\\' => array($vendorDir . '/dompdf/dompdf/src'),
    'Doctrine\\Inflector\\' => array($vendorDir . '/doctrine/inflector/lib/Doctrine/Inflector'),
    'Doctrine\\Deprecations\\' => array($vendorDir . '/doctrine/deprecations/src'),
    'Doctrine\\Common\\Lexer\\' => array($vendorDir . '/doctrine/lexer/src'),
    'Diglactic\\Breadcrumbs\\' => array($vendorDir . '/diglactic/laravel-breadcrumbs/src'),
    'Dflydev\\DotAccessData\\' => array($vendorDir . '/dflydev/dot-access-data/src'),
    'Detection\\' => array($vendorDir . '/mobiledetect/mobiledetectlib/src'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'DebugBar\\' => array($vendorDir . '/php-debugbar/php-debugbar/src/DebugBar'),
    'Database\\Seeders\\' => array($baseDir . '/database/seeders', $vendorDir . '/laravel/pint/database/seeders'),
    'Database\\Factories\\' => array($baseDir . '/database/factories', $vendorDir . '/laravel/pint/database/factories'),
    'Cron\\' => array($vendorDir . '/dragonmantank/cron-expression/src/Cron'),
    'Composer\\Semver\\' => array($vendorDir . '/composer/semver/src'),
    'Composer\\Pcre\\' => array($vendorDir . '/composer/pcre/src'),
    'Composer\\CaBundle\\' => array($vendorDir . '/composer/ca-bundle/src'),
    'Complex\\' => array($vendorDir . '/markbaker/complex/classes/src'),
    'Carbon\\Doctrine\\' => array($vendorDir . '/carbonphp/carbon-doctrine-types/src/Carbon/Doctrine'),
    'Carbon\\' => array($vendorDir . '/nesbot/carbon/src/Carbon'),
    'Brick\\Math\\' => array($vendorDir . '/brick/math/src'),
    'Barryvdh\\DomPDF\\' => array($vendorDir . '/barryvdh/laravel-dompdf/src'),
    'Barryvdh\\Debugbar\\' => array($vendorDir . '/barryvdh/laravel-debugbar/src'),
    'Astrotomic\\Translatable\\' => array($vendorDir . '/astrotomic/laravel-translatable/src/Translatable'),
    'ArPHP\\I18N\\' => array($vendorDir . '/khaled.alshamaa/ar-php/src'),
    'App\\' => array($baseDir . '/app', $vendorDir . '/laravel/pint/app'),
);
