@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
    font-family: "bagisto-admin";
    src: url("../fonts/bagisto-admin.woff?jwnnow") format("woff");
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

@layer components {
    ::selection {
        background-color: rgba(0, 68, 242, .2);
    }

    body {
        @apply bg-gray-50;
    }

    button:disabled {
        @apply cursor-not-allowed opacity-50;
    }

    button:disabled:hover {
        @apply cursor-not-allowed opacity-50;
    }

    .direction-ltr {
        direction: ltr;
    }

    .direction-rtl {
        direction: rtl;
    }

    .draggable-ghost {
        opacity: 0.5;
        background: #e0e7ff;
    }

    [class^="icon-"],
    [class*=" icon-"] {
        /* use !important to prevent issues with browser extensions that change fonts */
        font-family: "bagisto-admin" !important;
        speak: never;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1;
        color: #6b7280;
        
        /* Better Font Rendering =========== */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        line-height: 1 !important;
    }

    html.dark [class^="icon-"],
    html.dark [class*=" icon-"]{
        color: #d1d5db;
    }
    
    .box-shadow {
        @apply border-[1px] dark:border-gray-800 shadow-[0px_0px_0px_0px_rgba(0,0,0,0.03),0px_1px_1px_0px_rgba(0,0,0,0.03),0px_3px_3px_0px_rgba(0,0,0,0.03),0px_6px_4px_0px_rgba(0,0,0,0.02),0px_11px_4px_0px_rgba(0,0,0,0.00),0px_17px_5px_0px_rgba(0,0,0,0.00)];
    }

    .icon-phone:before {
        content: "\e94f";
    }

    .icon-list:before {
        content: "\e950";
    }

    .icon-admin-export:before {
        content: "\e95d";
    }

    .icon-magic:before {
        content: "\e94b";
    }

    .icon-zoom:before {
        content: "\e94c";
    }

    .icon-ar:before {
        content: "\e94a";
    }

    .icon-report:before {
        content: "\e942";
    }

    .icon-refund:before {
        content: "\e948";
    }

    .icon-light:before {
        content: "\e947";
    }

    .icon-dark:before {
        content: "\e949";
    }

    .icon-checkbox-partical:before {
        content: "\e900";
    }

    .icon-uncheckbox:before {
        content: "\e901";
    }

    .icon-add-customer:before {
        content: "\e902";
    }

    .icon-arrow-down:before {
        content: "\e903";
    }

    .icon-arrow-left:before {
        content: "\e904";
    }

    .icon-arrow-right:before {
        content: "\e905";
    }

    .icon-arrow-up:before {
        content: "\e906";
    }

    .icon-attribute-block:before {
        content: "\e907";
    }

    .icon-attribute:before {
        content: "\e908";
    }

    .icon-calendar:before {
        content: "\e909";
    }

    .icon-cancel-1:before {
        content: "\e90a";
    }

    .icon-cancel:before {
        content: "\e90b";
    }

    .icon-cart:before {
        content: "\e90c";
    }

    .icon-checked:before {
        content: "\e90d";
    }

    .icon-cms:before {
        content: "\e90e";
    }

    .icon-configuration:before {
        content: "\e90f";
    }

    .icon-cross:before {
        content: "\e910";
    }

    .icon-customer-2:before {
        content: "\e911";
    }

    .icon-customer:before {
        content: "\e912";
    }

    .icon-dashboard:before {
        content: "\e913";
    }

    .icon-delete:before {
        content: "\e914";
    }

    .icon-done:before {
        content: "\e915";
    }

    .icon-dot:before {
        content: "\e916";
    }

    .icon-dots:before {
        content: "\e917";
    }

    .icon-down-stat:before {
        content: "\e918";
    }

    .icon-drag:before {
        content: "\e919";
    }

    .icon-edit-save:before {
        content: "\e91a";
    }

    .icon-edit:before {
        content: "\e91b";
    }

    .icon-filter:before {
        content: "\e91c";
    }

    .icon-folder-block:before {
        content: "\e91d";
    }

    .icon-folder:before {
        content: "\e91e";
    }

    .icon-image:before {
        content: "\e91f";
    }

    .icon-information:before {
        content: "\e920";
    }

    .icon-language:before {
        content: "\e921";
    }

    .icon-location:before {
        content: "\e922";
    }

    .icon-mail:before {
        content: "\e923";
    }

    .icon-menu:before {
        content: "\e924";
    }

    .icon-notification:before {
        content: "\e925";
    }

    .icon-order-back:before {
        content: "\e926";
    }

    .icon-printer:before {
        content: "\e927";
    }

    .icon-processing:before {
        content: "\e928";
    }

    .icon-product-1:before {
        content: "\e929";
    }

    .icon-product:before {
        content: "\e92a";
    }

    .icon-promotion:before {
        content: "\e92b";
    }

    .icon-radio-normal:before {
        content: "\e92c";
    }

    .icon-radio-selected:before {
        content: "\e92d";
    }

    .icon-repeat:before {
        content: "\e92e";
    }

    .icon-sales:before {
        content: "\e92f";
    }

    .icon-search:before {
        content: "\e930";
    }

    .icon-setting:before {
        content: "\e931";
    }

    .icon-settings:before {
        content: "\e932";
    }

    .icon-ship:before {
        content: "\e933";
    }

    .icon-sort-down:before {
        content: "\e934";
    }

    .icon-sort-left:before {
        content: "\e935";
    }

    .icon-sort-right:before {
        content: "\e936";
    }

    .icon-sort-up-down:before {
        content: "\e937";
    }

    .icon-sort-up:before {
        content: "\e938";
    }

    .icon-star:before {
        content: "\e939";
    }

    .icon-store:before {
        content: "\e93a";
    }

    .icon-tick:before {
        content: "\e93b";
    }

    .icon-up-stat:before {
        content: "\e93c";
    }

    .icon-view:before {
        content: "\e93d";
    }

    .icon-view-close:before {
        content: "\e946";
    }

    .icon-copy:before {
        content: "\e93e";
    }

    .icon-exit:before {
        content: "\e93f";
    }

    .icon-clip:before {
        content: "\e940";
    }

    .icon-collapse:before {
        content: "\e941";
    }

    .icon-login:before {
        content: "\e943";
    }

    .icon-pause:before {
        content: "\e944";
    }

    .icon-play:before {
        content: "\e945";
    }
      
    p {
        @apply text-[14px] !leading-[17px];
    }

    input,
    textarea,
    select {
        @apply outline-none;
    }

    .primary-button {
        @apply bg-blue-600 border border-blue-700 cursor-pointer flex focus:opacity-[0.9] font-semibold gap-x-1 hover:opacity-[0.9] items-center place-content-center px-3 py-1.5 rounded-md text-gray-50 transition-all;
    }

    .secondary-button {
        @apply flex cursor-pointer place-content-center items-center gap-x-1 whitespace-nowrap rounded-md border-2 border-blue-600 bg-white px-3 py-1.5 font-semibold text-blue-600 transition-all hover:bg-[#eff6ff61] focus:bg-[#eff6ff61] dark:border-gray-400 dark:bg-gray-800 dark:text-white dark:hover:opacity-80;
    }

    .transparent-button {
        @apply flex cursor-pointer appearance-none place-content-center items-center gap-x-1 whitespace-nowrap rounded-md border-2 border-transparent px-3 py-1.5 font-semibold text-gray-600 transition-all marker:shadow hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-950;
    }

    .journal-scroll::-webkit-scrollbar {
        width: 14px;
        cursor: pointer;
        display: none;
    }

    .journal-scroll::-webkit-scrollbar-track {
        background-color: #fff;
        cursor: pointer;
        border-radius: 12px;
        border: 1px solid #e9e9e9;
    }

    .journal-scroll::-webkit-scrollbar-thumb {
        cursor: pointer;
        background-color: #e9e9e9;
        border-radius: 12px;
        border: 3px solid transparent;
        background-clip: content-box;
    }

    .custom-select {
        -webkit-appearance: none;
        -moz-appearance: none;
        background: transparent;
        background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
        background-repeat: no-repeat;
        background-position-x: calc(100% - 10px);
        background-position-y: 50%;
    }
    .dark .custom-select{
        background-image: url("data:image/svg+xml;utf8,<svg fill='white' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
    }

    [dir="rtl"] .custom-select {
        background-position-x: calc(100% - (100% - 10px));
    }

    .label-pending,
    .label-processing,
    .label-closed,
    .label-canceled,
    .label-info,
    .label-fraud,
    .label-pending_payment,
    .label-completed,
    .label-active {
        @apply text-[12px] text-white font-semibold py-px px-1.5 max-w-max rounded-[35px];
    }

	.label-pending,
    .label-pending_payment {
		@apply bg-yellow-500;
	}

	.label-processing{
		@apply bg-cyan-600;
    }

	.label-completed,
    .label-active {
		@apply bg-green-600;
	}

	.label-closed {
		@apply bg-indigo-600;
	}

    .label-canceled,
    .label-fraud {
        @apply bg-rose-600;
    }

    .label-info {
        @apply bg-slate-400;
    }

    /* status */
    .status-enable {
        @apply text-green-600;
    }

    .status-disable {
        @apply text-red-600;
    }

	.icon-star-fill:before {
        content: "\e938";
        color: #ffb600;
    }

    .shimmer {
        animation-duration: 2.2s;
        animation-fill-mode: forwards;
        animation-iteration-count: infinite;
        animation-name: skeleton;
        animation-timing-function: linear;
        background: linear-gradient(
            to right,
            #f6f6f6 8%,
            #f0f0f0 18%,
            #f6f6f6 33%
        );
        background-size: 1250px 100%;
    }

    .dark .shimmer {
        background: linear-gradient(
            to right,
            #1f2937 8%,
            #1a2232 18%,
            #1f2937 33%
        );
    }

    @keyframes skeleton {
        0% {
            @apply bg-[-1250px_0];
        }

        100% {
            @apply bg-[1250px_0];
        }
    }

    .required:after {
        @apply content-['*'];
    }

    .CodeMirror {
        @apply !h-[calc(100vh-367px)]
    }
}

.tox .tox-toolbar__group:last-child button {
    padding: 6px 8px;
    background: #eff6ff;
    color: #2563EB;
}

.tox .tox-toolbar__group:last-child button:hover {
    background: #dbeafe;
}


.tox .tox-toolbar__group:last-child button[aria-disabled="true"] {
    @apply cursor-not-allowed opacity-50;
}