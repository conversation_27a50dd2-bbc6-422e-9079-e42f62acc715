{"__meta": {"id": "01K2E8PWJ9ZXHR26ZD5Y3GEXH3", "datetime": "2025-08-12 10:00:41", "utime": **********.22692, "method": "GET", "uri": "/onlinestore/bagisto-2.3/public/cache/small/product/2/BURS21o4NmmkrUhP2sebQrdigeA8sVS5xNapyIVS.webp", "ip": "::1"}, "modules": {"count": 1, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "duration": 3.84, "duration_str": "3.84s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.cache_small_image.width'", "duration": 2.25, "duration_str": "2.25s", "connection": "Online_store"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.cache_small_image.height'", "duration": 4.78, "duration_str": "4.78s", "connection": "Online_store"}]}]}, "messages": {"count": 1, "messages": [{"message": "[10:00:41] LOG.warning: Creation of dynamic property Intervention\\Image\\Image::$cachekey is deprecated in D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\bagisto\\image-cache\\src\\Intervention\\Image\\ImageCache.php on line 275", "message_html": null, "is_string": false, "label": "warning", "time": **********.208882, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754973039.999642, "end": **********.271338, "duration": 1.2716960906982422, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1754973039.999642, "relative_start": 0, "end": **********.825914, "relative_end": **********.825914, "duration": 0.****************, "duration_str": "826ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.825959, "relative_start": 0.****************, "end": **********.271343, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "445ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.870834, "relative_start": 0.***************, "end": **********.885037, "relative_end": **********.885037, "duration": 0.*****************, "duration_str": "14.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.221494, "relative_start": 1.****************, "end": **********.221823, "relative_end": **********.221823, "duration": 0.00032901763916015625, "duration_str": "329μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.221863, "relative_start": 1.****************, "end": **********.221933, "relative_end": **********.221933, "duration": 6.985664367675781e-05, "duration_str": "70μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.010870000000000001, "accumulated_duration_str": "10.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('localhost', 'http://localhost', 'https://localhost')", "type": "query", "params": [], "bindings": ["localhost", "http://localhost", "https://localhost"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 169}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\Core.php", "line": 246}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 218}], "start": **********.087855, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "Online_store", "explain": null, "start_percent": 0, "width_percent": 35.327}, {"sql": "select * from `core_config` where `code` = 'catalog.products.cache_small_image.width'", "type": "query", "params": [], "bindings": ["catalog.products.cache_small_image.width"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.1043081, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 35.327, "width_percent": 20.699}, {"sql": "select * from `core_config` where `code` = 'catalog.products.cache_small_image.height'", "type": "query", "params": [], "bindings": ["catalog.products.cache_small_image.height"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.13144, "duration": 0.00478, "duration_str": "4.78ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\xampp\\htdocs\\onlinestore\\bagisto-2.3\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "Online_store", "explain": null, "start_percent": 56.026, "width_percent": 43.974}]}, "models": {"data": {"Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/cache/small/product/2/BURS21o4NmmkrUhP2sebQrdigeA8sV...", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fonlinestore%2Fbagisto-2.3%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "1.27s", "peak_memory": "40MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2105000578 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2105000578\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1803113744 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1803113744\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1363525488 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">http://localhost/onlinestore/bagisto-2.3/public/arctic-bliss-stylish-winter-scarf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"730 characters\">dark_mode=0; XSRF-TOKEN=eyJpdiI6Ikg1aWZnNVI3clJ2TjMvMWIzcXdoN3c9PSIsInZhbHVlIjoiN0MzRHVoTHdOK2Y3WW12UEZoNGVucmhpdUIzRjRlVmoxVytCTThpbFdWNW81c0hjNVR6ZnQ0R2FDR2FOVFdTNTVwclEzNXNCeHNMclp2dVlIMzVJOW9zTlppaktHTmhYV2RvcEhnaDFScFVySERSczRCaUw4RFN1Mi9IcWltNHkiLCJtYWMiOiIxZmNmN2ExNmRmZjBlZmVjZWIyNzhjMzQ4OGIxODE4NDExMzk2OWNkMDhmMjcxOWRiYzhjNDgxYzgyNDIwZDQ1IiwidGFnIjoiIn0%3D; onlinestore_session=eyJpdiI6ImpjRGdYRWJic1FGY2ZmR2Nmd3Jhc0E9PSIsInZhbHVlIjoiQjU5b3BHQkovZGE2WmZ5SjhrVUVWV2crYW1UK3JMZHQvVDZLenZDbkRQdTFlUm5nVlo5UWVFQ3VhS3JrdVZTazJHZE5sM2thVzBKYnN4UEtRVVE4T3p2NGtTdi8zR3hob2pZSUVzMnJjblFkdnRmRjhnY1hsUWFLeWJFYXRVR3oiLCJtYWMiOiJjZGUwZTZlOWYzMWYwNzJjYjAwZjE4NTgzY2YwODY4NzY1NWEyYWRmNmQ4Njk4ODI5MDdlMjBkODMzMGRhY2NhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363525488\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1064531273 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ikg1aWZnNVI3clJ2TjMvMWIzcXdoN3c9PSIsInZhbHVlIjoiN0MzRHVoTHdOK2Y3WW12UEZoNGVucmhpdUIzRjRlVmoxVytCTThpbFdWNW81c0hjNVR6ZnQ0R2FDR2FOVFdTNTVwclEzNXNCeHNMclp2dVlIMzVJOW9zTlppaktHTmhYV2RvcEhnaDFScFVySERSczRCaUw4RFN1Mi9IcWltNHkiLCJtYWMiOiIxZmNmN2ExNmRmZjBlZmVjZWIyNzhjMzQ4OGIxODE4NDExMzk2OWNkMDhmMjcxOWRiYzhjNDgxYzgyNDIwZDQ1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>onlinestore_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImpjRGdYRWJic1FGY2ZmR2Nmd3Jhc0E9PSIsInZhbHVlIjoiQjU5b3BHQkovZGE2WmZ5SjhrVUVWV2crYW1UK3JMZHQvVDZLenZDbkRQdTFlUm5nVlo5UWVFQ3VhS3JrdVZTazJHZE5sM2thVzBKYnN4UEtRVVE4T3p2NGtTdi8zR3hob2pZSUVzMnJjblFkdnRmRjhnY1hsUWFLeWJFYXRVR3oiLCJtYWMiOiJjZGUwZTZlOWYzMWYwNzJjYjAwZjE4NTgzY2YwODY4NzY1NWEyYWRmNmQ4Njk4ODI5MDdlMjBkODMzMGRhY2NhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064531273\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-966628483 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3264</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">3b0bc4651faafc85ff3f0c1a33eb0c35</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 04:30:41 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966628483\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1110693266 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1110693266\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/onlinestore/bagisto-2.3/public/cache/small/product/2/BURS21o4NmmkrUhP2sebQrdigeA8sV...", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}